# StreamOnPod Production Environment Configuration
# Optimized for production with working login

# Application Environment
NODE_ENV=production
PORT=7575

# Logging Configuration (Optimized for production but with essential logging)
LOG_LEVEL=warn
ENABLE_FILE_LOGGING=true
ENABLE_CONSOLE_LOGGING=true
ENABLE_VERBOSE_FFMPEG_LOGS=false

# Performance Optimizations
ENABLE_COMPRESSION=true
ENABLE_CACHING=true
CACHE_TTL=3600

# Security Settings (Production-ready)
SESSION_SECRET=72f28a9c8453bba0638deecdc565c2edce354ae507b79a35ff1e9dd2bb168b87
CSRF_SECRET=d3d9b1894d2de6ad8de68b9249351f7dd2864229f73a12b89412a12d5d994f72

# Database Configuration
DATABASE_PATH=./db/streamonpod.db
ENABLE_DATABASE_LOGGING=false

# FFmpeg Configuration
FFMPEG_TIMEOUT=300000
FFMPEG_MAX_RETRIES=3
ENABLE_FFMPEG_HARDWARE_ACCELERATION=true

# Load Balancer Configuration
LOAD_BALANCER_ENABLED=true
LOAD_BALANCER_CHECK_INTERVAL=30000
CPU_THRESHOLD_HIGH=85
CPU_THRESHOLD_MEDIUM=75
CPU_THRESHOLD_LOW=60

# File Upload Limits
MAX_FILE_SIZE=2147483648
MAX_STORAGE_GB=50

# Notification Settings
NOTIFICATION_CLEANUP_INTERVAL=86400000
MAX_NOTIFICATIONS_PER_USER=100

# System Monitoring
ENABLE_PERFORMANCE_MONITORING=true
SYSTEM_STATS_INTERVAL=60000
MEMORY_THRESHOLD=90
CPU_THRESHOLD=85

# Cleanup and Maintenance
LOG_ROTATION_SIZE=10485760
LOG_RETENTION_DAYS=30
TEMP_FILE_CLEANUP_INTERVAL=3600000

# Midtrans Payment Gateway Configuration
MIDTRANS_SERVER_KEY=SB-Mid-server-L-oAVvIRyXTVVY65L4qCRRLr
MIDTRANS_CLIENT_KEY=SB-Mid-client-Gea0ZBqekgqxBYSf
MIDTRANS_IS_PRODUCTION=false
MIDTRANS_MERCHANT_ID=G463893303

# Base URL for callbacks and webhooks
BASE_URL=https://streamonpod.imthe.one

# Additional Midtrans Settings
MIDTRANS_WEBHOOK_TIMEOUT=30000
MIDTRANS_ENABLE_SIGNATURE_VERIFICATION=true
MIDTRANS_ENABLE_TRANSACTION_LOGGING=true

# Production Session Configuration
# Note: For local production testing, we need to handle HTTPS properly
FORCE_HTTPS=false
TRUST_PROXY=false
