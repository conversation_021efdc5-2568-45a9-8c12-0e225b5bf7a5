# Bandwidth Troubleshooting - StreamOnPod

## 🚨 **KEMUN<PERSON><PERSON><PERSON><PERSON> PENYEBAB BITRATE RENDAH**

### **1. VPS Upload Bandwidth Terbatas**
- **Masalah**: VPS upload speed < 12 Mbps
- **Akibat**: FFmpeg tidak bisa maintain 10000 kbps
- **Solusi**: Lower bitrate atau upgrade VPS

### **2. Koneksi VPS ke YouTube Tidak Stabil**
- **Masalah**: High latency/packet loss ke YouTube servers
- **Akibat**: YouTube auto-adjust bitrate ke bawah
- **Solusi**: Test dengan server RTMP berbeda

### **3. VPS Provider Throttling**
- **Masalah**: Bandwidth dibatasi saat usage tinggi
- **Akibat**: Bitrate drop saat streaming
- **Solusi**: Konfirmasi dengan provider

## 🔧 **TROUBLESHOOTING STEPS**

### **Step 1: Test dengan Bitrate Rendah**
```
1. Set bitrate ke 6800 kbps di dashboard
2. Start streaming
3. Monitor YouTube Studio selama 5 menit
4. Catat bitrate yang diterima YouTube
```

### **Step 2: Cek <PERSON>i VPS**
```bash
# Test ping ke YouTube
ping -c 10 youtube.com

# Test traceroute
traceroute youtube.com

# Check bandwidth usage
iftop -i eth0
```

### **Step 3: Test Berbagai Bitrate**
| Bitrate Setting | Upload Required | Test Result |
|----------------|-----------------|-------------|
| 4000 kbps      | 5.5 Mbps       | ✅ Test first |
| 6800 kbps      | 9.0 Mbps       | ⚠️ YouTube minimum |
| 8000 kbps      | 11.0 Mbps      | ⚠️ May fail |
| 10000 kbps     | 12.0 Mbps      | ❌ Likely fail |

## 📊 **DIAGNOSTIC COMMANDS**

### **Check Current Bandwidth Usage:**
```bash
# Real-time bandwidth monitoring
sudo iftop -i eth0

# Network statistics
cat /proc/net/dev

# Check if bandwidth is throttled
sudo wondershaper -s eth0
```

### **Test Upload Speed:**
```bash
# Install speedtest
sudo apt install speedtest-cli

# Run speed test
speedtest-cli --simple

# Test specific server
speedtest-cli --server 1234
```

### **Monitor FFmpeg Output:**
```bash
# Check FFmpeg logs for bandwidth errors
tail -f /var/log/streamonpod/ffmpeg.log | grep -i "bitrate\|bandwidth\|connection"
```

## 🎯 **RECOMMENDED ACTIONS**

### **Immediate (Test Now):**
1. **Lower bitrate to 6800 kbps**
2. **Test streaming for 10 minutes**
3. **Check YouTube Studio bitrate**
4. **If stable, gradually increase**

### **Short Term:**
1. **Contact VPS provider:**
   - Ask about upload bandwidth limits
   - Check for throttling policies
   - Confirm network location

2. **Test different times:**
   - Peak hours vs off-peak
   - Different days of week
   - Monitor consistency

### **Long Term:**
1. **Consider VPS upgrade:**
   - Higher bandwidth plan
   - Dedicated bandwidth
   - Better network location

2. **Alternative solutions:**
   - CDN for streaming
   - Multiple VPS load balancing
   - Different VPS provider

## 🔍 **VPS PROVIDER QUESTIONS**

Ask your VPS provider:

1. **"What is the guaranteed upload bandwidth?"**
   - Not shared/burst speed
   - Sustained upload rate

2. **"Are there bandwidth limits or throttling?"**
   - Daily/monthly caps
   - Speed throttling after usage

3. **"What is the network route to YouTube servers?"**
   - Latency to Singapure/US
   - Peering agreements

4. **"Can you provide dedicated bandwidth?"**
   - Cost for guaranteed speed
   - SLA for network performance

## 📈 **EXPECTED RESULTS**

### **If VPS bandwidth is sufficient (>12 Mbps):**
- ✅ 10000 kbps setting should work
- ✅ YouTube receives full bitrate
- ✅ Warning message disappears

### **If VPS bandwidth is limited (<12 Mbps):**
- ❌ 10000 kbps will drop to actual bandwidth
- ⚠️ Need to lower bitrate setting
- 💡 6800 kbps may be maximum achievable

## 🎉 **SUCCESS INDICATORS**

Monitor these in YouTube Studio:
- **Bitrate matches your setting** (±10%)
- **Stable bitrate** (no frequent drops)
- **No buffering warnings**
- **Good stream health score**

## 🚀 **NEXT STEPS**

1. **Test with 6800 kbps now**
2. **Monitor for 10 minutes**
3. **Report results**
4. **Adjust based on findings**

Remember: **Better to have stable 6800 kbps than unstable 10000 kbps!**
