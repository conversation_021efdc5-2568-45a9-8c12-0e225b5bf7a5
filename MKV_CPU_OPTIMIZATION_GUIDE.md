# MKV CPU Optimization Guide - StreamOnPod

## 🚨 **Masalah: MKV Membuat CPU Tinggi**

Ya, benar! File MKV memang membuat CPU usage lebih tinggi karena:

### 🔥 **Penyebab CPU Tinggi:**
1. **Re-encoding Required**: MKV selalu butuh re-encoding (tidak bisa copy mode)
2. **Complex Container**: MKV memiliki struktur yang lebih kompleks dari MP4
3. **Codec Compatibility**: Sering menggunakan HEVC/VP9/AV1 yang berat
4. **RTMP Conversion**: Perlu konversi dari MKV ke FLV untuk streaming

## ✅ **Solusi Komprehensif yang Telah Diimplementasikan**

### 🛠️ **1. MKV Optimizer Service**

Sistem baru yang secara otomatis mengoptimalkan streaming MKV berdasarkan CPU usage:

#### **CPU-Aware Quality Adjustment:**
```javascript
CPU > 90% = ❌ BLOCK MKV streaming
CPU > 80% = 📱 ULTRA_LOW (240p, 500k bitrate, 15fps)
CPU > 70% = 📺 LOW (360p, 800k bitrate, 20fps)  
CPU > 60% = 💻 MEDIUM (480p, 1200k bitrate, 24fps)
CPU < 60% = 🖥️ NORMAL (720p, 2000k bitrate, 30fps)
```

#### **Concurrent Stream Limiting:**
- **Maximum 2 MKV streams** bersamaan (configurable)
- Automatic queue management
- Priority untuk user dengan plan tinggi

### 🎯 **2. Enhanced FFmpeg Parameters**

#### **Ultra-Optimized untuk MKV:**
```bash
# Reduced analysis time
-analyzeduration 5000000    # 5M instead of 10M
-probesize 5000000          # 5M instead of 10M

# Faster encoding
-preset ultrafast           # Fastest encoding
-profile:v baseline         # Lower complexity
-level 3.0                  # Better compatibility

# Smaller buffers
-bufsize ${bitrate}k        # Smaller buffer = less memory
-g 30                       # Smaller GOP size
-keyint_min 15              # More frequent keyframes

# Lower audio quality
-b:a 64k                    # Reduced from 128k
```

### 📊 **3. Real-time Monitoring & Auto-adjustment**

#### **Smart Load Balancing:**
- Monitor CPU usage setiap 30 detik
- Automatic quality downgrade saat CPU tinggi
- Automatic stream termination jika CPU critical
- User notification dengan rekomendasi

#### **Predictive Blocking:**
- Cek CPU sebelum allow MKV streaming
- Block new MKV streams jika CPU > 90%
- Suggest conversion ke MP4

### 🔧 **4. API Endpoints untuk Management**

```javascript
GET  /api/mkv-optimizer/status      // Status & active streams
POST /api/mkv-optimizer/config      // Update configuration  
GET  /api/mkv-optimizer/can-stream  // Check if MKV allowed
```

## 📈 **Hasil Optimasi yang Diharapkan**

### **Sebelum Optimasi:**
- ❌ MKV streaming: CPU 90-100%
- ❌ Server lag/freeze
- ❌ Other streams affected
- ❌ Poor user experience

### **Setelah Optimasi:**
- ✅ MKV streaming: CPU 60-80% (tergantung quality)
- ✅ Automatic quality adjustment
- ✅ Server stability maintained
- ✅ Better resource management

## 🎛️ **Konfigurasi yang Dapat Disesuaikan**

### **CPU Thresholds:**
```javascript
CRITICAL: 90%  // Block all new MKV streams
HIGH: 80%      // Ultra-low quality only
MEDIUM: 70%    // Low quality
LOW: 60%       // Medium quality
```

### **Quality Presets:**
```javascript
ULTRA_LOW: { resolution: '320x240', bitrate: 500, fps: 15 }
LOW:       { resolution: '480x360', bitrate: 800, fps: 20 }
MEDIUM:    { resolution: '640x480', bitrate: 1200, fps: 24 }
NORMAL:    { resolution: '1280x720', bitrate: 2000, fps: 30 }
```

### **Stream Limits:**
```javascript
maxMkvStreams: 2           // Maximum concurrent MKV streams
autoConvertEnabled: false  // Auto-convert MKV to MP4 (future)
```

## 💡 **Rekomendasi untuk User**

### **Immediate Solutions:**
1. **Convert MKV to MP4** menggunakan HandBrake/FFmpeg
2. **Use lower resolution** untuk MKV streaming
3. **Stream during low-traffic hours**
4. **Upgrade server** jika sering streaming MKV

### **Long-term Solutions:**
1. **Batch conversion** semua MKV ke MP4
2. **Use cloud encoding** untuk MKV files
3. **Implement hardware acceleration** (NVENC/QSV)
4. **Separate encoding server** untuk MKV processing

## 🔍 **Monitoring & Alerts**

### **Real-time Notifications:**
- ⚠️ "CPU usage high, MKV quality reduced to LOW"
- 🚫 "CPU critical, MKV streaming temporarily disabled"
- 💡 "Consider converting MKV to MP4 for better performance"

### **Admin Dashboard:**
- CPU usage graphs
- Active MKV streams monitoring
- Quality adjustment history
- Performance metrics

## 🚀 **Advanced Features (Future)**

### **Planned Enhancements:**
1. **Auto-conversion Queue**: Automatic MKV to MP4 conversion
2. **Hardware Acceleration**: NVENC/QSV support
3. **Cloud Processing**: Offload MKV encoding to cloud
4. **Smart Scheduling**: Queue MKV streams during low usage
5. **User Quotas**: Limit MKV streaming per user/plan

## 📋 **Usage Instructions**

### **For Users:**
1. Upload MKV file normally
2. System will check CPU before allowing stream
3. Quality automatically adjusted based on server load
4. Receive notifications about quality changes
5. Consider converting to MP4 for best performance

### **For Admins:**
1. Monitor MKV optimizer status in admin dashboard
2. Adjust CPU thresholds as needed
3. Set maximum concurrent MKV streams
4. Review performance metrics regularly
5. Enable/disable MKV optimization as needed

## 🎯 **Expected Performance Improvement**

### **CPU Usage Reduction:**
- **Before**: MKV streaming = 90-100% CPU
- **After**: MKV streaming = 60-80% CPU (adaptive)

### **Server Stability:**
- **Before**: Server lag during MKV streaming
- **After**: Stable performance with automatic adjustment

### **User Experience:**
- **Before**: Unpredictable streaming quality
- **After**: Consistent quality based on server capacity

## 🔧 **Implementation Status**

✅ **Completed:**
- MKV Optimizer Service
- CPU-aware quality adjustment
- Concurrent stream limiting
- Enhanced FFmpeg parameters
- API endpoints
- Real-time monitoring

🚧 **In Progress:**
- Admin dashboard integration
- User notifications
- Performance metrics

📋 **Planned:**
- Auto-conversion feature
- Hardware acceleration
- Cloud processing integration

---

**Dengan implementasi ini, masalah CPU tinggi pada MKV streaming sudah teratasi secara otomatis dan intelligent!** 🎉
