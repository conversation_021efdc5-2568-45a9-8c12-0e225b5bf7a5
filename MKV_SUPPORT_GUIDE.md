# MKV Support Guide - StreamOnPod

## 🎬 Overview

StreamOnPod sekarang mendukung file **MKV (Matroska Video)** untuk upload dan streaming! Dukungan ini mencakup upload manual dan import dari Google Drive.

## ✅ **Masalah yang Diperbaiki**

### 🔧 **Sebelum Perbaikan:**
- ❌ File .mkv ditolak saat upload manual
- ❌ File .mkv dari Google Drive bisa masuk tapi gagal streaming
- ❌ FFmpeg tidak dioptimalkan untuk container MKV
- ❌ Codec HEVC/H.265 dalam MKV menyebabkan error

### ✅ **Setelah Perbaikan:**
- ✅ File .mkv diterima di upload manual dan Google Drive
- ✅ Deteksi format MKV yang akurat
- ✅ FFmpeg dioptimalkan khusus untuk MKV
- ✅ Re-encoding otomatis untuk kompatibilitas RTMP
- ✅ Dukungan codec HEVC, VP9, AV1 dalam MKV

## 🛠️ **<PERSON><PERSON><PERSON>**

### 1. **Upload Filter Enhancement**
```javascript
// File: app.js
fileFilter: function (req, file, cb) {
  // Support more video formats including MKV
  if (!file.mimetype.match(/^video\/(mp4|avi|quicktime|x-msvideo|x-matroska)$/)) {
    return cb(new Error('Only MP4, AVI, MOV, and MKV video files are allowed!'), false);
  }
  cb(null, true);
}
```

### 2. **Format Detection**
```javascript
// File: app.js
if (mimetype === 'video/mp4') format = 'mp4';
else if (mimetype === 'video/avi') format = 'avi';
else if (mimetype === 'video/quicktime') format = 'mov';
else if (mimetype === 'video/x-matroska') format = 'mkv';
else if (originalname.toLowerCase().endsWith('.mkv')) format = 'mkv';
```

### 3. **Google Drive Import**
```javascript
// File: utils/googleDriveService.js
const supportedVideoTypes = [
  'video/mp4',
  'video/avi', 
  'video/quicktime',
  'video/x-msvideo',
  'video/x-matroska', // MKV files
  'application/octet-stream' // Some MKV files might have this mimetype
];

const isVideoFile = fileMetadata.data.mimeType.includes('video') || 
                   supportedVideoTypes.includes(fileMetadata.data.mimeType) ||
                   fileMetadata.data.name.toLowerCase().match(/\.(mp4|avi|mov|mkv)$/);
```

### 4. **Streaming Service Optimization**
```javascript
// File: services/streamingService.js

// Enhanced codec detection
if (video.codec) {
  const codecLower = video.codec.toLowerCase();
  if (codecLower.includes('hevc') || codecLower.includes('h265') || 
      codecLower.includes('vp9') || codecLower.includes('av1')) {
    return true; // Force re-encoding
  }
}

// MKV container detection
if (video.format && video.format.toLowerCase() === 'mkv') {
  return true; // Force re-encoding for RTMP compatibility
}
```

### 5. **FFmpeg MKV Optimization**
```javascript
// Enhanced re-encoding for MKV files
const isMKV = video.format && video.format.toLowerCase() === 'mkv';
const extraOptions = isMKV ? [
  '-analyzeduration', '10000000',  // Analyze more data for MKV
  '-probesize', '10000000',        // Probe more data for MKV
  '-fflags', '+genpts+discardcorrupt+igndts'  // Enhanced flags for MKV
] : [
  '-fflags', '+genpts+discardcorrupt'
];
```

## 🎯 **Cara Kerja MKV Support**

### **Upload Manual:**
1. User memilih file .mkv
2. Sistem memvalidasi mimetype `video/x-matroska`
3. File diterima dan disimpan ke gallery
4. Format dideteksi sebagai 'mkv'

### **Google Drive Import:**
1. User paste link Google Drive file .mkv
2. Sistem cek mimetype dan nama file
3. Download file dengan progress tracking
4. Format dideteksi dan disimpan

### **Streaming Process:**
1. User pilih video .mkv untuk streaming
2. Sistem deteksi format = 'mkv'
3. **Otomatis force re-encoding** (tidak copy mode)
4. FFmpeg menggunakan parameter khusus MKV:
   - `-analyzeduration 10000000`
   - `-probesize 10000000` 
   - `-fflags +genpts+discardcorrupt+igndts`
5. Re-encode ke H.264 untuk kompatibilitas RTMP

## ⚡ **Performance Considerations**

### **Re-encoding Required:**
- ✅ MKV files **selalu** menggunakan re-encoding mode
- ✅ Lebih CPU intensive daripada copy mode
- ✅ Memastikan kompatibilitas RTMP streaming
- ✅ Mengatasi masalah codec HEVC/VP9/AV1

### **Optimizations Applied:**
- ✅ `preset ultrafast` untuk encoding cepat
- ✅ `tune zerolatency` untuk streaming real-time
- ✅ Enhanced buffer management
- ✅ Proper timestamp handling

## 🧪 **Testing Results**

```
📊 MKV Support Test Results:
✅ MKV File Upload Filter: PASS
✅ Google Drive MKV Import: PASS  
✅ Streaming Service MKV Handling: PASS
✅ FFmpeg MKV Optimization: PASS
✅ Video Format Detection: PASS

Success Rate: 100%
```

## 💡 **Recommendations**

### **For Best Performance:**
1. **Convert MKV to MP4** jika memungkinkan untuk performa optimal
2. **Monitor CPU usage** saat streaming MKV files
3. **Use lower bitrate** untuk MKV streaming jika CPU terbatas
4. **Test stream** sebelum go-live dengan file MKV

### **Supported MKV Codecs:**
- ✅ **H.264/AVC** - Re-encoded for compatibility
- ✅ **HEVC/H.265** - Re-encoded to H.264
- ✅ **VP9** - Re-encoded to H.264  
- ✅ **AV1** - Re-encoded to H.264
- ✅ **Audio**: AAC, MP3, Vorbis, Opus

### **File Size Considerations:**
- MKV files cenderung lebih besar dari MP4
- Re-encoding akan menggunakan lebih banyak bandwidth
- Monitor storage space untuk file MKV

## 🔧 **Troubleshooting**

### **Jika Stream MKV Masih Error:**

1. **Check FFmpeg Support:**
   ```bash
   ffmpeg -formats | grep matroska
   ffmpeg -codecs | grep -E "(h264|hevc|vp9|av1)"
   ```

2. **Check Stream Logs:**
   - Lihat log FFmpeg untuk error spesifik
   - Monitor CPU usage selama streaming
   - Check RTMP connection stability

3. **Alternative Solutions:**
   - Convert MKV to MP4 menggunakan HandBrake
   - Use lower resolution/bitrate untuk MKV
   - Test dengan file MKV yang lebih kecil

## 📈 **Next Steps**

Dengan dukungan MKV yang lengkap, StreamOnPod sekarang dapat:
- ✅ Handle berbagai format video populer
- ✅ Import dari Google Drive dengan format apapun
- ✅ Stream dengan kompatibilitas RTMP yang baik
- ✅ Provide user experience yang konsisten

**File .mkv dari Google Drive sekarang seharusnya bisa streaming dengan normal!** 🎉
