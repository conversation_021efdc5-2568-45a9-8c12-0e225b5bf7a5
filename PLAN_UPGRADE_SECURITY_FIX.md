# Plan Upgrade Security & Spam Protection Fix

## Masalah yang Diperbaiki

### 1. Plan Hierarchy Validation
**Masalah**: User bisa membeli plan yang lebih rendah dari plan saat ini (downgrade berbayar)
**Solusi**: Menambahkan validasi di backend dan frontend untuk mencegah downgrade ke plan berbayar yang lebih rendah

### 2. Spam Protection
**Masalah**: User bisa klik berulang kali tombol upgrade dan membuat multiple transaksi pending di Midtrans
**Solusi**: Implementasi spam protection dengan button state management dan cooldown

### 3. Duplicate Transaction Prevention
**Masalah**: Tidak ada validasi untuk transaksi pending yang sudah ada
**Solusi**: Validasi transaksi pending sebelum membuat transaksi baru

## Perubahan yang Dibuat

### Backend Changes

#### 1. routes/subscription.js
- **Plan Hierarchy Validation**: Mencegah downgrade ke plan berbayar yang lebih rendah
- **Same Plan Prevention**: Mencegah subscribe ke plan yang sama
- **Pending Transaction Check**: Validasi transaksi pending sebelum membuat baru
- **Automatic Cleanup**: Membersihkan transaksi expired sebelum validasi

```javascript
// Prevent downgrade to paid plans
if (plan.price > 0 && currentPlan.price > 0 && plan.price < currentPlan.price) {
  return res.status(400).json({ 
    error: 'Tidak dapat downgrade ke plan berbayar yang lebih rendah...'
  });
}

// Check for existing pending transactions
const hasPendingTransaction = await Transaction.hasPendingTransaction(req.session.userId, planId);
if (hasPendingTransaction) {
  return res.status(400).json({ 
    error: 'Anda sudah memiliki transaksi pending untuk plan ini...'
  });
}
```

#### 2. models/Transaction.js
- **cleanupExpiredTransactions()**: Membersihkan transaksi pending yang expired (>24 jam)
- **hasPendingTransaction()**: Mengecek apakah user memiliki transaksi pending untuk plan tertentu

```javascript
static async cleanupExpiredTransactions() {
  // Update status pending menjadi expired untuk transaksi >24 jam
}

static async hasPendingTransaction(userId, planId) {
  // Check transaksi pending yang masih valid (<24 jam)
}
```

#### 3. services/midtrans.js
- **Custom Expiry**: Setting Midtrans transaction expiry ke 24 jam
- **Expiry Logging**: Log waktu expiry untuk monitoring

```javascript
custom_expiry: {
  expiry_duration: 1440,
  unit: "minute"
}
```

#### 4. app.js
- **Scheduled Cleanup**: Menjalankan cleanup transaksi expired setiap 5 menit
- **Initial Cleanup**: Menjalankan cleanup saat aplikasi start

```javascript
// Cleanup expired pending transactions every 5 minutes
setInterval(async () => {
  await Transaction.cleanupExpiredTransactions();
}, 5 * 60 * 1000);
```

### Frontend Changes

#### 1. views/subscription/plans.ejs
- **Button State Management**: Tracking state button untuk mencegah spam clicking
- **Plan Hierarchy Display**: Menampilkan button "Tidak Tersedia" untuk downgrade berbayar
- **Enhanced Error Handling**: Restore button state pada semua kondisi error

```javascript
// Track button states to prevent spam clicking
const buttonStates = new Map();

// Check if button is already processing
if (buttonStates.get(planId)) {
  return; // Ignore subsequent clicks
}

// Set button state to processing
buttonStates.set(planId, true);
```

#### 2. Button Logic Enhancement
- **Visual Feedback**: Opacity dan cursor changes untuk disabled state
- **State Cleanup**: Proper cleanup pada semua kondisi (success, error, close)
- **Payment Protection**: Separate state tracking untuk payment process

```javascript
// Enhanced button state management
buttonElement.style.opacity = '0.6';
buttonElement.style.cursor = 'not-allowed';

// Cleanup on all conditions
buttonStates.delete(planId);
buttonStates.delete(planId + '_payment');
```

## Fitur Keamanan

### 1. Plan Hierarchy Protection
- User dengan plan Rp. 49,900 tidak bisa beli plan Rp. 24,900
- User dengan plan Rp. 99,900 tidak bisa beli plan yang lebih rendah
- Hanya upgrade atau same-price plan yang diizinkan

### 2. Transaction Spam Protection
- Button disabled setelah diklik pertama kali
- State tracking mencegah multiple API calls
- Visual feedback untuk user (opacity, cursor)
- Automatic state cleanup pada semua kondisi

### 3. Pending Transaction Management
- Validasi transaksi pending sebelum membuat baru
- Automatic cleanup transaksi expired (>10 menit)
- Scheduled cleanup setiap 5 menit
- Informative error messages

## Error Messages

### Indonesian Messages
- "Tidak dapat downgrade ke plan berbayar yang lebih rendah"
- "Anda sudah menggunakan plan ini"
- "Anda sudah memiliki transaksi pending untuk plan ini"
- "Silakan selesaikan pembayaran yang ada atau tunggu hingga transaksi expired (10 menit)"

### Button States
- "Processing..." - Saat sedang memproses
- "Tidak Tersedia" - Untuk downgrade berbayar
- "Current Plan" - Untuk plan saat ini

## Testing Scenarios

### 1. Plan Hierarchy Test
1. User dengan PodFlow (Rp. 49,900) coba beli PodLite (Rp. 24,900) → Blocked
2. User dengan PodPrime (Rp. 99,900) coba beli PodFlow → Blocked
3. User dengan PodLite coba beli PodFlow → Allowed

### 2. Spam Protection Test
1. Klik tombol upgrade berulang kali dengan cepat → Hanya 1 request
2. Klik tombol saat sedang processing → Ignored
3. Error terjadi → Button state restored

### 3. Pending Transaction Test
1. User dengan transaksi pending coba beli plan sama → Blocked
2. Transaksi expired (>10 menit) → Automatic cleanup
3. Multiple pending transactions → Proper validation

## Monitoring & Logs

### Console Logs
- Transaction cleanup results
- Plan validation failures
- Spam protection triggers
- Pending transaction blocks

### Database Tracking
- Transaction status changes (pending → expired)
- Plan upgrade attempts
- Error occurrences

## Deployment Notes

### Database Migration
Tidak ada perubahan schema database yang diperlukan.

### Environment Variables
Tidak ada environment variables baru yang diperlukan.

### Backward Compatibility
Semua perubahan backward compatible dengan sistem existing.

## Sinkronisasi Midtrans & Sistem Internal

### Timeline Expiry
- **Midtrans**: Transaksi expired setelah 24 jam
- **Sistem Internal**: Cleanup transaksi expired setiap 5 menit
- **Sinkronisasi**: Kedua sistem menggunakan waktu yang sama (24 jam)

### Monitoring Expiry
```javascript
console.log(`⏰ Setting Midtrans transaction expiry: 24 hours (${expiryTimeString})`);
console.log(`🧹 Cleaned up ${this.changes} expired pending transactions`);
```

### Webhook Handling
- Midtrans akan mengirim notification saat transaksi expired
- Sistem internal akan update status transaksi menjadi 'expired'
- User bisa langsung retry setelah transaksi expired

## Fitur Continue Payment

### Problem Solved
Ketika user mendapat error "transaksi pending", sekarang mereka bisa melanjutkan pembayaran yang sudah dibuat tanpa perlu menunggu expired.

### Implementation

#### Backend API
- **POST /payment/continue/:orderId**: Melanjutkan pembayaran existing
- **GET /payment/pending**: Mendapatkan daftar transaksi pending user
- **Enhanced error response**: Menyertakan detail transaksi pending

#### Frontend UX
- **Confirmation Dialog**: User ditanya apakah ingin melanjutkan pembayaran
- **Automatic Snap Reopen**: Membuka kembali Snap popup dengan token yang sama
- **Expiry Information**: Menampilkan sisa waktu sebelum expired

#### User Flow
1. **User klik upgrade** → Sistem deteksi transaksi pending
2. **Loading state** muncul: "Melanjutkan Pembayaran..."
3. **Snap popup terbuka** langsung dengan pembayaran existing
4. **User melanjutkan pembayaran** yang tertunda

### Code Examples

#### Error Response with Continue Option
```javascript
{
  error: "Anda sudah memiliki transaksi pending...",
  has_pending: true,
  pending_transaction: {
    order_id: "SOP-12345678-ABCD1234-1234567890-ABC123",
    plan_name: "PodFlow",
    amount_formatted: "Rp49.900",
    created_at: "2024-01-01T10:00:00.000Z"
  }
}
```

#### Continue Payment Function
```javascript
async function continueExistingPayment(orderId, button) {
  const response = await fetch(`/payment/continue/${orderId}`, {
    method: 'POST'
  });

  if (result.success && result.snap_token) {
    snap.pay(result.snap_token, { /* callbacks */ });
  }
}
```

## Custom Processing Dialog

### Problem Solved
Alert dengan tombol OK dapat ditutup user sebelum auto-refresh, menyebabkan user bisa pindah halaman dan miss refresh.

### Solution
Custom dialog tanpa tombol OK yang tidak bisa ditutup user, dengan loading spinner dan auto-refresh.

### Features
- **No OK Button**: User tidak bisa menutup dialog
- **Loading Spinner**: Visual feedback yang menarik
- **Auto-refresh Message**: Jelas bahwa halaman akan refresh otomatis
- **Overlay Protection**: Klik di luar dialog tidak menutup dialog
- **Brand Colors**: Menggunakan warna brand #ad6610

### Implementation
```javascript
function showProcessingDialog(message) {
  // Custom dialog dengan spinner
  // Tidak bisa ditutup user
  // Auto-refresh setelah delay
}
```

### Usage
- **onSuccess**: "Pembayaran berhasil! Menunggu konfirmasi sistem..."
- **onPending**: "Pembayaran sedang diproses. Silakan tunggu konfirmasi sistem..."

## Future Improvements

1. **Rate Limiting**: Implementasi rate limiting per user
2. **Transaction Timeout**: Konfigurasi timeout yang dapat diatur
3. **Admin Override**: Kemampuan admin untuk override restrictions
4. **Audit Trail**: Logging semua plan change attempts
5. **Email Notifications**: Notifikasi email untuk transaksi pending
6. **Real-time Expiry**: WebSocket notification saat transaksi expired
7. **Payment History Page**: Halaman khusus untuk melihat semua transaksi
8. **Auto-retry Payment**: Otomatis retry pembayaran yang gagal
9. **Progress Bar**: Progress bar untuk countdown auto-refresh
