# Timezone Schedule Validation Fix

## Problem
User reported that schedule validation was failing with error "Schedule time must be at least 1 minute in the future" even when scheduling 2 minutes ahead.

## Root Cause
**Mismatch between frontend and backend timezone conversion logic:**

- **Backend**: Used proper timezone conversion via `datetimeLocalToUTC()` function
- **Frontend**: Used incorrect timezone offset calculation that subtracted timezone offset instead of properly handling system timezone

## Example of the Problem
For input `2025-06-02T07:19` with timezone `Asia/Jakarta`:
- **Backend produced**: `2025-06-02T00:19:00.000Z` ✅ (Correct)
- **Frontend produced**: `2025-06-01T17:19:00.000Z` ❌ (Wrong - 7 hours off)

This caused frontend validation to fail while backend would accept the same input.

## Solution

### 1. Fixed Frontend Timezone Logic
Updated `validateScheduleTime()` function in `views/dashboard.ejs`:

```javascript
// OLD (Incorrect)
const offsetMinutes = timezoneOffsets[scheduleTimezone] || 0;
scheduleTimeUTC = new Date(inputDate.getTime() - (offsetMinutes * 60 * 1000));

// NEW (Correct)
const systemTimezone = 'Asia/Jakarta'; // Server timezone
if (scheduleTimezone === systemTimezone) {
  scheduleTimeUTC = inputDate;
} else {
  const systemOffset = timezoneOffsets[systemTimezone] || 7;
  const targetOffset = timezoneOffsets[scheduleTimezone] || 7;
  const offsetDiff = targetOffset - systemOffset;
  scheduleTimeUTC = new Date(inputDate.getTime() - (offsetDiff * 60 * 60 * 1000));
}
```

### 2. Added Debug Logging
Added comprehensive logging in both frontend and backend:

**Backend** (`app.js`):
```javascript
console.log(`[Schedule Validation] Input: ${req.body.scheduleTime} (${timezone})`);
console.log(`[Schedule Validation] Converted UTC: ${scheduleTimeUTC}`);
console.log(`[Schedule Validation] Time difference: ${timeDiff} seconds`);
```

**Frontend** (`dashboard.ejs`):
```javascript
console.log(`[Frontend Schedule Validation] Input: ${scheduleTime} (${scheduleTimezone})`);
console.log(`[Frontend Schedule Validation] Schedule UTC: ${scheduleTimeUTC.toISOString()}`);
console.log(`[Frontend Schedule Validation] Time difference: ${timeDiff} seconds`);
```

## Verification Results

### All Indonesian Timezones Tested:
- **Asia/Jakarta (UTC+7)**: ✅ Frontend/Backend match
- **Asia/Makassar (UTC+8)**: ✅ Frontend/Backend match  
- **Asia/Jayapura (UTC+9)**: ✅ Frontend/Backend match

### Before Fix:
```
Backend UTC: 2025-06-02T00:19:00.000Z
Frontend UTC: 2025-06-01T17:19:00.000Z  ❌ MISMATCH
```

### After Fix:
```
Backend UTC: 2025-06-02T00:19:00.000Z
Frontend UTC: 2025-06-02T00:19:00.000Z  ✅ MATCH
```

## Impact
- ✅ Schedule validation now works correctly for all Indonesian timezones
- ✅ Frontend and backend validation are synchronized
- ✅ Users can successfully schedule streams 2+ minutes in the future
- ✅ Debug logging available for future troubleshooting

## Files Modified
1. `views/dashboard.ejs` - Fixed frontend timezone conversion logic
2. `app.js` - Added debug logging for schedule validation
3. `test-timezone-fix.js` - Created comprehensive test suite

## Testing
Run `node test-timezone-fix.js` to verify the fix works for all timezones.
