# Video Upload & Deletion Bug Fixes

## Problems Description

### Bug 1: Google Drive Import - Orphaned WebP Thumbnails
When uploading videos from Google Drive, the system generates both JPG and WebP thumbnails, but only stores the JPG path in the database. When the video is deleted, the WebP thumbnail remains on the server.

### Bug 2: Upload Quota Exceeded - Orphaned Files
When uploading videos that exceed storage quota, the file is already uploaded to disk before the quota check happens. If quota is exceeded, the file remains on the server even though it's not saved to the database.

### Bug 3: Original Video Deletion Issue
Users reported that when deleting videos from the gallery, sometimes the thumbnail and video files would remain on the server even though the video was removed from the database and UI.

## Root Cause Analysis

### Bug 1: Google Drive Import WebP Thumbnails
- **Root Cause**: `utils/videoProcessor.js` generates both JPG and WebP thumbnails during Google Drive import
- **Issue**: Only JPG path is stored in database, WebP thumbnails become orphaned when video is deleted
- **Impact**: Accumulation of orphaned WebP files consuming disk space

### Bug 2: Upload Quota Exceeded
- **Root Cause**: Multer processes and saves files to disk BEFORE quota validation occurs
- **Issue**: When quota is exceeded, files remain on disk but aren't saved to database
- **Impact**: Disk space consumed by files that aren't tracked in the system

### Bug 3: Original Video Deletion Issue
The issue was caused by **duplicate file deletion logic** in two different places:

1. **API Route** (`app.js` lines 2061-2070): Attempted to delete video and thumbnail files
2. **Model Method** (`models/Video.js` lines 109-128): Also attempted to delete the same files

### Issues with the Original Implementation:

1. **Race Conditions**: Both the API route and model were trying to delete the same files simultaneously
2. **Inconsistent Error Handling**: If one deletion succeeded but the other failed, files could be left orphaned
3. **Parameter Mismatch**: API was calling `Video.delete(videoId, userId)` but model only accepted `Video.delete(id)`
4. **Poor Error Reporting**: Failures in file deletion weren't properly reported back to the frontend

## Solutions Implemented

### 1. Enhanced Video Deletion for WebP Thumbnails

**Enhanced `Video.delete()` method:**
- Now checks for and deletes both JPG and WebP thumbnails
- Automatically detects WebP versions of JPG thumbnails
- Handles missing files gracefully with warnings instead of errors

### 2. Upload Quota File Cleanup

**Enhanced quota middleware:**
- Added file cleanup when quota is exceeded
- Removes uploaded files immediately if quota validation fails
- Prevents orphaned files from quota-exceeded uploads

**Enhanced upload error handling:**
- Added cleanup for all upload error scenarios
- Removes files on database errors, thumbnail generation failures, and general errors
- Comprehensive error recovery with file cleanup

### 3. Consolidated File Deletion Logic (Original Bug)

**Before:**
- API route deleted files, then called model
- Model also tried to delete the same files

**After:**
- API route only handles authorization and storage updates
- Model handles ALL file operations in a single, atomic operation

### 2. Improved Error Handling

**Enhanced `Video.delete()` method:**
- Returns structured success/error responses
- Handles authorization within the model
- Provides detailed error messages
- Continues with database deletion even if file deletion fails
- Reports warnings for partial failures

### 3. Better File Management

**New features:**
- Verifies file existence before deletion attempts
- Logs all file operations for debugging
- Handles missing files gracefully (warns instead of failing)
- Provides detailed success/error feedback

### 4. Added Debugging Tools

**New utilities:**
- `Video.verifyFiles(id)` - Check if video files exist on disk
- `/api/videos/:id/verify-files` - API endpoint for file verification
- `scripts/cleanup-orphaned-files.js` - Script to find and remove orphaned files
- `test-video-deletion.js` - Test script to verify the fix

## Files Modified

### 1. `app.js` (lines 2046-2078)
- Removed duplicate file deletion logic from API route
- Added proper error handling for model responses
- Added debug endpoint for file verification

### 2. `models/Video.js` (lines 97-205)
- Enhanced `delete()` method with better error handling
- Added optional `userId` parameter for authorization
- Added `verifyFiles()` utility method
- Improved logging and error reporting

### 3. New Files Created
- `scripts/cleanup-orphaned-files.js` - Cleanup utility
- `test-video-deletion.js` - Testing utility
- `VIDEO_DELETION_BUG_FIX.md` - This documentation

## How to Use the Fix

### 1. Test the Current State
```bash
# Check for existing orphaned files
node scripts/cleanup-orphaned-files.js

# Test video deletion functionality
node test-video-deletion.js
```

### 2. Clean Up Existing Orphaned Files
```bash
# Dry run (shows what would be deleted)
node scripts/cleanup-orphaned-files.js

# Actually delete orphaned files
node scripts/cleanup-orphaned-files.js --execute
```

### 3. Verify Individual Videos
```bash
# Use the API endpoint to check specific videos
curl -X GET "http://localhost:7575/api/videos/VIDEO_ID/verify-files" \
  -H "Cookie: your-session-cookie"
```

## Prevention Measures

### 1. Atomic Operations
- Database deletion happens first
- File deletion happens after database success
- Partial failures are handled gracefully

### 2. Better Logging
- All file operations are logged with emojis for easy identification
- Warnings for missing files (instead of errors)
- Success confirmations for completed deletions

### 3. Structured Error Responses
```javascript
// Success response
{ success: true, id: "123", message: "Video and all files deleted successfully" }

// Partial success (database clean, some files remain)
{ 
  success: true, 
  id: "123", 
  warnings: ["Failed to delete thumbnail: Permission denied"],
  message: "Video deleted but some files could not be removed"
}

// Failure response
{ success: false, error: "Database deletion failed: constraint violation" }
```

## Testing the Fix

### Manual Testing Steps:
1. Upload a video through the gallery
2. Verify both video and thumbnail files exist on disk
3. Delete the video through the gallery UI
4. Verify both files are removed from disk
5. Confirm video is removed from database
6. Check that no orphaned files remain

### Automated Testing:
```bash
# Run the test script
node test-video-deletion.js

# Check for orphaned files
node scripts/cleanup-orphaned-files.js
```

## Monitoring

### Log Messages to Watch For:
- `✅ Deleted video file: /path/to/file` - Successful deletion
- `⚠️ Video file not found (already deleted?): /path/to/file` - File already gone
- `❌ Error deleting video file: error message` - Deletion failed
- `🗑️ Deleting video: Title, Size: X.XXXgb` - Deletion started

### Health Check:
Run the cleanup script periodically to identify any orphaned files that might accumulate over time.

## Future Improvements

1. **Transaction Support**: Implement database transactions for atomic operations
2. **Background Cleanup**: Scheduled cleanup of orphaned files
3. **File Integrity Checks**: Regular verification of file-database consistency
4. **Soft Deletion**: Mark files as deleted before physical removal
5. **Audit Trail**: Log all deletion operations for compliance
