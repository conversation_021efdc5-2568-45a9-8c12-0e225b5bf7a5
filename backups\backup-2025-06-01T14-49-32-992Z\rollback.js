#!/usr/bin/env node
// Rollback script generated on 2025-06-01T14:49:33.062Z
const fs = require('fs');
const path = require('path');

console.log('🔄 Rolling back StreamOnPod to previous version...');

const filesToRestore = [
  "app.js",
  "package.json",
  "services/streamingService.js",
  "models/Video.js",
  "models/Stream.js",
  "routes/admin.js",
  "middleware/quotaMiddleware.js"
];

filesToRestore.forEach(file => {
  const backupPath = path.join(__dirname, file);
  if (fs.existsSync(backupPath)) {
    try {
      const content = fs.readFileSync(backupPath, 'utf8');
      fs.writeFileSync(path.join('../../', file), content);
      console.log(`✅ Restored ${file}`);
    } catch (error) {
      console.log(`❌ Failed to restore ${file}: ${error.message}`);
    }
  }
});

console.log('🎉 Rollback completed. Please restart the application.');
