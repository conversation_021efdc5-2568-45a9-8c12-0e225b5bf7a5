const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const ffmpegInstaller = require('@ffmpeg-installer/ffmpeg');
const schedulerService = require('./schedulerService');
const { v4: uuidv4 } = require('uuid');
const { db } = require('../db/database');
let ffmpegPath;
if (fs.existsSync('/usr/bin/ffmpeg')) {
  ffmpegPath = '/usr/bin/ffmpeg';
  console.log('Using system FFmpeg at:', ffmpegPath);
} else {
  ffmpegPath = ffmpegInstaller.path;
  console.log('Using bundled FFmpeg at:', ffmpegPath);
}
const Stream = require('../models/Stream');
const Video = require('../models/Video');
const notificationService = require('./notificationService');
const {
  createStreamingError,
  createNotFoundError,
  createValidationError,
  logError,
  retryOperation
} = require('../utils/errorHandler');
const activeStreams = new Map();
const streamLogs = new Map();
const streamRetryCount = new Map();
const MAX_RETRY_ATTEMPTS = 3;
const manuallyStoppingStreams = new Set();
const failedStreams = new Set(); // Blacklist for streams that consistently fail
const streamFailureTimestamps = new Map(); // Track failure timestamps
const MAX_LOG_LINES = 100;

// Memory management constants
const CLEANUP_INTERVAL = 60 * 60 * 1000; // 1 hour
const DATA_RETENTION_TIME = 24 * 60 * 60 * 1000; // 24 hours
const MAX_FAILURE_HISTORY = 50; // Maximum failure records per stream

// Auto-stop configuration
const AUTO_STOP_CONFIG = {
  MAX_CONSECUTIVE_FAILURES: 5,        // Max consecutive failures before auto-stop
  FAILURE_WINDOW_MINUTES: 5,          // Time window to count failures
  I_O_ERROR_THRESHOLD: 3,             // Max I/O errors before immediate stop
  CONNECTION_ERROR_THRESHOLD: 3       // Max connection errors before immediate stop
};

// Load balancing configuration
const LOAD_BALANCE_CONFIG = {
  CPU_CHECK_INTERVAL: 10000,          // Check CPU every 10 seconds
  QUALITY_CHANGE_COOLDOWN: 30000,     // Wait 30s between quality changes
  CPU_THRESHOLDS: {
    HIGH: 85,      // CPU > 85% = Minimal quality
    MEDIUM: 75,    // CPU 75-85% = Low quality
    LOW: 60        // CPU 60-75% = Medium quality
                   // CPU < 60% = Normal quality
  },
  QUALITY_PRESETS: {
    MINIMAL: { resolution: '360x240', bitrate: 800, fps: 24, preset: 'ultrafast' },
    LOW: { resolution: '480x360', bitrate: 1500, fps: 30, preset: 'ultrafast' },
    MEDIUM: { resolution: '720x480', bitrate: 2500, fps: 30, preset: 'ultrafast' },
    NORMAL: { resolution: '1280x720', bitrate: 4000, fps: 30, preset: 'veryfast' }
  }
};

// Load balancing state
let currentCpuUsage = 0;
let currentQualityLevel = 'NORMAL';
let lastQualityChange = 0;
let loadBalancingEnabled = true;
let qualityChangeHistory = new Map(); // streamId -> last change time

// Load balancing functions
function determineQualityLevel(cpuUsage) {
  if (cpuUsage >= LOAD_BALANCE_CONFIG.CPU_THRESHOLDS.HIGH) {
    return 'MINIMAL';
  } else if (cpuUsage >= LOAD_BALANCE_CONFIG.CPU_THRESHOLDS.MEDIUM) {
    return 'LOW';
  } else if (cpuUsage >= LOAD_BALANCE_CONFIG.CPU_THRESHOLDS.LOW) {
    return 'MEDIUM';
  } else {
    return 'NORMAL';
  }
}

function getQualityPreset(qualityLevel) {
  return LOAD_BALANCE_CONFIG.QUALITY_PRESETS[qualityLevel] || LOAD_BALANCE_CONFIG.QUALITY_PRESETS.NORMAL;
}

function shouldChangeQuality(newQualityLevel) {
  const now = Date.now();
  const timeSinceLastChange = now - lastQualityChange;

  return (
    loadBalancingEnabled &&
    newQualityLevel !== currentQualityLevel &&
    timeSinceLastChange >= LOAD_BALANCE_CONFIG.QUALITY_CHANGE_COOLDOWN
  );
}

async function applyLoadBalancing(cpuUsage) {
  try {
    currentCpuUsage = cpuUsage;
    const newQualityLevel = determineQualityLevel(cpuUsage);

    if (shouldChangeQuality(newQualityLevel)) {
      console.log(`[LoadBalancer] CPU usage: ${cpuUsage}% - Changing quality from ${currentQualityLevel} to ${newQualityLevel}`);

      const activeStreamIds = Array.from(activeStreams.keys());
      if (activeStreamIds.length > 0) {
        await changeAllStreamsQuality(newQualityLevel);
        currentQualityLevel = newQualityLevel;
        lastQualityChange = Date.now();

        // Log quality change
        addLoadBalanceLog(`Quality changed to ${newQualityLevel} due to CPU usage: ${cpuUsage}%`);
      }
    }
  } catch (error) {
    console.error('[LoadBalancer] Error applying load balancing:', error);
  }
}

async function changeAllStreamsQuality(qualityLevel) {
  const activeStreamIds = Array.from(activeStreams.keys());
  const qualityPreset = getQualityPreset(qualityLevel);

  console.log(`[LoadBalancer] Applying ${qualityLevel} quality to ${activeStreamIds.length} active streams`);

  // Batch load all streams at once to prevent N+1 query problem
  const streams = await Stream.findByIds(activeStreamIds);
  const streamMap = new Map(streams.map(s => [s.id, s]));

  for (const streamId of activeStreamIds) {
    try {
      const now = Date.now();
      const lastChange = qualityChangeHistory.get(streamId) || 0;

      // Prevent too frequent changes per stream
      if (now - lastChange < LOAD_BALANCE_CONFIG.QUALITY_CHANGE_COOLDOWN) {
        continue;
      }

      const stream = streamMap.get(streamId);
      if (stream && stream.use_advanced_settings) {
        // Only apply load balancing to streams with advanced settings
        await restartStreamWithQuality(streamId, qualityPreset);
        qualityChangeHistory.set(streamId, now);

        addStreamLog(streamId, `[LoadBalancer] Quality changed to ${qualityLevel} (CPU: ${currentCpuUsage}%)`);
      }
    } catch (error) {
      console.error(`[LoadBalancer] Error changing quality for stream ${streamId}:`, error);
    }
  }
}

async function restartStreamWithQuality(streamId, qualityPreset) {
  try {
    // Get current stream info
    const stream = await Stream.findById(streamId);
    if (!stream) return;

    // Stop current stream
    await stopStream(streamId);

    // Wait a moment for cleanup
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Update stream with new quality settings
    await Stream.update(streamId, {
      resolution: qualityPreset.resolution,
      bitrate: qualityPreset.bitrate,
      fps: qualityPreset.fps
    });

    // Restart with new settings
    const result = await startStream(streamId);
    if (result.success) {
      console.log(`[LoadBalancer] Stream ${streamId} restarted with ${qualityPreset.resolution} quality`);
    } else {
      console.error(`[LoadBalancer] Failed to restart stream ${streamId}:`, result.error);
    }
  } catch (error) {
    console.error(`[LoadBalancer] Error restarting stream ${streamId}:`, error);
  }
}

// Load balance logging with production optimization
const loadBalanceLogs = [];
const isProduction = process.env.NODE_ENV === 'production';

function addLoadBalanceLog(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] ${message}`;

  loadBalanceLogs.push(logEntry);
  if (loadBalanceLogs.length > MAX_LOG_LINES) {
    loadBalanceLogs.shift();
  }

  // In production, only log warnings and errors
  if (!isProduction || level === 'warn' || level === 'error') {
    if (level === 'error') {
      console.error(`[LoadBalancer] ${message}`);
    } else if (level === 'warn') {
      console.warn(`[LoadBalancer] ${message}`);
    } else {
      console.info(`[LoadBalancer] ${message}`);
    }
  }
}

function getLoadBalanceStatus() {
  return {
    enabled: loadBalancingEnabled,
    currentCpuUsage,
    currentQualityLevel,
    lastQualityChange: new Date(lastQualityChange).toISOString(),
    activeStreams: activeStreams.size,
    thresholds: LOAD_BALANCE_CONFIG.CPU_THRESHOLDS,
    qualityPresets: LOAD_BALANCE_CONFIG.QUALITY_PRESETS,
    recentLogs: loadBalanceLogs.slice(-10)
  };
}

// Function to clean up orphaned streams after server restart
async function restoreActiveStreams() {
  try {
    console.log('[StreamingService] Cleaning up orphaned streams...');
    const liveStreams = await Stream.findAll(null, 'live');

    if (liveStreams && liveStreams.length > 0) {
      console.log(`[StreamingService] Found ${liveStreams.length} streams marked as 'live' in database`);
      console.log('[StreamingService] Marking all as offline since server restarted (streams cannot survive restart)');

      for (const stream of liveStreams) {
        try {
          console.log(`[StreamingService] Marking stream ${stream.id} as offline (server restart cleanup)`);
          await Stream.updateStatus(stream.id, 'offline');
        } catch (error) {
          console.error(`[StreamingService] Error marking stream ${stream.id} as offline:`, error);
        }
      }
    } else {
      console.log('[StreamingService] No orphaned streams found to clean up');
    }
  } catch (error) {
    console.error('[StreamingService] Error during stream cleanup:', error);
  }
}

// Helper function to detect if video needs re-encoding
function needsReencoding(video, targetResolution, targetBitrate, targetFps) {
  if (!video.resolution || !video.bitrate || !video.fps) {
    return true; // Re-encode if we don't have metadata
  }

  // Check codec compatibility - HEVC videos need re-encoding for streaming
  if (video.codec && (video.codec.toLowerCase().includes('hevc') || video.codec.toLowerCase().includes('h265'))) {
    console.log(`[StreamingService] Video uses HEVC codec, re-encoding required for streaming compatibility`);
    return true;
  }

  const [currentWidth, currentHeight] = video.resolution.split('x').map(Number);
  const [targetWidth, targetHeight] = targetResolution.split('x').map(Number);

  // Check if current video exceeds target parameters significantly
  const bitrateExceeds = video.bitrate > (targetBitrate * 1.5);
  const resolutionExceeds = (currentWidth > targetWidth * 1.2) || (currentHeight > targetHeight * 1.2);
  const fpsExceeds = video.fps > (targetFps * 1.2);

  return bitrateExceeds || resolutionExceeds || fpsExceeds;
}

// Helper function to detect available hardware acceleration
function getHardwareAcceleration() {
  // For now, we'll use software encoding for maximum compatibility
  // In the future, this can be enhanced to detect NVENC, QSV, etc.
  return 'none';
}

function addStreamLog(streamId, message) {
  if (!streamLogs.has(streamId)) {
    streamLogs.set(streamId, []);
  }
  const logs = streamLogs.get(streamId);
  logs.push({
    timestamp: new Date().toISOString(),
    message
  });
  if (logs.length > MAX_LOG_LINES) {
    logs.shift();
  }
}

// Memory management functions
function cleanupStreamData(streamId) {
  console.log(`[MemoryCleanup] Cleaning up data for stream ${streamId}`);

  // Remove from all tracking maps and sets
  activeStreams.delete(streamId);
  streamLogs.delete(streamId);
  streamRetryCount.delete(streamId);
  qualityChangeHistory.delete(streamId);
  streamFailureTimestamps.delete(streamId);
  manuallyStoppingStreams.delete(streamId);
  failedStreams.delete(streamId);

  console.log(`[MemoryCleanup] Cleaned up data for stream ${streamId}`);
}

function performPeriodicCleanup() {
  const now = Date.now();
  let cleanedItems = 0;

  console.log('[MemoryCleanup] Starting periodic cleanup...');

  // Clean old quality change history
  for (const [streamId, timestamp] of qualityChangeHistory.entries()) {
    if (now - timestamp > DATA_RETENTION_TIME) {
      qualityChangeHistory.delete(streamId);
      cleanedItems++;
    }
  }

  // Clean old failure timestamps and limit history size
  for (const [streamId, failures] of streamFailureTimestamps.entries()) {
    // Remove old failures
    const recentFailures = failures.filter(f => now - f.timestamp < DATA_RETENTION_TIME);

    // Limit history size
    if (recentFailures.length > MAX_FAILURE_HISTORY) {
      recentFailures.splice(0, recentFailures.length - MAX_FAILURE_HISTORY);
    }

    if (recentFailures.length === 0) {
      streamFailureTimestamps.delete(streamId);
      cleanedItems++;
    } else if (recentFailures.length !== failures.length) {
      streamFailureTimestamps.set(streamId, recentFailures);
      cleanedItems++;
    }
  }

  // Clean old stream logs for inactive streams
  for (const [streamId, logs] of streamLogs.entries()) {
    if (!activeStreams.has(streamId)) {
      // Check if the last log entry is old
      const lastLog = logs[logs.length - 1];
      if (lastLog && now - new Date(lastLog.timestamp).getTime() > DATA_RETENTION_TIME) {
        streamLogs.delete(streamId);
        cleanedItems++;
      }
    }
  }

  // Clean orphaned retry counts
  for (const streamId of streamRetryCount.keys()) {
    if (!activeStreams.has(streamId)) {
      streamRetryCount.delete(streamId);
      cleanedItems++;
    }
  }

  console.log(`[MemoryCleanup] Periodic cleanup completed. Cleaned ${cleanedItems} items.`);
  console.log(`[MemoryCleanup] Current memory usage: activeStreams=${activeStreams.size}, streamLogs=${streamLogs.size}, qualityChangeHistory=${qualityChangeHistory.size}, failureTimestamps=${streamFailureTimestamps.size}`);
}
async function buildFFmpegArgs(stream) {
  const video = await Video.findById(stream.video_id);
  if (!video) {
    throw new Error(`Video record not found in database for video_id: ${stream.video_id}`);
  }
  const relativeVideoPath = video.filepath.startsWith('/') ? video.filepath.substring(1) : video.filepath;
  const projectRoot = path.resolve(__dirname, '..');
  const videoPath = path.join(projectRoot, 'public', relativeVideoPath);
  if (!fs.existsSync(videoPath)) {
    console.error(`[StreamingService] CRITICAL: Video file not found on disk.`);
    console.error(`[StreamingService] Checked path: ${videoPath}`);
    console.error(`[StreamingService] stream.video_id: ${stream.video_id}`);
    console.error(`[StreamingService] video.filepath (from DB): ${video.filepath}`);
    console.error(`[StreamingService] Calculated relativeVideoPath: ${relativeVideoPath}`);
    console.error(`[StreamingService] process.cwd(): ${process.cwd()}`);
    throw new Error('Video file not found on disk. Please check paths and file existence.');
  }
  const rtmpUrl = `${stream.rtmp_url.replace(/\/$/, '')}/${stream.stream_key}`;
  const loopOption = stream.loop_video ? '-stream_loop' : '-stream_loop 0';
  const loopValue = stream.loop_video ? '-1' : '0';

  // Optimized basic copy mode - most efficient for streaming
  if (!stream.use_advanced_settings) {
    // Check if video codec is compatible with copy mode
    const isHEVC = video.codec && (video.codec.toLowerCase().includes('hevc') || video.codec.toLowerCase().includes('h265'));

    if (isHEVC) {
      // Force re-encoding for HEVC videos
      console.log(`[StreamingService] HEVC video detected, using re-encoding mode for stream ${stream.id}`);
      const resolution = '1280x720';
      const bitrate = 2500;
      const fps = 30;

      return [
        '-hwaccel', getHardwareAcceleration(),
        '-loglevel', 'error',
        '-re',
        '-fflags', '+genpts+discardcorrupt',
        '-avoid_negative_ts', 'make_zero',
        loopOption, loopValue,
        '-i', videoPath,
        '-c:v', 'libx264',
        '-preset', 'ultrafast',
        '-tune', 'zerolatency',
        '-b:v', `${bitrate}k`,
        '-maxrate', `${bitrate * 1.2}k`,
        '-bufsize', `${bitrate * 1.5}k`,
        '-pix_fmt', 'yuv420p',
        '-g', '60',
        '-keyint_min', '60',
        '-sc_threshold', '0',
        '-s', resolution,
        '-r', fps.toString(),
        '-c:a', 'aac',
        '-b:a', '128k',
        '-ar', '44100',
        '-ac', '2',
        '-f', 'flv',
        '-flvflags', 'no_duration_filesize',
        rtmpUrl
      ];
    }

    // Check if video codec is compatible with copy mode
    const videoCodec = video.video_codec || 'unknown';

    if (videoCodec.toLowerCase().includes('h264') || videoCodec.toLowerCase().includes('avc')) {
      // Standard copy mode for H.264 videos
      console.log(`[StreamingService] Using copy mode for H.264 stream ${stream.id}`);
      return [
        '-hwaccel', getHardwareAcceleration(),
        '-loglevel', 'error',
        '-re',
        '-fflags', '+genpts+discardcorrupt',
        '-avoid_negative_ts', 'make_zero',
        loopOption, loopValue,
        '-i', videoPath,
        '-c:v', 'copy',
        '-c:a', 'copy',
        '-bsf:v', 'h264_mp4toannexb',
        '-f', 'flv',
        '-flvflags', 'no_duration_filesize',
        rtmpUrl
      ];
    } else {
      // Force re-encoding for non-H.264 codecs to ensure compatibility
      console.log(`[StreamingService] Video codec ${videoCodec} not compatible with copy mode, using re-encoding for stream ${stream.id}`);
      const resolution = stream.resolution || '1280x720';
      const bitrate = stream.bitrate || 2500;
      const fps = stream.fps || 30;

      return [
        '-hwaccel', getHardwareAcceleration(),
        '-loglevel', 'error',
        '-re',
        '-fflags', '+genpts+discardcorrupt',
        '-avoid_negative_ts', 'make_zero',
        loopOption, loopValue,
        '-i', videoPath,
        '-c:v', 'libx264',
        '-preset', 'ultrafast',
        '-tune', 'zerolatency',
        '-b:v', `${bitrate}k`,
        '-maxrate', `${bitrate * 1.2}k`,
        '-bufsize', `${bitrate * 1.5}k`,
        '-pix_fmt', 'yuv420p',
        '-g', '60',
        '-keyint_min', '60',
        '-sc_threshold', '0',
        '-s', resolution,
        '-r', fps.toString(),
        '-c:a', 'aac',
        '-b:a', '128k',
        '-ar', '44100',
        '-ac', '2',
        '-f', 'flv',
        '-flvflags', 'no_duration_filesize',
        rtmpUrl
      ];
    }
  }
  // Advanced settings with smart copy/encode decision
  const resolution = stream.resolution || '1280x720';
  const bitrate = stream.bitrate || 2500;
  const fps = stream.fps || 30;

  // Check if we can use copy mode even with advanced settings
  const shouldReencode = needsReencoding(video, resolution, bitrate, fps);

  if (!shouldReencode) {
    // Check if video codec is compatible with copy mode
    const isHEVC = video.codec && (video.codec.toLowerCase().includes('hevc') || video.codec.toLowerCase().includes('h265'));

    if (isHEVC) {
      console.log(`[StreamingService] HEVC video detected in advanced mode, forcing re-encoding for stream ${stream.id}`);
      // Force re-encoding for HEVC even in copy mode
    } else {
      // Use optimized copy mode for H.264 videos
      console.log(`[StreamingService] Using optimized copy mode for stream ${stream.id} (video is compatible)`);
      return [
        '-hwaccel', getHardwareAcceleration(),
        '-loglevel', 'error',
        '-re',
        '-fflags', '+genpts+discardcorrupt',
        '-avoid_negative_ts', 'make_zero',
        loopOption, loopValue,
        '-i', videoPath,
        '-c:v', 'copy',
        '-c:a', 'copy',
        '-bsf:v', 'h264_mp4toannexb',
        '-f', 'flv',
        '-flvflags', 'no_duration_filesize',
        rtmpUrl
      ];
    }
  }

  // Full re-encoding mode (only when necessary)
  console.log(`[StreamingService] Using re-encoding mode for stream ${stream.id} (video needs optimization)`);
  return [
    '-hwaccel', getHardwareAcceleration(),
    '-loglevel', 'error',
    '-re',
    '-fflags', '+genpts+discardcorrupt',
    '-avoid_negative_ts', 'make_zero',
    loopOption, loopValue,
    '-i', videoPath,
    '-c:v', 'libx264',
    '-preset', 'ultrafast', // Changed from 'veryfast' to 'ultrafast' for better performance
    '-tune', 'zerolatency', // Optimize for low latency streaming
    '-b:v', `${bitrate}k`,
    '-maxrate', `${bitrate * 1.2}k`, // Reduced from 1.5x to 1.2x
    '-bufsize', `${bitrate * 1.5}k`, // Reduced from 2x to 1.5x
    '-pix_fmt', 'yuv420p',
    '-g', '60',
    '-keyint_min', '60',
    '-sc_threshold', '0',
    '-s', resolution,
    '-r', fps.toString(),
    '-c:a', 'aac',
    '-b:a', '128k',
    '-ar', '44100',
    '-ac', '2',
    '-f', 'flv',
    '-flvflags', 'no_duration_filesize',
    rtmpUrl
  ];
}
async function startStream(streamId) {
  try {
    // Validate stream ID
    if (!streamId) {
      throw createValidationError('Stream ID is required', null, 'streamId');
    }

    // Check if stream is blacklisted due to repeated failures
    if (failedStreams.has(streamId)) {
      throw createStreamingError(
        'Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.',
        streamId,
        'start'
      );
    }

    streamRetryCount.set(streamId, 0);

    // Check if stream is already active
    if (activeStreams.has(streamId)) {
      throw createStreamingError('Stream is already active', streamId, 'start');
    }

    // Get stream from database
    const stream = await Stream.findById(streamId);
    if (!stream) {
      throw createNotFoundError('Stream', streamId);
    }

    // Validate stream configuration
    if (!stream.rtmp_url) {
      throw createValidationError('RTMP URL is required', null, 'rtmp_url');
    }

    if (!stream.stream_key) {
      throw createValidationError('Stream key is required', null, 'stream_key');
    }

    if (!stream.video_id) {
      throw createValidationError('Video is required for streaming', null, 'video_id');
    }
    const ffmpegArgs = await buildFFmpegArgs(stream);
    const fullCommand = `${ffmpegPath} ${ffmpegArgs.join(' ')}`;
    addStreamLog(streamId, `Starting stream with command: ${fullCommand}`);
    console.log(`Starting stream: ${fullCommand}`);
    const ffmpegProcess = spawn(ffmpegPath, ffmpegArgs, {
      detached: false,
      stdio: ['ignore', 'pipe', 'pipe']
    });
    activeStreams.set(streamId, ffmpegProcess);
    await Stream.updateStatus(streamId, 'live', stream.user_id);

    // Send notification for stream started
    try {
      await notificationService.notifyStreamStarted(streamId, stream.user_id, stream.title);
    } catch (notifError) {
      console.error('Error sending stream start notification:', notifError);
    }
    // Production-optimized FFmpeg logging
    const isProduction = process.env.NODE_ENV === 'production';
    const enableVerboseFFmpegLogs = process.env.ENABLE_VERBOSE_FFMPEG_LOGS === 'true';

    ffmpegProcess.stdout.on('data', (data) => {
      const message = data.toString().trim();
      if (message && !isProduction) {
        addStreamLog(streamId, `[OUTPUT] ${message}`);
        if (enableVerboseFFmpegLogs) {
          console.debug(`[FFMPEG_STDOUT] ${streamId}: ${message}`);
        }
      }
    });

    ffmpegProcess.stderr.on('data', (data) => {
      const message = data.toString().trim();
      if (message) {
        // Always log to stream logs for debugging
        addStreamLog(streamId, `[FFmpeg] ${message}`);

        // Filter out progress messages and only log important events
        const isProgressMessage = message.includes('frame=') ||
                                 message.includes('fps=') ||
                                 message.includes('bitrate=') ||
                                 message.includes('time=') ||
                                 message.includes('speed=');

        const isImportantMessage = message.includes('error') ||
                                  message.includes('Error') ||
                                  message.includes('warning') ||
                                  message.includes('Warning') ||
                                  message.includes('failed') ||
                                  message.includes('Failed');

        // In production, only log errors and warnings
        if (isImportantMessage) {
          console.error(`[FFMPEG_STDERR] ${streamId}: ${message}`);
        } else if (!isProduction && !isProgressMessage && enableVerboseFFmpegLogs) {
          console.debug(`[FFMPEG_STDERR] ${streamId}: ${message}`);
        }

        // Check for critical errors that should trigger immediate auto-stop
        if (message.includes('I/O error') ||
            message.includes('Connection refused') ||
            message.includes('Network is unreachable') ||
            message.includes('Invalid stream key') ||
            message.includes('Authentication failed') ||
            message.includes('403 Forbidden') ||
            message.includes('404 Not Found')) {

          const autoStopDecision = shouldAutoStopStream(streamId, message);

          if (autoStopDecision.shouldStop) {
            console.log(`[StreamingService] Critical error detected, auto-stopping stream ${streamId}: ${autoStopDecision.reason}`);
            // Send notification for stream error (async wrapper)
            (async () => {
              try {
                const stream = await Stream.findById(streamId);
                if (stream) {
                  await notificationService.notifyStreamError(streamId, stream.user_id, stream.title, message);
                }
              } catch (notifError) {
                console.error('Error sending stream error notification:', notifError);
              }
            })();
            // Use setTimeout to avoid blocking the current stderr processing
            setTimeout(async () => {
              await autoStopStream(streamId, autoStopDecision.reason);
            }, 1000);
          }
        }
      }
    });
    ffmpegProcess.on('exit', async (code, signal) => {
      addStreamLog(streamId, `Stream ended with code ${code}, signal: ${signal}`);
      console.log(`[FFMPEG_EXIT] ${streamId}: Code=${code}, Signal=${signal}`);
      const wasActive = activeStreams.delete(streamId);
      const isManualStop = manuallyStoppingStreams.has(streamId);
      if (isManualStop) {
        console.log(`[StreamingService] Stream ${streamId} was manually stopped, not restarting`);
        // Clean up all stream data for manual stops
        cleanupStreamData(streamId);
        if (wasActive) {
          try {
            await Stream.updateStatus(streamId, 'offline');
            if (typeof schedulerService !== 'undefined' && schedulerService.cancelStreamTermination) {
              schedulerService.handleStreamStopped(streamId);
            }
          } catch (error) {
            console.error(`[StreamingService] Error updating stream status after manual stop: ${error.message}`);
          }
        }
        return;
      }
      if (signal === 'SIGSEGV') {
        const retryCount = streamRetryCount.get(streamId) || 0;
        if (retryCount < MAX_RETRY_ATTEMPTS) {
          streamRetryCount.set(streamId, retryCount + 1);
          console.log(`[StreamingService] FFmpeg crashed with SIGSEGV. Attempting restart #${retryCount + 1} for stream ${streamId}`);
          addStreamLog(streamId, `FFmpeg crashed with SIGSEGV. Attempting restart #${retryCount + 1}`);
          setTimeout(async () => {
            try {
              const streamInfo = await Stream.findById(streamId);
              if (streamInfo) {
                const result = await startStream(streamId);
                if (!result.success) {
                  console.error(`[StreamingService] Failed to restart stream: ${result.error}`);
                  await Stream.updateStatus(streamId, 'offline');
                }
              } else {
                console.error(`[StreamingService] Cannot restart stream ${streamId}: not found in database`);
              }
            } catch (error) {
              console.error(`[StreamingService] Error during stream restart: ${error.message}`);
              try {
                await Stream.updateStatus(streamId, 'offline');
              } catch (dbError) {
                console.error(`Error updating stream status: ${dbError.message}`);
              }
            }
          }, 3000);
          return;
        } else {
          console.error(`[StreamingService] Maximum retry attempts (${MAX_RETRY_ATTEMPTS}) reached for stream ${streamId}`);
          addStreamLog(streamId, `Maximum retry attempts (${MAX_RETRY_ATTEMPTS}) reached, stopping stream`);
        }
      }
      else {
        let errorMessage = '';
        if (code !== 0 && code !== null) {
          errorMessage = `FFmpeg process exited with error code ${code}`;
          addStreamLog(streamId, errorMessage);
          console.error(`[StreamingService] ${errorMessage} for stream ${streamId}`);

          // Check if stream should be auto-stopped due to repeated failures
          const autoStopDecision = shouldAutoStopStream(streamId, errorMessage);

          if (autoStopDecision.shouldStop) {
            console.log(`[StreamingService] Auto-stopping stream ${streamId}: ${autoStopDecision.reason}`);
            await autoStopStream(streamId, autoStopDecision.reason);
            return;
          }

          const retryCount = streamRetryCount.get(streamId) || 0;
          if (retryCount < MAX_RETRY_ATTEMPTS) {
            streamRetryCount.set(streamId, retryCount + 1);
            console.log(`[StreamingService] FFmpeg exited with code ${code}. Attempting restart #${retryCount + 1} for stream ${streamId}`);
            setTimeout(async () => {
              try {
                const streamInfo = await Stream.findById(streamId);
                if (streamInfo) {
                  const result = await startStream(streamId);
                  if (!result.success) {
                    console.error(`[StreamingService] Failed to restart stream: ${result.error}`);
                    await Stream.updateStatus(streamId, 'offline');
                  }
                }
              } catch (error) {
                console.error(`[StreamingService] Error during stream restart: ${error.message}`);
                await Stream.updateStatus(streamId, 'offline');
              }
            }, 3000);
            return;
          } else {
            // Max retries reached, auto-stop the stream
            console.error(`[StreamingService] Maximum retry attempts (${MAX_RETRY_ATTEMPTS}) reached for stream ${streamId}`);
            await autoStopStream(streamId, `Maximum retry attempts reached (${MAX_RETRY_ATTEMPTS})`);
          }
        }
        if (wasActive) {
          try {
            console.log(`[StreamingService] Updating stream ${streamId} status to offline after FFmpeg exit`);
            await Stream.updateStatus(streamId, 'offline');
            if (typeof schedulerService !== 'undefined' && schedulerService.cancelStreamTermination) {
              schedulerService.handleStreamStopped(streamId);
            }
          } catch (error) {
            console.error(`[StreamingService] Error updating stream status after exit: ${error.message}`);
          }
        }
      }
    });
    ffmpegProcess.on('error', async (err) => {
      addStreamLog(streamId, `Error in stream process: ${err.message}`);
      console.error(`[FFMPEG_PROCESS_ERROR] ${streamId}: ${err.message}`);
      activeStreams.delete(streamId);

      // Clean up stream data on error
      cleanupStreamData(streamId);

      try {
        await Stream.updateStatus(streamId, 'offline');
      } catch (error) {
        console.error(`Error updating stream status: ${error.message}`);
      }
    });
    // Removed ffmpegProcess.unref() to ensure proper process management
    if (stream.duration && typeof schedulerService !== 'undefined') {
      schedulerService.scheduleStreamTermination(streamId, stream.duration);
    }
    return {
      success: true,
      message: 'Stream started successfully',
      isAdvancedMode: stream.use_advanced_settings
    };
  } catch (error) {
    // Enhanced error logging with context
    const errorContext = {
      streamId,
      operation: 'startStream',
      timestamp: new Date().toISOString()
    };

    logError(error, null, errorContext);
    addStreamLog(streamId, `Failed to start stream: ${error.message}`);

    // Clean up any partial state
    activeStreams.delete(streamId);
    streamRetryCount.delete(streamId);

    // Return structured error response
    return {
      success: false,
      error: error.message,
      errorType: error.type || 'STREAMING_ERROR',
      errorId: error.errorId
    };
  }
}
async function stopStream(streamId) {
  try {
    const ffmpegProcess = activeStreams.get(streamId);
    const isActive = ffmpegProcess !== undefined;
    console.log(`[StreamingService] Stop request for stream ${streamId}, isActive: ${isActive}`);
    if (!isActive) {
      const stream = await Stream.findById(streamId);
      if (stream && stream.status === 'live') {
        console.log(`[StreamingService] Stream ${streamId} not active in memory but status is 'live' in DB. Fixing status.`);
        await Stream.updateStatus(streamId, 'offline', stream.user_id);
        if (typeof schedulerService !== 'undefined' && schedulerService.cancelStreamTermination) {
          schedulerService.handleStreamStopped(streamId);
        }
        return { success: true, message: 'Stream status fixed (was not active but marked as live)' };
      }
      return { success: false, error: 'Stream is not active' };
    }
    addStreamLog(streamId, 'Stopping stream...');
    console.log(`[StreamingService] Stopping active stream ${streamId}`);
    manuallyStoppingStreams.add(streamId);

    // Enhanced process termination with timeout and fallback to SIGKILL
    try {
      console.log(`[StreamingService] Sending SIGTERM to FFmpeg process for stream ${streamId}`);
      ffmpegProcess.kill('SIGTERM');

      // Set a timeout to force kill if SIGTERM doesn't work
      const forceKillTimeout = setTimeout(() => {
        if (activeStreams.has(streamId)) {
          console.log(`[StreamingService] SIGTERM timeout, sending SIGKILL to FFmpeg process for stream ${streamId}`);
          try {
            ffmpegProcess.kill('SIGKILL');
          } catch (forceKillError) {
            console.error(`[StreamingService] Error force killing FFmpeg process: ${forceKillError.message}`);
          }
        }
      }, 5000); // 5 second timeout

      // Clear timeout if process exits normally
      ffmpegProcess.once('exit', () => {
        clearTimeout(forceKillTimeout);
      });

    } catch (killError) {
      console.error(`[StreamingService] Error killing FFmpeg process: ${killError.message}`);
      manuallyStoppingStreams.delete(streamId);
    }
    const stream = await Stream.findById(streamId);
    activeStreams.delete(streamId);
    if (stream) {
      await Stream.updateStatus(streamId, 'offline', stream.user_id);
      const updatedStream = await Stream.findById(streamId);
      await saveStreamHistory(updatedStream);

      // Send notification for stream stopped
      try {
        await notificationService.notifyStreamStopped(streamId, stream.user_id, stream.title);
      } catch (notifError) {
        console.error('Error sending stream stop notification:', notifError);
      }
    }
    if (typeof schedulerService !== 'undefined' && schedulerService.cancelStreamTermination) {
      schedulerService.handleStreamStopped(streamId);
    }
    return { success: true, message: 'Stream stopped successfully' };
  } catch (error) {
    manuallyStoppingStreams.delete(streamId);
    console.error(`[StreamingService] Error stopping stream ${streamId}:`, error);
    return { success: false, error: error.message };
  }
}
async function syncStreamStatuses() {
  try {
    console.log('[StreamingService] Syncing stream statuses...');
    const liveStreams = await Stream.findAll(null, 'live');
    for (const stream of liveStreams) {
      const isReallyActive = activeStreams.has(stream.id);
      if (!isReallyActive) {
        console.log(`[StreamingService] Found inconsistent stream ${stream.id}: marked as 'live' in DB but not active in memory`);
        await Stream.updateStatus(stream.id, 'offline');
        console.log(`[StreamingService] Updated stream ${stream.id} status to 'offline'`);
      }
    }
    const activeStreamIds = Array.from(activeStreams.keys());
    for (const streamId of activeStreamIds) {
      const stream = await Stream.findById(streamId);
      if (!stream || stream.status !== 'live') {
        console.log(`[StreamingService] Found inconsistent stream ${streamId}: active in memory but not 'live' in DB`);
        if (stream) {
          await Stream.updateStatus(streamId, 'live');
          console.log(`[StreamingService] Updated stream ${streamId} status to 'live'`);
        } else {
          console.log(`[StreamingService] Stream ${streamId} not found in DB, removing from active streams`);
          const process = activeStreams.get(streamId);
          if (process) {
            try {
              process.kill('SIGTERM');
            } catch (error) {
              console.error(`[StreamingService] Error killing orphaned process: ${error.message}`);
            }
          }
          activeStreams.delete(streamId);
        }
      }
    }
    console.log(`[StreamingService] Stream status sync completed. Active streams: ${activeStreamIds.length}`);
  } catch (error) {
    console.error('[StreamingService] Error syncing stream statuses:', error);
  }
}

// Run initial sync on startup
console.log('[StreamingService] Running initial stream status sync on startup...');
syncStreamStatuses().then(() => {
  console.log('[StreamingService] Initial stream status sync completed');
  // Also cleanup any orphaned FFmpeg processes on startup
  forceCleanupOrphanedProcesses();
}).catch(error => {
  console.error('[StreamingService] Error during initial stream status sync:', error);
});

// Start periodic memory cleanup
console.log('[StreamingService] Starting periodic memory cleanup...');
const cleanupTimer = setInterval(() => {
  try {
    performPeriodicCleanup();
  } catch (error) {
    console.error('[StreamingService] Error during periodic cleanup:', error);
  }
}, CLEANUP_INTERVAL);

// Ensure cleanup timer doesn't prevent process exit
cleanupTimer.unref();

setInterval(syncStreamStatuses, 10 * 60 * 1000); // Increased from 5 to 10 minutes
// Also run orphaned process cleanup every 15 minutes
setInterval(forceCleanupOrphanedProcesses, 15 * 60 * 1000);
function isStreamActive(streamId) {
  return activeStreams.has(streamId);
}

// Function to force cleanup orphaned FFmpeg processes
async function forceCleanupOrphanedProcesses() {
  const { exec } = require('child_process');
  const os = require('os');

  try {
    console.log('[StreamingService] Checking for orphaned FFmpeg processes...');

    if (os.platform() === 'win32') {
      // Windows - check for FFmpeg processes
      exec('tasklist /FI "IMAGENAME eq ffmpeg.exe" /FO CSV', (error, stdout) => {
        if (!error && stdout) {
          const lines = stdout.split('\n');
          const ffmpegProcesses = lines.filter(line => line.includes('ffmpeg.exe'));

          if (ffmpegProcesses.length > 1) { // More than header line
            console.log(`[StreamingService] Found ${ffmpegProcesses.length - 1} FFmpeg processes, cleaning up...`);
            exec('taskkill /F /IM ffmpeg.exe', (killError) => {
              if (killError) {
                console.error('[StreamingService] Error killing orphaned FFmpeg processes:', killError.message);
              } else {
                console.log('[StreamingService] Orphaned FFmpeg processes cleaned up');
              }
            });
          }
        }
      });
    } else {
      // Linux/Mac - check for FFmpeg processes
      exec('pgrep -f ffmpeg', (error, stdout) => {
        if (!error && stdout) {
          const pids = stdout.trim().split('\n').filter(pid => pid);
          if (pids.length > 0) {
            console.log(`[StreamingService] Found ${pids.length} FFmpeg processes, cleaning up...`);
            exec(`kill -9 ${pids.join(' ')}`, (killError) => {
              if (killError) {
                console.error('[StreamingService] Error killing orphaned FFmpeg processes:', killError.message);
              } else {
                console.log('[StreamingService] Orphaned FFmpeg processes cleaned up');
              }
            });
          }
        }
      });
    }
  } catch (error) {
    console.error('[StreamingService] Error during orphaned process cleanup:', error);
  }
}
function getActiveStreams() {
  return Array.from(activeStreams.keys());
}
function getStreamLogs(streamId) {
  return streamLogs.get(streamId) || [];
}

// Function to track stream failures
function trackStreamFailure(streamId, errorMessage) {
  const now = Date.now();

  if (!streamFailureTimestamps.has(streamId)) {
    streamFailureTimestamps.set(streamId, []);
  }

  const failures = streamFailureTimestamps.get(streamId);
  failures.push({ timestamp: now, error: errorMessage });

  // Keep only failures within the time window
  const windowStart = now - (AUTO_STOP_CONFIG.FAILURE_WINDOW_MINUTES * 60 * 1000);
  const recentFailures = failures.filter(f => f.timestamp >= windowStart);
  streamFailureTimestamps.set(streamId, recentFailures);

  return recentFailures;
}

// Function to analyze if stream should be auto-stopped
function shouldAutoStopStream(streamId, errorMessage) {
  const recentFailures = trackStreamFailure(streamId, errorMessage);

  // Check for immediate stop conditions
  const ioErrors = recentFailures.filter(f =>
    f.error.includes('I/O error') ||
    f.error.includes('Connection refused') ||
    f.error.includes('Network is unreachable')
  ).length;

  const connectionErrors = recentFailures.filter(f =>
    f.error.includes('Connection') ||
    f.error.includes('timeout') ||
    f.error.includes('refused')
  ).length;

  // Immediate stop conditions
  if (ioErrors >= AUTO_STOP_CONFIG.I_O_ERROR_THRESHOLD) {
    console.log(`[StreamingService] Auto-stopping stream ${streamId}: Too many I/O errors (${ioErrors})`);
    return { shouldStop: true, reason: `Too many I/O errors (${ioErrors}/${AUTO_STOP_CONFIG.I_O_ERROR_THRESHOLD})` };
  }

  if (connectionErrors >= AUTO_STOP_CONFIG.CONNECTION_ERROR_THRESHOLD) {
    console.log(`[StreamingService] Auto-stopping stream ${streamId}: Too many connection errors (${connectionErrors})`);
    return { shouldStop: true, reason: `Too many connection errors (${connectionErrors}/${AUTO_STOP_CONFIG.CONNECTION_ERROR_THRESHOLD})` };
  }

  // General failure threshold
  if (recentFailures.length >= AUTO_STOP_CONFIG.MAX_CONSECUTIVE_FAILURES) {
    console.log(`[StreamingService] Auto-stopping stream ${streamId}: Too many consecutive failures (${recentFailures.length})`);
    return { shouldStop: true, reason: `Too many consecutive failures (${recentFailures.length}/${AUTO_STOP_CONFIG.MAX_CONSECUTIVE_FAILURES})` };
  }

  return { shouldStop: false, reason: null };
}

// Function to auto-stop a problematic stream
async function autoStopStream(streamId, reason) {
  try {
    console.log(`[StreamingService] Auto-stopping stream ${streamId}: ${reason}`);

    // Add to failed streams list
    failedStreams.add(streamId);

    // Stop the stream
    await stopStream(streamId);

    // Add log entry
    addStreamLog(streamId, `Stream auto-stopped: ${reason}`);
    addStreamLog(streamId, `Stream has been temporarily disabled. Check your RTMP settings and clear failed status to retry.`);

    // Update stream status to "error" instead of "offline"
    const stream = await Stream.findById(streamId);
    if (stream) {
      await Stream.updateStatus(streamId, 'error', stream.user_id, reason);
      console.log(`[StreamingService] Stream ${streamId} status set to 'error': ${reason}`);
    }

    // Clear retry count
    streamRetryCount.delete(streamId);

    // Remove from blacklist after 15 minutes
    setTimeout(() => {
      failedStreams.delete(streamId);
      streamFailureTimestamps.delete(streamId);
      console.log(`[StreamingService] Stream ${streamId} removed from failed streams list after cooldown`);
    }, 15 * 60 * 1000); // 15 minutes

    return true;
  } catch (error) {
    console.error(`[StreamingService] Error auto-stopping stream ${streamId}:`, error);
    return false;
  }
}

// Function to clear failed streams (for manual recovery)
async function clearFailedStream(streamId) {
  const wasBlacklisted = failedStreams.has(streamId);

  // Use centralized cleanup function
  cleanupStreamData(streamId);

  // Reset status from "error" to "offline" when clearing failed status
  try {
    const stream = await Stream.findById(streamId);
    if (stream && stream.status === 'error') {
      await Stream.updateStatus(streamId, 'offline', stream.user_id);
      console.log(`[StreamingService] Reset stream ${streamId} status from 'error' to 'offline'`);
    }
  } catch (error) {
    console.error(`[StreamingService] Error resetting stream status: ${error.message}`);
  }

  console.log(`[StreamingService] Cleared failed status for stream ${streamId}, was blacklisted: ${wasBlacklisted}`);
  return wasBlacklisted;
}
async function saveStreamHistory(stream) {
  try {
    if (!stream.start_time) {
      console.log(`[StreamingService] Not saving history for stream ${stream.id} - no start time recorded`);
      return false;
    }
    const startTime = new Date(stream.start_time);
    const endTime = stream.end_time ? new Date(stream.end_time) : new Date();
    const durationSeconds = Math.floor((endTime - startTime) / 1000);
    if (durationSeconds < 5) { // Changed from 1 to 5 seconds minimum
      console.log(`[StreamingService] Not saving history for stream ${stream.id} - duration too short (${durationSeconds}s)`);
      return false;
    }

    // Get video details with error handling
    let videoDetails = null;
    if (stream.video_id) {
      try {
        videoDetails = await Video.findById(stream.video_id);
      } catch (error) {
        console.warn(`[StreamingService] Could not fetch video details for video_id ${stream.video_id}:`, error.message);
      }
    }

    const historyData = {
      id: uuidv4(),
      stream_id: stream.id,
      title: stream.title || 'Untitled Stream',
      platform: stream.platform || 'Custom',
      platform_icon: stream.platform_icon || 'ti-broadcast',
      video_id: stream.video_id,
      video_title: videoDetails ? videoDetails.title : null,
      resolution: stream.resolution || '1280x720',
      bitrate: stream.bitrate || 2500,
      fps: stream.fps || 30,
      start_time: stream.start_time,
      end_time: stream.end_time || new Date().toISOString(),
      duration: durationSeconds,
      use_advanced_settings: stream.use_advanced_settings ? 1 : 0,
      user_id: stream.user_id
    };

    return new Promise((resolve, reject) => {
      const { db } = require('../db/database');
      db.run(
        `INSERT INTO stream_history (
          id, stream_id, title, platform, platform_icon, video_id, video_title,
          resolution, bitrate, fps, start_time, end_time, duration, use_advanced_settings, user_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          historyData.id, historyData.stream_id, historyData.title,
          historyData.platform, historyData.platform_icon, historyData.video_id, historyData.video_title,
          historyData.resolution, historyData.bitrate, historyData.fps,
          historyData.start_time, historyData.end_time, historyData.duration,
          historyData.use_advanced_settings, historyData.user_id
        ],
        function (err) {
          if (err) {
            console.error('[StreamingService] Error saving stream history:', err.message);
            return reject(err);
          }
          console.log(`[StreamingService] Stream history saved for stream ${stream.id}, duration: ${Math.floor(durationSeconds/60)}m ${durationSeconds%60}s`);
          resolve(historyData);
        }
      );
    });
  } catch (error) {
    console.error('[StreamingService] Failed to save stream history:', error);
    return false;
  }
}
module.exports = {
  startStream,
  stopStream,
  isStreamActive,
  getActiveStreams,
  getStreamLogs,
  syncStreamStatuses,
  saveStreamHistory,
  restoreActiveStreams,
  cleanupOrphanedStreams: restoreActiveStreams, // Alias for the cleanup function
  clearFailedStream,
  forceCleanupOrphanedProcesses,
  // Memory management functions
  cleanupStreamData,
  performPeriodicCleanup,
  // Load balancing functions
  applyLoadBalancing,
  getLoadBalanceStatus,
  setLoadBalancingEnabled: (enabled) => { loadBalancingEnabled = enabled; },
  updateLoadBalanceConfig: (config) => {
    if (config.thresholds) Object.assign(LOAD_BALANCE_CONFIG.CPU_THRESHOLDS, config.thresholds);
    if (config.qualityPresets) Object.assign(LOAD_BALANCE_CONFIG.QUALITY_PRESETS, config.qualityPresets);
    if (config.checkInterval) LOAD_BALANCE_CONFIG.CPU_CHECK_INTERVAL = config.checkInterval;
    if (config.cooldown) LOAD_BALANCE_CONFIG.QUALITY_CHANGE_COOLDOWN = config.cooldown;
  }
};
schedulerService.init(module.exports);