const { db } = require('./db/database');

/**
 * Check referral data in database to debug commission calculation
 */

async function checkReferralData() {
  try {
    console.log('🔍 Checking referral data in database...\n');

    // Check referrals table
    console.log('📊 REFERRALS TABLE:');
    const referrals = await new Promise((resolve, reject) => {
      db.all(
        `SELECT id, referrer_id, referee_id, status, commission_amount, commission_paid, created_at
         FROM referrals 
         ORDER BY created_at DESC`,
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        }
      );
    });

    if (referrals.length === 0) {
      console.log('❌ No referrals found in database');
    } else {
      console.log(`✅ Found ${referrals.length} referrals:`);
      referrals.forEach((ref, index) => {
        console.log(`  ${index + 1}. ID: ${ref.id.substring(0, 8)}...`);
        console.log(`     Status: ${ref.status}`);
        console.log(`     Commission: Rp ${(ref.commission_amount || 0).toLocaleString('id-ID')}`);
        console.log(`     Paid: ${ref.commission_paid ? 'Yes' : 'No'}`);
        console.log(`     Date: ${new Date(ref.created_at).toLocaleDateString('id-ID')}`);
        console.log('');
      });
    }

    // Check referral_earnings table
    console.log('\n💰 REFERRAL_EARNINGS TABLE:');
    const earnings = await new Promise((resolve, reject) => {
      db.all(
        `SELECT id, user_id, referral_id, amount, status, created_at
         FROM referral_earnings 
         ORDER BY created_at DESC`,
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        }
      );
    });

    if (earnings.length === 0) {
      console.log('❌ No earnings found in database');
    } else {
      console.log(`✅ Found ${earnings.length} earnings:`);
      earnings.forEach((earning, index) => {
        console.log(`  ${index + 1}. ID: ${earning.id.substring(0, 8)}...`);
        console.log(`     Amount: Rp ${earning.amount.toLocaleString('id-ID')}`);
        console.log(`     Status: ${earning.status}`);
        console.log(`     Date: ${new Date(earning.created_at).toLocaleDateString('id-ID')}`);
        console.log('');
      });
    }

    // Calculate totals
    console.log('\n📈 CALCULATIONS:');
    
    // From referrals table
    const totalFromReferrals = referrals
      .filter(r => r.status === 'completed')
      .reduce((sum, r) => sum + (r.commission_amount || 0), 0);
    
    // From earnings table
    const totalFromEarnings = earnings
      .filter(e => e.status === 'completed')
      .reduce((sum, e) => sum + (e.amount || 0), 0);

    console.log(`Total from referrals table (completed): Rp ${totalFromReferrals.toLocaleString('id-ID')}`);
    console.log(`Total from earnings table (completed): Rp ${totalFromEarnings.toLocaleString('id-ID')}`);
    console.log(`Max of both: Rp ${Math.max(totalFromReferrals, totalFromEarnings).toLocaleString('id-ID')}`);

    // Check user balances
    console.log('\n👥 USER REFERRAL BALANCES:');
    const userBalances = await new Promise((resolve, reject) => {
      db.all(
        `SELECT username, referral_balance
         FROM users 
         WHERE referral_balance > 0
         ORDER BY referral_balance DESC`,
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        }
      );
    });

    if (userBalances.length === 0) {
      console.log('❌ No users with referral balance found');
    } else {
      console.log(`✅ Found ${userBalances.length} users with referral balance:`);
      userBalances.forEach((user, index) => {
        console.log(`  ${index + 1}. ${user.username}: Rp ${user.referral_balance.toLocaleString('id-ID')}`);
      });
    }

    // Check withdrawal requests
    console.log('\n💸 WITHDRAWAL REQUESTS:');
    const withdrawals = await new Promise((resolve, reject) => {
      db.all(
        `SELECT wr.amount, wr.status, u.username, wr.requested_at
         FROM withdrawal_requests wr
         JOIN users u ON wr.user_id = u.id
         ORDER BY wr.requested_at DESC`,
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        }
      );
    });

    if (withdrawals.length === 0) {
      console.log('❌ No withdrawal requests found');
    } else {
      console.log(`✅ Found ${withdrawals.length} withdrawal requests:`);
      withdrawals.forEach((withdrawal, index) => {
        console.log(`  ${index + 1}. ${withdrawal.username}: Rp ${withdrawal.amount.toLocaleString('id-ID')} (${withdrawal.status})`);
        console.log(`     Date: ${new Date(withdrawal.requested_at).toLocaleDateString('id-ID')}`);
      });
    }

  } catch (error) {
    console.error('❌ Error checking referral data:', error);
  } finally {
    console.log('\n🔒 Database connection closed');
    db.close();
  }
}

// Run the check
checkReferralData();
