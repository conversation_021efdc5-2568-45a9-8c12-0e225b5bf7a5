const os = require('os');

/**
 * CPU Configuration for StreamOnPod
 * Dynamic 50:50 allocation between encoding and streaming processes
 */

class CPUConfig {
  constructor() {
    this.totalCores = os.cpus().length;
    this.updateAllocation();
  }

  /**
   * Update CPU allocation based on current system
   */
  updateAllocation() {
    // 50:50 split with encoding getting the first half
    this.encodingCores = Math.floor(this.totalCores / 2);
    this.streamingCores = this.totalCores - this.encodingCores;
    
    // Ensure minimum allocation
    if (this.encodingCores < 1) this.encodingCores = 1;
    if (this.streamingCores < 1) this.streamingCores = 1;
    
    // Core ranges (0-indexed)
    this.encodingCoreStart = 0;
    this.encodingCoreEnd = this.encodingCores - 1;
    this.streamingCoreStart = this.encodingCores;
    this.streamingCoreEnd = this.totalCores - 1;
  }

  /**
   * Get CPU allocation configuration
   */
  getAllocation() {
    return {
      total: this.totalCores,
      encoding: {
        cores: this.encodingCores,
        start: this.encodingCoreStart,
        end: this.encodingCoreEnd,
        range: this.encodingCoreStart === this.encodingCoreEnd ? 
          this.encodingCoreStart.toString() : 
          `${this.encodingCoreStart}-${this.encodingCoreEnd}`,
        threads: this.encodingCores,
        maxConcurrentJobs: Math.max(1, Math.floor(this.encodingCores / 2))
      },
      streaming: {
        cores: this.streamingCores,
        start: this.streamingCoreStart,
        end: this.streamingCoreEnd,
        range: this.streamingCoreStart === this.streamingCoreEnd ? 
          this.streamingCoreStart.toString() : 
          `${this.streamingCoreStart}-${this.streamingCoreEnd}`,
        threads: Math.min(this.streamingCores, 2), // Copy mode doesn't need many threads
        maxConcurrentStreams: Math.max(2, this.streamingCores)
      }
    };
  }

  /**
   * Get configuration for different server sizes
   */
  getScenarios() {
    const scenarios = {};
    
    // Test different core counts
    const testCores = [4, 6, 8, 10, 12, 16, 20, 24, 32];
    
    testCores.forEach(cores => {
      const encodingCores = Math.floor(cores / 2);
      const streamingCores = cores - encodingCores;
      
      scenarios[`${cores}_cores`] = {
        total: cores,
        encoding: {
          cores: encodingCores,
          range: encodingCores === 1 ? '0' : `0-${encodingCores - 1}`,
          maxJobs: Math.max(1, Math.floor(encodingCores / 2))
        },
        streaming: {
          cores: streamingCores,
          range: streamingCores === 1 ? encodingCores.toString() : `${encodingCores}-${cores - 1}`,
          maxStreams: Math.max(2, streamingCores)
        }
      };
    });
    
    return scenarios;
  }

  /**
   * Get recommendations based on current system
   */
  getRecommendations() {
    const allocation = this.getAllocation();
    
    return {
      current: allocation,
      recommendations: {
        encoding: {
          performance: allocation.encoding.cores >= 4 ? 'Excellent' : 
                      allocation.encoding.cores >= 2 ? 'Good' : 'Basic',
          suggestion: allocation.encoding.cores < 4 ? 
            'Consider upgrading to 8+ cores for better encoding performance' : 
            'Current allocation is optimal for encoding tasks'
        },
        streaming: {
          performance: allocation.streaming.cores >= 4 ? 'Excellent' : 
                      allocation.streaming.cores >= 2 ? 'Good' : 'Basic',
          suggestion: allocation.streaming.cores < 2 ? 
            'Consider upgrading to 6+ cores for better streaming capacity' : 
            'Current allocation supports multiple concurrent streams'
        },
        overall: {
          status: this.totalCores >= 8 ? 'Optimal' : 
                  this.totalCores >= 4 ? 'Adequate' : 'Minimal',
          upgrade: this.totalCores < 8 ? 
            'Upgrade to 8+ cores recommended for production use' : 
            this.totalCores < 16 ? 
            'Consider 16+ cores for high-load scenarios' : 
            'Current configuration is excellent for high-load production'
        }
      }
    };
  }

  /**
   * Check if CPU affinity is supported
   */
  isAffinitySupported() {
    return process.platform === 'linux';
  }

  /**
   * Get platform-specific information
   */
  getPlatformInfo() {
    return {
      platform: process.platform,
      architecture: process.arch,
      cpuModel: os.cpus()[0]?.model || 'Unknown',
      affinitySupported: this.isAffinitySupported(),
      recommendation: this.isAffinitySupported() ? 
        'CPU affinity will be applied for optimal performance' : 
        'Consider using Linux for better CPU management'
    };
  }
}

// Export singleton instance
module.exports = new CPUConfig();
