const fs = require('fs');
const path = require('path');

// Create a test video file by generating binary data
// This creates a fake video file for testing chunked upload
function createTestVideoFile(filename, sizeInMB) {
  const sizeInBytes = sizeInMB * 1024 * 1024;
  const chunkSize = 1024 * 1024; // 1MB chunks
  const filePath = path.join(__dirname, filename);
  
  console.log(`Creating test video file: ${filename} (${sizeInMB}MB)`);
  
  // Create a write stream
  const writeStream = fs.createWriteStream(filePath);
  
  // Write MP4 header (minimal valid MP4 structure)
  const mp4Header = Buffer.from([
    0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70, // ftyp box
    0x69, 0x73, 0x6F, 0x6D, 0x00, 0x00, 0x02, 0x00,
    0x69, 0x73, 0x6F, 0x6D, 0x69, 0x73, 0x6F, 0x32,
    0x61, 0x76, 0x63, 0x31, 0x6D, 0x70, 0x34, 0x31
  ]);
  
  writeStream.write(mp4Header);
  
  let bytesWritten = mp4Header.length;
  
  // Fill the rest with dummy data
  while (bytesWritten < sizeInBytes) {
    const remainingBytes = sizeInBytes - bytesWritten;
    const currentChunkSize = Math.min(chunkSize, remainingBytes);
    
    // Create chunk with pattern data
    const chunk = Buffer.alloc(currentChunkSize);
    for (let i = 0; i < currentChunkSize; i++) {
      chunk[i] = (bytesWritten + i) % 256;
    }
    
    writeStream.write(chunk);
    bytesWritten += currentChunkSize;
    
    // Show progress
    const progress = ((bytesWritten / sizeInBytes) * 100).toFixed(1);
    process.stdout.write(`\rProgress: ${progress}%`);
  }
  
  writeStream.end();
  
  writeStream.on('finish', () => {
    console.log(`\n✅ Test video file created: ${filename}`);
    console.log(`📊 File size: ${(fs.statSync(filePath).size / 1024 / 1024).toFixed(2)} MB`);
  });
  
  writeStream.on('error', (err) => {
    console.error('❌ Error creating test file:', err);
  });
}

// Create test files of different sizes
console.log('🎬 Creating test video files for chunked upload testing...\n');

// Create a 60MB file (will trigger chunked upload)
createTestVideoFile('test_video_60mb.mp4', 60);

// Create a 30MB file (will use regular upload)
setTimeout(() => {
  createTestVideoFile('test_video_30mb.mp4', 30);
}, 1000);

// Create a 100MB file (will definitely trigger chunked upload)
setTimeout(() => {
  createTestVideoFile('test_video_100mb.mp4', 100);
}, 2000);
