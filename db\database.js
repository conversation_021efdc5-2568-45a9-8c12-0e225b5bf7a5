const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const bcrypt = require('bcrypt');
const dbDir = path.join(__dirname);
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}
const dbPath = path.join(dbDir, 'streamonpod.db');
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Error connecting to database:', err.message);
  } else {
    createTables();
  }
});
function createTables() {
  db.run(`CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE,
    password TEXT NOT NULL,
    avatar_path TEXT,
    gdrive_api_key TEXT,
    role TEXT DEFAULT 'user',
    plan_type TEXT DEFAULT 'Preview',
    max_streaming_slots INTEGER DEFAULT 0,
    max_storage_gb INTEGER DEFAULT 2,
    used_storage_gb REAL DEFAULT 0,
    subscription_start_date TIMESTAMP,
    subscription_end_date TIMESTAMP,
    trial_start_date TIMESTAMP,
    trial_end_date TIMESTAMP,
    trial_slots INTEGER DEFAULT 0,
    trial_storage_gb REAL DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  )`, (err) => {
    if (err) {
      console.error('Error creating users table:', err.message);
    }
  });
  db.run(`CREATE TABLE IF NOT EXISTS videos (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    filepath TEXT NOT NULL,
    thumbnail_path TEXT,
    file_size INTEGER,
    duration REAL,
    format TEXT,
    resolution TEXT,
    bitrate INTEGER,
    fps TEXT,
    codec TEXT,
    audio_codec TEXT,
    user_id TEXT,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
  )`, (err) => {
    if (err) {
      console.error('Error creating videos table:', err.message);
    }

    // Add codec columns if they don't exist (migration)
    db.run(`ALTER TABLE videos ADD COLUMN codec TEXT`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding codec column:', err.message);
      }
    });

    db.run(`ALTER TABLE videos ADD COLUMN audio_codec TEXT`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding audio_codec column:', err.message);
      }
    });

    // Add schedule_timezone column to streams table if it doesn't exist
    db.run(`ALTER TABLE streams ADD COLUMN schedule_timezone TEXT DEFAULT 'UTC'`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding schedule_timezone column:', err.message);
      }
    });
  });
  db.run(`CREATE TABLE IF NOT EXISTS streams (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    video_id TEXT,
    rtmp_url TEXT NOT NULL,
    stream_key TEXT NOT NULL,
    platform TEXT,
    platform_icon TEXT,
    bitrate INTEGER DEFAULT 2500,
    resolution TEXT,
    fps INTEGER DEFAULT 30,
    orientation TEXT DEFAULT 'horizontal',
    loop_video BOOLEAN DEFAULT 1,
    schedule_time TIMESTAMP,
    schedule_timezone TEXT DEFAULT 'UTC',
    duration INTEGER,
    status TEXT DEFAULT 'offline',
    status_updated_at TIMESTAMP,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    use_advanced_settings BOOLEAN DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (video_id) REFERENCES videos(id)
  )`, (err) => {
    if (err) {
      console.error('Error creating streams table:', err.message);
    }
  });
  db.run(`CREATE TABLE IF NOT EXISTS stream_history (
    id TEXT PRIMARY KEY,
    stream_id TEXT,
    title TEXT NOT NULL,
    platform TEXT,
    platform_icon TEXT,
    video_id TEXT,
    video_title TEXT,
    resolution TEXT,
    bitrate INTEGER,
    fps INTEGER,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    duration INTEGER,
    use_advanced_settings BOOLEAN DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (stream_id) REFERENCES streams(id),
    FOREIGN KEY (video_id) REFERENCES videos(id)
  )`, (err) => {
    if (err) {
      console.error('Error creating stream_history table:', err.message);
    }
  });

  // Create subscription plans table
  db.run(`CREATE TABLE IF NOT EXISTS subscription_plans (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    price REAL NOT NULL,
    currency TEXT DEFAULT 'USD',
    billing_period TEXT DEFAULT 'monthly',
    max_streaming_slots INTEGER DEFAULT 1,
    max_storage_gb INTEGER DEFAULT 5,
    features TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  )`, (err) => {
    if (err) {
      console.error('Error creating subscription_plans table:', err.message);
    }
  });

  // Create transactions table for payment tracking
  db.run(`CREATE TABLE IF NOT EXISTS transactions (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    plan_id TEXT NOT NULL,
    order_id TEXT UNIQUE NOT NULL,
    amount_idr INTEGER NOT NULL,
    payment_method TEXT DEFAULT 'midtrans',
    status TEXT DEFAULT 'pending',
    midtrans_token TEXT,
    midtrans_redirect_url TEXT,
    midtrans_transaction_id TEXT,
    midtrans_payment_type TEXT,
    midtrans_transaction_time TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (plan_id) REFERENCES subscription_plans (id)
  )`, (err) => {
    if (err) {
      console.error('Error creating transactions table:', err.message);
    }
  });

  // Create user subscriptions table
  db.run(`CREATE TABLE IF NOT EXISTS user_subscriptions (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    plan_id TEXT NOT NULL,
    status TEXT DEFAULT 'active',
    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP,
    payment_method TEXT,
    payment_id TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id)
  )`, (err) => {
    if (err) {
      console.error('Error creating user_subscriptions table:', err.message);
    }
  });

  // Create role permissions table
  db.run(`CREATE TABLE IF NOT EXISTS role_permissions (
    id TEXT PRIMARY KEY,
    role TEXT NOT NULL,
    permission TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  )`, (err) => {
    if (err) {
      console.error('Error creating role_permissions table:', err.message);
    }
  });

  // Create notifications table
  db.run(`CREATE TABLE IF NOT EXISTS notifications (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT DEFAULT 'info',
    category TEXT DEFAULT 'system',
    priority TEXT DEFAULT 'normal',
    target_user_id TEXT,
    metadata TEXT,
    is_read BOOLEAN DEFAULT 0,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (target_user_id) REFERENCES users(id)
  )`, (err) => {
    if (err) {
      console.error('Error creating notifications table:', err.message);
    }
  });

  // Create notifications table
  db.run(`CREATE TABLE IF NOT EXISTS notifications (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT DEFAULT 'info',
    category TEXT DEFAULT 'system',
    priority TEXT DEFAULT 'normal',
    target_user_id TEXT,
    metadata TEXT,
    is_read BOOLEAN DEFAULT 0,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (target_user_id) REFERENCES users(id)
  )`, (err) => {
    if (err) {
      console.error('Error creating notifications table:', err.message);
    }
  });

  // Create referrals table
  db.run(`CREATE TABLE IF NOT EXISTS referrals (
    id TEXT PRIMARY KEY,
    referrer_id TEXT NOT NULL,
    referee_id TEXT,
    referral_code TEXT UNIQUE NOT NULL,
    status TEXT DEFAULT 'pending',
    commission_amount INTEGER DEFAULT 0,
    commission_paid BOOLEAN DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users (id),
    FOREIGN KEY (referee_id) REFERENCES users (id)
  )`, (err) => {
    if (err) {
      console.error('Error creating referrals table:', err.message);
    }
  });

  // Create referral_earnings table
  db.run(`CREATE TABLE IF NOT EXISTS referral_earnings (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    referral_id TEXT NOT NULL,
    transaction_id TEXT,
    amount INTEGER NOT NULL,
    status TEXT DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (referral_id) REFERENCES referrals (id),
    FOREIGN KEY (transaction_id) REFERENCES transactions (id)
  )`, (err) => {
    if (err) {
      console.error('Error creating referral_earnings table:', err.message);
    }
  });

  // Create withdrawal_requests table
  db.run(`CREATE TABLE IF NOT EXISTS withdrawal_requests (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    amount INTEGER NOT NULL,
    bank_name TEXT NOT NULL,
    account_number TEXT NOT NULL,
    account_name TEXT NOT NULL,
    status TEXT DEFAULT 'pending',
    admin_notes TEXT,
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    processed_by TEXT,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (processed_by) REFERENCES users (id)
  )`, (err) => {
    if (err) {
      console.error('Error creating withdrawal_requests table:', err.message);
    }
  });

  // Create referral_clicks table
  db.run(`CREATE TABLE IF NOT EXISTS referral_clicks (
    id TEXT PRIMARY KEY,
    referral_code TEXT NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    clicked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  )`, (err) => {
    if (err) {
      console.error('Error creating referral_clicks table:', err.message);
    }
  });

  // Insert default subscription plans
  insertDefaultPlans();
  insertDefaultPermissions();

  // Add trial columns to existing users table if they don't exist
  addTrialColumns();

  // Add referral columns to users table if they don't exist
  addReferralColumns();
}

function addTrialColumns() {
  // Check if trial columns exist, if not add them
  db.all("PRAGMA table_info(users)", (err, columns) => {
    if (err) {
      console.error('Error checking users table structure:', err);
      return;
    }

    const columnNames = columns.map(col => col.name);

    if (!columnNames.includes('trial_start_date')) {
      db.run("ALTER TABLE users ADD COLUMN trial_start_date TIMESTAMP", (err) => {
        if (err) {
          console.error('Error adding trial_start_date column:', err);
        } else {
          console.log('✅ Added trial_start_date column to users table');
        }
      });
    }

    if (!columnNames.includes('trial_end_date')) {
      db.run("ALTER TABLE users ADD COLUMN trial_end_date TIMESTAMP", (err) => {
        if (err) {
          console.error('Error adding trial_end_date column:', err);
        } else {
          console.log('✅ Added trial_end_date column to users table');
        }
      });
    }

    if (!columnNames.includes('trial_slots')) {
      db.run("ALTER TABLE users ADD COLUMN trial_slots INTEGER DEFAULT 0", (err) => {
        if (err) {
          console.error('Error adding trial_slots column:', err);
        } else {
          console.log('✅ Added trial_slots column to users table');
        }
      });
    }

    if (!columnNames.includes('trial_storage_gb')) {
      db.run("ALTER TABLE users ADD COLUMN trial_storage_gb REAL DEFAULT 0", (err) => {
        if (err) {
          console.error('Error adding trial_storage_gb column:', err);
        } else {
          console.log('✅ Added trial_storage_gb column to users table');
        }
      });
    }
  });
}

function addReferralColumns() {
  // Check if referral columns exist, if not add them
  db.all("PRAGMA table_info(users)", (err, columns) => {
    if (err) {
      console.error('Error checking users table structure for referral columns:', err);
      return;
    }

    const columnNames = columns.map(col => col.name);

    if (!columnNames.includes('referral_balance')) {
      db.run("ALTER TABLE users ADD COLUMN referral_balance INTEGER DEFAULT 0", (err) => {
        if (err) {
          console.error('Error adding referral_balance column:', err);
        } else {
          console.log('✅ Added referral_balance column to users table');
        }
      });
    }

    if (!columnNames.includes('referral_code')) {
      db.run("ALTER TABLE users ADD COLUMN referral_code TEXT", (err) => {
        if (err) {
          console.error('Error adding referral_code column:', err);
        } else {
          console.log('✅ Added referral_code column to users table');
          // Add unique constraint separately
          db.run("CREATE UNIQUE INDEX IF NOT EXISTS idx_users_referral_code ON users(referral_code)", (indexErr) => {
            if (indexErr) {
              console.error('Error creating referral_code index:', indexErr);
            } else {
              console.log('✅ Added unique index for referral_code');
            }
          });
        }
      });
    }

    if (!columnNames.includes('referred_by')) {
      db.run("ALTER TABLE users ADD COLUMN referred_by TEXT", (err) => {
        if (err) {
          console.error('Error adding referred_by column:', err);
        } else {
          console.log('✅ Added referred_by column to users table');
        }
      });
    }

    if (!columnNames.includes('first_commission_paid')) {
      db.run("ALTER TABLE users ADD COLUMN first_commission_paid BOOLEAN DEFAULT 0", (err) => {
        if (err) {
          console.error('Error adding first_commission_paid column:', err);
        } else {
          console.log('✅ Added first_commission_paid column to users table');
        }
      });
    }

    if (!columnNames.includes('referred_by')) {
      db.run("ALTER TABLE users ADD COLUMN referred_by TEXT", (err) => {
        if (err) {
          console.error('Error adding referred_by column:', err);
        } else {
          console.log('✅ Added referred_by column to users table');
        }
      });
    }
  });
}

function insertDefaultPlans() {
  // Default plans are now handled by scripts/initDatabase.js to avoid duplicates
  // This section is removed to prevent duplicate plan creation
}

function insertDefaultPermissions() {
  const { v4: uuidv4 } = require('uuid');

  const defaultPermissions = [
    // Admin permissions
    { role: 'admin', permission: 'manage_users' },
    { role: 'admin', permission: 'manage_plans' },
    { role: 'admin', permission: 'view_all_streams' },
    { role: 'admin', permission: 'manage_system' },
    { role: 'admin', permission: 'unlimited_streaming' },
    { role: 'admin', permission: 'unlimited_storage' },

    // User permissions
    { role: 'user', permission: 'create_stream' },
    { role: 'user', permission: 'upload_video' },
    { role: 'user', permission: 'view_own_streams' },
    { role: 'user', permission: 'manage_profile' },

    // Moderator permissions
    { role: 'moderator', permission: 'view_all_streams' },
    { role: 'moderator', permission: 'moderate_content' },
    { role: 'moderator', permission: 'manage_profile' }
  ];

  defaultPermissions.forEach(perm => {
    db.run(
      `INSERT OR IGNORE INTO role_permissions (id, role, permission) VALUES (?, ?, ?)`,
      [uuidv4(), perm.role, perm.permission],
      (err) => {
        if (err) {
          console.error('Error inserting default permission:', err.message);
        }
      }
    );
  });
}

function checkIfUsersExist() {
  return new Promise((resolve, reject) => {
    db.get('SELECT COUNT(*) as count FROM users', [], (err, result) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(result.count > 0);
    });
  });
}

module.exports = {
  db,
  checkIfUsersExist
};