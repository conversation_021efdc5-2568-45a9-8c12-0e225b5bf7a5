-- Migration to add video processing columns
-- Run this to update existing database schema

-- Add processing status column
ALTER TABLE videos ADD COLUMN processing_status TEXT DEFAULT 'pending';

-- Add streaming ready path column  
ALTER TABLE videos ADD COLUMN streaming_ready_path TEXT;

-- Add original filepath column (to keep track of original file)
ALTER TABLE videos ADD COLUMN original_filepath TEXT;

-- Update existing videos to have 'completed' status if they're already compatible
UPDATE videos SET processing_status = 'completed' 
WHERE codec LIKE '%h264%' OR codec LIKE '%avc%';

-- Update existing videos to have 'pending' status if they need processing
UPDATE videos SET processing_status = 'pending' 
WHERE codec LIKE '%hevc%' OR codec LIKE '%h265%' OR codec LIKE '%vp9%' OR codec LIKE '%av1%' OR format = 'mkv';

-- Create index for faster processing queue queries
CREATE INDEX IF NOT EXISTS idx_videos_processing_status ON videos(processing_status);
CREATE INDEX IF NOT EXISTS idx_videos_created_at ON videos(created_at);
