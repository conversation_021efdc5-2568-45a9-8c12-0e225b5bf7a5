// Debug final untuk memahami masalah timezone
const { datetimeLocalToUTC } = require('./utils/timezone');

console.log('=== Debug Timezone Final (Fixed) ===');

// Test manual tanpa library
const input = '2025-06-02T06:00';
const timezone = 'Asia/Jakarta';

console.log(`Input: ${input} (${timezone})`);
console.log(`System timezone: ${Intl.DateTimeFormat().resolvedOptions().timeZone}`);

// Test 1: Apa yang terjadi dengan new Date()
const date1 = new Date(input);
console.log(`\n1. new Date('${input}'):`);
console.log(`   Result: ${date1}`);
console.log(`   ISO: ${date1.toISOString()}`);
console.log(`   Timezone offset: ${date1.getTimezoneOffset()} minutes`);

// Test 2: Manual calculation
console.log(`\n2. Manual calculation for WIB (UTC+7):`);
const date2 = new Date(input);
const wibOffsetMinutes = 7 * 60; // WIB = UTC+7
const utcTime = new Date(date2.getTime() - (wibOffsetMinutes * 60 * 1000));
console.log(`   Input as local: ${date2}`);
console.log(`   Subtract 7 hours: ${utcTime}`);
console.log(`   UTC ISO: ${utcTime.toISOString()}`);

// Test 3: Display test
console.log(`\n3. Display test with correct UTC:`);
const correctUTC = new Date('2025-06-01T23:00:00.000Z'); // Expected UTC
const displayDate = correctUTC.toLocaleDateString('en-US', {
  month: 'short',
  day: 'numeric',
  year: 'numeric',
  timeZone: 'Asia/Jakarta'
});
const displayTime = correctUTC.toLocaleTimeString('en-US', {
  hour: '2-digit',
  minute: '2-digit',
  hour12: false,
  timeZone: 'Asia/Jakarta'
});
console.log(`   Expected UTC: ${correctUTC}`);
console.log(`   Display: Starts ${displayDate} • ${displayTime}`);

// Test 4: What we actually need
console.log(`\n4. What we need:`);
console.log(`   Input: 2025-06-02T06:00 (user wants 6 AM on June 2 in Jakarta)`);
console.log(`   Should store as UTC: 2025-06-01T23:00:00.000Z (6 AM Jakarta = 11 PM UTC previous day)`);
console.log(`   Should display as: Starts Jun 2, 2025 • 06:00`);

// Test 5: Correct approach
console.log(`\n5. Correct approach:`);
// Treat input as naive datetime, then interpret in target timezone
const year = 2025, month = 6, day = 2, hour = 6, minute = 0;

// Create date in target timezone using a trick
const tempDate = new Date();
tempDate.setFullYear(year, month - 1, day); // month is 0-based
tempDate.setHours(hour, minute, 0, 0);

console.log(`   Temp date: ${tempDate}`);
console.log(`   Temp ISO: ${tempDate.toISOString()}`);

// Get the offset for Jakarta at this date
const jakartaTime = new Date(tempDate.toLocaleString('en-US', { timeZone: 'Asia/Jakarta' }));
const utcTime2 = new Date(tempDate.toLocaleString('en-US', { timeZone: 'UTC' }));
const offset = jakartaTime.getTime() - utcTime2.getTime();

console.log(`   Jakarta time: ${jakartaTime}`);
console.log(`   UTC time: ${utcTime2}`);
console.log(`   Offset: ${offset / (1000 * 60)} minutes`);

// Apply offset correctly
const finalUTC = new Date(tempDate.getTime() - offset);
console.log(`   Final UTC: ${finalUTC}`);
console.log(`   Final ISO: ${finalUTC.toISOString()}`);

// Test display
const finalDisplayDate = finalUTC.toLocaleDateString('en-US', {
  month: 'short',
  day: 'numeric',
  year: 'numeric',
  timeZone: 'Asia/Jakarta'
});
const finalDisplayTime = finalUTC.toLocaleTimeString('en-US', {
  hour: '2-digit',
  minute: '2-digit',
  hour12: false,
  timeZone: 'Asia/Jakarta'
});
console.log(`   Final display: Starts ${finalDisplayDate} • ${finalDisplayTime}`);

// Test 6: Using fixed function
console.log(`\n6. Using fixed datetimeLocalToUTC function:`);
const fixedResult = datetimeLocalToUTC(input, timezone);
console.log(`   Result: ${fixedResult}`);

if (fixedResult) {
  const fixedUTC = new Date(fixedResult);
  console.log(`   UTC Date: ${fixedUTC}`);

  const fixedDisplayDate = fixedUTC.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    timeZone: timezone
  });

  const fixedDisplayTime = fixedUTC.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
    timeZone: timezone
  });

  console.log(`   Fixed display: Starts ${fixedDisplayDate} • ${fixedDisplayTime}`);
}
