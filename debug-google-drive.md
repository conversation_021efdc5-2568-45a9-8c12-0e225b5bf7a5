# Google Drive Import Troubleshooting Guide

## Masalah: "Video file processing failed - file not found after download"

### Kemungkinan Penyebab:

1. **File Access Issues**
   - File tidak public
   - Google Drive API key tidak memiliki permission
   - File memerlukan authentication khusus

2. **File Format Issues**
   - File bukan video
   - File corrupted
   - File terlalu besar

3. **System Issues**
   - Disk space tidak cukup
   - Permission issues pada folder upload
   - Antivirus blocking file download

### Langkah Debugging:

#### 1. Cek File Google Drive
- Pastikan file dapat diakses public: https://drive.google.com/file/d/1BWWoHl-A4-3tKHDApjjizo4PTx7Ort6u/view
- Coba download manual untuk memastikan file valid
- Pastikan file adalah video (MP4, AVI, MOV, dll)

#### 2. Cek Google Drive API Key
```bash
# Test API key dengan script
GOOGLE_DRIVE_API_KEY=your_api_key node test-google-drive.js
```

#### 3. <PERSON>k Logs
Lihat console logs saat import untuk melihat:
- `[Google Drive] Starting download for file ID: ...`
- `[Google Drive] File metadata: ...`
- `[Google Drive] Download completed successfully`
- `[Google Drive Import] File verification successful`

#### 4. Cek Folder Permissions
```bash
# Pastikan folder upload dapat ditulis
ls -la public/uploads/videos/
```

### Solusi yang Telah Diimplementasikan:

1. **Enhanced Logging**: Logging detail di setiap step
2. **File Verification**: Cek file exists setelah download
3. **Better Error Messages**: Pesan error yang lebih spesifik
4. **Timing Fixes**: Delay untuk memastikan file fully written
5. **Progress Tracking**: Handle file size unknown
6. **API Error Handling**: Specific error untuk 403, 404, 401

### Cara Test:

1. Jalankan aplikasi dengan logging enabled
2. Coba import file Google Drive
3. Monitor console logs
4. Cek folder `public/uploads/videos/` untuk file yang didownload

### File yang Dimodifikasi:

- `utils/googleDriveService.js`: Enhanced download & verification
- `utils/videoProcessor.js`: Better file validation
- `app.js`: Improved import process with logging
- `test-google-drive.js`: Test script untuk debugging
