// Debug timezone issue dengan fungsi yang sudah diperbaiki
const { datetimeLocalToUTC, convertToUTC } = require('./utils/timezone');

console.log('=== Debug Timezone Issue (Fixed) ===');

// Test case: 2 Juni 2025 jam 6:00 WIB
const testInput = '2025-06-02T06:00';
const timezone = 'Asia/Jakarta';

console.log(`Input: ${testInput} (${timezone})`);

console.log('\n1. Test fixed backend conversion:');
const fixedUTC = convertToUTC(testInput, timezone);
console.log(`   Fixed UTC: ${fixedUTC.toISOString()}`);

const utcResult = datetimeLocalToUTC(testInput, timezone);
console.log(`   datetimeLocalToUTC result: ${utcResult}`);

console.log('\n2. Test display dengan UTC yang benar:');
if (utcResult) {
  const utcDate = new Date(utcResult);

  const displayDate = utcDate.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    timeZone: timezone
  });

  const displayTime = utcDate.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
    timeZone: timezone
  });

  console.log(`   Display: Starts ${displayDate} • ${displayTime}`);

  console.log('\n3. Test edit modal:');
  const formatter = new Intl.DateTimeFormat('sv-SE', {
    timeZone: timezone,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });

  const parts = formatter.formatToParts(utcDate);
  const year = parts.find(p => p.type === 'year').value;
  const month = parts.find(p => p.type === 'month').value;
  const day = parts.find(p => p.type === 'day').value;
  const hour = parts.find(p => p.type === 'hour').value;
  const minute = parts.find(p => p.type === 'minute').value;

  const editFormat = `${year}-${month}-${day}T${hour}:${minute}`;
  console.log(`   Edit format: ${editFormat}`);
}

console.log('\n=== Expected vs Actual ===');
console.log('Expected: Input 2025-06-02T06:00 (Asia/Jakarta)');
console.log('Expected: Store as UTC 2025-06-01T23:00:00.000Z');
console.log('Expected: Display as "Starts Jun 2, 2025 • 06:00"');
console.log('Expected: Edit as "2025-06-02T06:00"');
