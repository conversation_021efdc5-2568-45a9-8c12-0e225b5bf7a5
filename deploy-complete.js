#!/usr/bin/env node

/**
 * Complete Deployment Script for StreamOnPod
 * 
 * This script orchestrates the complete deployment process:
 * 1. Deploy all fixes to production
 * 2. Implement monitoring and alerting
 * 3. Run comprehensive test suite
 * 4. Start monitoring systems
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 StreamOnPod Complete Deployment Process\n');

// Configuration
const CONFIG = {
  LOG_FILE: './logs/complete-deployment.log',
  TIMEOUT: 300000, // 5 minutes per step
  STEPS: [
    {
      name: 'Pre-Deployment Health Check',
      command: 'node scripts/health-check.js --pre-deployment',
      required: true,
      description: 'Validate system health before deployment'
    },
    {
      name: 'Comprehensive Test Suite',
      command: 'npm run test:comprehensive',
      required: false,
      description: 'Run all bug fix tests'
    },
    {
      name: 'Production Deployment',
      command: 'npm run deploy:production',
      required: true,
      description: 'Deploy all fixes to production'
    },
    {
      name: 'Post-Deployment Validation',
      command: 'npm run test:deployment',
      required: true,
      description: 'Validate deployment success'
    },
    {
      name: 'Final Health Check',
      command: 'npm run health:check',
      required: true,
      description: 'Confirm system health after deployment'
    }
  ]
};

// Utility functions
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] [${level}] ${message}`;
  
  console.log(logEntry);
  
  // Ensure log directory exists
  const logDir = path.dirname(CONFIG.LOG_FILE);
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
  
  // Write to log file
  fs.appendFileSync(CONFIG.LOG_FILE, logEntry + '\n');
}

async function runStep(step, stepNumber, totalSteps) {
  log(`\n📋 Step ${stepNumber}/${totalSteps}: ${step.name}`, 'INFO');
  log(`Description: ${step.description}`, 'INFO');
  log(`Command: ${step.command}`, 'INFO');
  
  const startTime = Date.now();
  
  try {
    // Run the command
    const output = execSync(step.command, {
      timeout: CONFIG.TIMEOUT,
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    const duration = Date.now() - startTime;
    
    log(`✅ ${step.name} completed successfully (${duration}ms)`, 'SUCCESS');
    
    return {
      name: step.name,
      status: 'SUCCESS',
      duration,
      output: output.substring(0, 500) // Limit output size
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    
    log(`❌ ${step.name} failed: ${error.message}`, 'ERROR');
    
    if (step.required) {
      log(`🚨 Required step failed. Stopping deployment.`, 'ERROR');
      throw new Error(`Required step failed: ${step.name}`);
    }
    
    return {
      name: step.name,
      status: 'FAILED',
      duration,
      error: error.message,
      output: error.stdout || error.stderr || ''
    };
  }
}

function startMonitoringSystems() {
  log('\n📊 Starting monitoring systems...', 'INFO');
  
  try {
    // Start performance monitoring in background
    const performanceMonitor = spawn('node', ['scripts/performance-monitor.js'], {
      detached: true,
      stdio: 'ignore'
    });
    performanceMonitor.unref();
    
    // Start deployment monitoring in background
    const deploymentMonitor = spawn('node', ['monitor-deployment.js'], {
      detached: true,
      stdio: 'ignore'
    });
    deploymentMonitor.unref();
    
    log('✅ Monitoring systems started in background', 'SUCCESS');
    
    return true;
  } catch (error) {
    log(`❌ Failed to start monitoring systems: ${error.message}`, 'ERROR');
    return false;
  }
}

function generateDeploymentReport(results) {
  log('\n📊 DEPLOYMENT REPORT', 'INFO');
  log('=' .repeat(60), 'INFO');
  
  const stats = {
    total: results.length,
    successful: 0,
    failed: 0,
    totalDuration: 0
  };
  
  results.forEach(result => {
    stats.totalDuration += result.duration;
    if (result.status === 'SUCCESS') {
      stats.successful++;
    } else {
      stats.failed++;
    }
  });
  
  log(`Total Steps: ${stats.total}`, 'INFO');
  log(`✅ Successful: ${stats.successful}`, 'SUCCESS');
  log(`❌ Failed: ${stats.failed}`, stats.failed > 0 ? 'ERROR' : 'INFO');
  log(`⏱️  Total Duration: ${(stats.totalDuration / 1000).toFixed(2)}s`, 'INFO');
  
  log('\n📋 STEP DETAILS', 'INFO');
  log('-' .repeat(60), 'INFO');
  
  results.forEach((result, index) => {
    const status = result.status === 'SUCCESS' ? '✅' : '❌';
    const duration = (result.duration / 1000).toFixed(2);
    
    log(`${status} Step ${index + 1}: ${result.name} (${duration}s)`, 'INFO');
    
    if (result.status === 'FAILED') {
      log(`   Error: ${result.error}`, 'ERROR');
    }
  });
  
  return stats;
}

function displayNextSteps() {
  log('\n🎯 NEXT STEPS', 'INFO');
  log('=' .repeat(60), 'INFO');
  
  log('1. Start the application:', 'INFO');
  log('   npm run production', 'INFO');
  log('   # OR with PM2:', 'INFO');
  log('   pm2 start scripts/production-start.js --name streamonpod', 'INFO');
  
  log('\n2. Monitor the application:', 'INFO');
  log('   npm run monitor:performance  # Real-time performance monitoring', 'INFO');
  log('   npm run health:check         # System health check', 'INFO');
  log('   npm run logs:tail           # View application logs', 'INFO');
  
  log('\n3. Access the application:', 'INFO');
  log(`   ${process.env.BASE_URL || 'http://localhost:7575'}`, 'INFO');
  
  log('\n4. Review monitoring data:', 'INFO');
  log('   ./logs/performance-metrics.json  # Performance data', 'INFO');
  log('   ./logs/health-check-results.json # Health status', 'INFO');
  log('   ./logs/test-results.json         # Test results', 'INFO');
  
  log('\n5. Emergency procedures:', 'INFO');
  log('   npm run health:check        # Quick health check', 'INFO');
  log('   npm run logs:errors         # View error logs', 'INFO');
  log('   pm2 restart streamonpod     # Restart application', 'INFO');
}

// Main deployment function
async function main() {
  log('🚀 Starting complete StreamOnPod deployment process...', 'INFO');
  log(`📊 Total steps: ${CONFIG.STEPS.length}`, 'INFO');
  log(`⏱️  Timeout per step: ${CONFIG.TIMEOUT / 1000}s`, 'INFO');
  
  const startTime = Date.now();
  const results = [];
  
  try {
    // Run all deployment steps
    for (let i = 0; i < CONFIG.STEPS.length; i++) {
      const step = CONFIG.STEPS[i];
      const result = await runStep(step, i + 1, CONFIG.STEPS.length);
      results.push(result);
    }
    
    // Start monitoring systems
    const monitoringStarted = startMonitoringSystems();
    
    // Generate deployment report
    const stats = generateDeploymentReport(results);
    
    const totalDuration = Date.now() - startTime;
    
    // Determine overall success
    const overallSuccess = stats.failed === 0;
    
    if (overallSuccess) {
      log('\n🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!', 'SUCCESS');
      log('✅ All fixes deployed to production', 'SUCCESS');
      log('✅ Monitoring and alerting implemented', 'SUCCESS');
      log('✅ Comprehensive test suite executed', 'SUCCESS');
      log(`✅ Monitoring systems ${monitoringStarted ? 'started' : 'attempted'}`, 'SUCCESS');
      
      displayNextSteps();
      
    } else {
      log('\n❌ DEPLOYMENT COMPLETED WITH ERRORS!', 'ERROR');
      log('⚠️  Some steps failed - review the report above', 'ERROR');
      log('🔧 Fix the issues and re-run the deployment', 'ERROR');
    }
    
    log(`\n⏱️  Total deployment time: ${(totalDuration / 1000).toFixed(2)}s`, 'INFO');
    log(`📄 Full log available at: ${CONFIG.LOG_FILE}`, 'INFO');
    
    // Exit with appropriate code
    process.exit(overallSuccess ? 0 : 1);
    
  } catch (error) {
    log(`\n💥 DEPLOYMENT FAILED: ${error.message}`, 'ERROR');
    log('🔄 Check the logs and try again', 'ERROR');
    log(`📄 Log file: ${CONFIG.LOG_FILE}`, 'ERROR');
    
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  log('\n⚠️  Deployment interrupted by user', 'WARNING');
  process.exit(1);
});

process.on('SIGTERM', () => {
  log('\n⚠️  Deployment terminated', 'WARNING');
  process.exit(1);
});

// Run deployment if executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Deployment script error:', error);
    process.exit(1);
  });
}

module.exports = { main, runStep, startMonitoringSystems };
