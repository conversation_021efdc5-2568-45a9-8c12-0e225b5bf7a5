# Chunked Upload Implementation

## Overview

StreamOnPod now supports chunked file upload to overcome Cloudflare's 100MB file upload limit. This implementation automatically detects large files and uses chunked upload for files larger than 50MB.

## Features

- **Automatic Detection**: Files > 50MB automatically use chunked upload
- **Cloudflare Compatible**: 50MB chunks safely pass through Cloudflare's 100MB limit
- **Resume Support**: Failed chunks are automatically retried (up to 3 attempts)
- **Progress Tracking**: Real-time progress for both chunk upload and overall progress
- **Fallback Support**: Small files still use regular upload for better performance
- **Cleanup**: Automatic cleanup of expired upload sessions and orphaned chunks

## Technical Implementation

### Backend Components

1. **ChunkedUploadService** (`utils/chunkedUploadService.js`)
   - Manages upload sessions and metadata
   - Handles chunk processing and validation
   - Merges chunks into final video file
   - Automatic cleanup of expired sessions

2. **Upload Middleware** (`middleware/uploadMiddleware.js`)
   - Separate multer configurations for regular and chunked uploads
   - Chunk storage in temporary directory
   - File validation for both upload types

3. **API Routes** (`app.js`)
   - `POST /api/videos/upload/init` - Initialize chunked upload
   - `POST /api/videos/upload/chunk` - Upload individual chunk
   - `POST /api/videos/upload/finalize` - Merge chunks and create video
   - `GET /api/videos/upload/status/:uploadId` - Get upload status

### Frontend Components

1. **Automatic Detection** (`views/gallery.ejs`)
   - Files > 50MB trigger chunked upload
   - Files ≤ 50MB use regular upload

2. **Progress Tracking**
   - Per-chunk progress display
   - Overall upload percentage
   - Retry indicators for failed chunks

3. **Error Handling**
   - Automatic retry for failed chunks
   - User-friendly error messages
   - Graceful fallback to regular upload

## Configuration

### Environment Variables

```env
# Chunked Upload Configuration
CHUNK_SIZE=52428800                    # 50MB chunk size
CHUNKED_UPLOAD_THRESHOLD=52428800      # 50MB threshold for chunked upload
CHUNKED_UPLOAD_CLEANUP_INTERVAL=3600000 # 1 hour cleanup interval
```

### Chunk Size Considerations

- **50MB chunks**: Safe for Cloudflare's 100MB limit with buffer
- **Optimal performance**: Balance between upload speed and reliability
- **Memory efficient**: Server doesn't load entire file into memory

## File Structure

```
public/uploads/videos/
├── chunks/                    # Temporary chunk storage
│   ├── {uploadId}_0.chunk    # Individual chunks
│   ├── {uploadId}_1.chunk
│   └── ...
└── {finalFilename}           # Merged final video file
```

## Upload Flow

### Chunked Upload Process

1. **Initialize Upload**
   ```javascript
   POST /api/videos/upload/init
   {
     "filename": "large_video.mp4",
     "fileSize": 157286400,
     "totalChunks": 3
   }
   ```

2. **Upload Chunks Sequentially**
   ```javascript
   POST /api/videos/upload/chunk
   FormData: {
     chunk: Blob,
     uploadId: "uuid",
     chunkIndex: 0
   }
   ```

3. **Finalize Upload**
   ```javascript
   POST /api/videos/upload/finalize
   {
     "uploadId": "uuid"
   }
   ```

### Regular Upload Process

Files ≤ 50MB continue using the existing upload endpoint:
```javascript
POST /api/videos/upload
FormData: { video: File }
```

## Error Handling

### Chunk Upload Failures
- Automatic retry up to 3 attempts per chunk
- Exponential backoff between retries
- Clear error messages for permanent failures

### Session Management
- Upload sessions expire after 2 hours of inactivity
- Automatic cleanup of orphaned chunks
- Graceful handling of expired sessions

### Storage Quota
- Quota validation before upload initialization
- Real-time storage usage updates
- Proper cleanup on upload failures

## Benefits

1. **Cloudflare Compatibility**: Bypasses 100MB upload limit
2. **Improved Reliability**: Retry failed chunks instead of entire upload
3. **Better User Experience**: Progress tracking and error recovery
4. **Memory Efficiency**: Streaming chunk processing
5. **Backward Compatibility**: Existing small file uploads unchanged

## Monitoring

### Logs
- Upload initialization and completion
- Chunk processing status
- Error tracking and retry attempts
- Cleanup operations

### Metrics
- Upload success/failure rates
- Average chunk upload times
- Storage usage patterns
- Error frequency by type

## Security Considerations

1. **File Validation**: Same validation rules apply to chunks
2. **Session Security**: Upload IDs are UUIDs to prevent guessing
3. **Cleanup**: Automatic removal of temporary files
4. **Quota Enforcement**: Storage limits enforced at initialization

## Performance Optimizations

1. **Sequential Upload**: Prevents overwhelming server with parallel requests
2. **Efficient Merging**: Streaming file operations for large files
3. **Memory Management**: Minimal memory footprint during processing
4. **Cleanup Scheduling**: Background cleanup to maintain performance

## Future Enhancements

1. **Parallel Chunk Upload**: Upload multiple chunks simultaneously
2. **Resume Capability**: Resume interrupted uploads across sessions
3. **Compression**: Optional chunk compression for faster uploads
4. **Progress Persistence**: Save progress across browser refreshes
