# 🚀 Copy Mode Friendly Advanced Settings

## 📋 **Overview**

Fitur ini memungkinkan user untuk menggunakan advanced settings sambil tetap mempertahankan **copy mode** untuk performa optimal. Sistem akan secara otomatis membatasi dan menyesuaikan opsi advanced settings berdasarkan video yang dipilih agar tetap kompatibel dengan copy mode.

## 🎯 **Tujuan**

1. **Mempertahankan Copy Mode**: Selalu menggunakan copy mode untuk CPU usage minimal
2. **User Friendly**: Membatasi opsi yang tidak kompatibel secara otomatis
3. **Real-time Feedback**: Memberikan indikator visual tentang mode streaming
4. **Optimal Performance**: Mencegah re-encoding yang tidak perlu

## ⚙️ **Cara Kerja**

### 1. **Video Compatibility Detection**

Saat user memilih video, sistem akan:
- Menganalisis codec video (H.264 ✅, HEVC ❌, VP9 ❌, AV1 ❌)
- Memeriksa container format (MP4 ✅, MOV ✅, MKV ❌)
- Menghitung batas maksimal bitrate, resolution, dan FPS

### 2. **Dynamic Settings Adjustment**

Advanced settings akan disesuaikan berdasarkan video:
- **Bitrate**: Dibatasi hingga `video.bitrate * 1.4`
- **Resolution**: Dibatasi hingga `video.resolution * 1.1`
- **FPS**: Dibatasi hingga `video.fps * 1.1`

### 3. **Real-time Indicators**

User akan melihat indikator:
- 🟢 **Copy Mode**: Settings kompatibel dengan copy mode
- 🟠 **Re-encode**: Settings memerlukan re-encoding

## 🔧 **Implementation Details**

### **Backend Functions**

```javascript
// services/streamingService.js
function getCopyModeCompatibleSettings(video) {
  // Mengembalikan opsi settings yang kompatibel dengan copy mode
}

function needsReencoding(video, targetResolution, targetBitrate, targetFps) {
  // Menentukan apakah video perlu re-encoding
}
```

### **API Endpoint**

```javascript
GET /api/videos/:id/copy-mode-settings
// Mengembalikan settings yang kompatibel untuk video tertentu
```

### **Frontend Integration**

```javascript
// views/dashboard.ejs
async function checkCopyModeCompatibility(videoId) {
  // Mengecek kompatibilitas dan update UI
}

function updateAdvancedSettingsForCopyMode(compatibilityData) {
  // Menyesuaikan opsi advanced settings
}
```

## 📊 **Compatibility Matrix**

| Video Codec | Container | Copy Mode | Action |
|-------------|-----------|-----------|---------|
| H.264/AVC   | MP4/MOV   | ✅ Yes    | Limit settings to video specs |
| HEVC/H.265  | Any       | ❌ No     | Show re-encode warning |
| VP9         | Any       | ❌ No     | Show re-encode warning |
| AV1         | Any       | ❌ No     | Show re-encode warning |
| Any         | MKV       | ❌ No     | Show re-encode warning |

## 🎨 **UI Components**

### **Copy Mode Indicators**

```html
<!-- Green indicator for copy mode -->
<div id="copyModeIndicator" class="bg-green-600 text-green-100">
  <i class="ti ti-copy"></i>Copy Mode
</div>

<!-- Orange indicator for re-encoding -->
<div id="reencodeIndicator" class="bg-orange-600 text-orange-100">
  <i class="ti ti-settings"></i>Re-encode
</div>
```

### **Warning Messages**

```html
<!-- Warning for incompatible videos -->
<div id="copyModeWarning" class="bg-orange-600/20 border-orange-600/30">
  <i class="ti ti-alert-triangle"></i>
  Copy Mode Tidak Tersedia. Streaming akan menggunakan re-encoding mode.
</div>
```

## 🔄 **User Flow**

1. **Video Selection**: User memilih video dari gallery
2. **Compatibility Check**: Sistem mengecek kompatibilitas copy mode
3. **Settings Adjustment**: Advanced settings disesuaikan otomatis
4. **Visual Feedback**: Indikator menunjukkan mode streaming
5. **Stream Creation**: Stream dibuat dengan settings optimal

## 💡 **Benefits**

### **For Users**
- ✅ Tidak perlu khawatir tentang performa CPU
- ✅ Settings otomatis optimal untuk video yang dipilih
- ✅ Feedback visual yang jelas
- ✅ Tidak ada trial-and-error

### **For System**
- ✅ CPU usage minimal dengan copy mode
- ✅ Mencegah overload server
- ✅ Streaming lebih stabil
- ✅ Resource efficiency

## 🚀 **Future Enhancements**

1. **Auto Video Processing**: Convert incompatible videos to H.264
2. **Smart Recommendations**: Suggest optimal settings
3. **Batch Processing**: Process multiple videos for copy mode
4. **Advanced Analytics**: Track copy mode usage statistics

## 🔧 **Configuration**

```javascript
// Thresholds dapat disesuaikan di streamingService.js
const COPY_MODE_THRESHOLDS = {
  BITRATE_MARGIN: 1.4,    // 40% margin
  RESOLUTION_MARGIN: 1.1, // 10% margin  
  FPS_MARGIN: 1.1         // 10% margin
};
```

## 📝 **Testing**

### **Test Scenarios**

1. ✅ H.264 MP4 video → Copy mode available
2. ❌ HEVC video → Re-encode warning shown
3. ❌ MKV video → Re-encode warning shown
4. ✅ Settings within limits → Copy mode maintained
5. ❌ Settings exceed limits → Re-encode indicator

### **Manual Testing**

```bash
# Test API endpoint
curl -X GET "http://localhost:7575/api/videos/{videoId}/copy-mode-settings"

# Expected response for compatible video:
{
  "success": true,
  "compatible": true,
  "settings": {
    "maxBitrate": 3500,
    "maxFps": 33,
    "compatibleResolutions": [...],
    "bitrateOptions": [1000, 1500, 2000, 2500, 3000],
    "fpsOptions": [15, 20, 24, 25, 30]
  }
}
```

## 🎯 **Success Metrics**

- **Copy Mode Usage**: >90% streams menggunakan copy mode
- **CPU Usage**: Rata-rata <30% saat streaming
- **User Satisfaction**: Feedback positif tentang performa
- **Error Rate**: <5% streaming errors

---

**Status**: ✅ Implemented  
**Version**: 1.0  
**Last Updated**: January 2025
