# CPU Allocation Guide - StreamOnPod

## Overview

StreamOnPod now implements dynamic CPU allocation to prevent conflicts between video re-encoding and live streaming processes. The system automatically allocates CPU cores using a 50:50 split that scales with your server specifications.

## How It Works

### Dynamic 50:50 Allocation

The system automatically detects your server's CPU cores and splits them evenly:

- **Encoding Cores**: First half of available cores (for video re-encoding during upload)
- **Streaming Cores**: Second half of available cores (for live streaming with copy mode)

### Examples by Server Size

| Server Cores | Encoding Cores | Streaming Cores | Encoding Range | Streaming Range |
|--------------|----------------|-----------------|----------------|-----------------|
| 4 cores      | 2 cores        | 2 cores         | 0-1            | 2-3             |
| 6 cores      | 3 cores        | 3 cores         | 0-2            | 3-5             |
| 8 cores      | 4 cores        | 4 cores         | 0-3            | 4-7             |
| 12 cores     | 6 cores        | 6 cores         | 0-5            | 6-11            |
| 16 cores     | 8 cores        | 8 cores         | 0-7            | 8-15            |
| 24 cores     | 12 cores       | 12 cores        | 0-11           | 12-23           |

## Technical Implementation

### CPU Affinity (Linux Only)

On Linux systems, the application uses `taskset` to bind processes to specific CPU cores:

```bash
# Encoding process (using cores 0-3 on 8-core system)
taskset -c 0-3 ffmpeg [encoding_args]

# Streaming process (using cores 4-7 on 8-core system)  
taskset -c 4-7 ffmpeg [streaming_args]
```

### FFmpeg Threading

The system automatically configures FFmpeg threading parameters with correct argument order:

**For Encoding:**
```bash
# Input options (before -i)
-thread_queue_size 1024

# Output options (after -i)
-threads 4
-filter_threads 4
```

**For Streaming:**
```bash
# Input options (before -i)
-thread_queue_size 512

# Output options (after -i)
-threads 2
```

**Correct FFmpeg Command Structure:**
```bash
ffmpeg [input_options] -i input.mp4 [output_options] output.mp4
```

## Benefits

### 1. **Prevents CPU Conflicts**
- Re-encoding processes won't interfere with live streaming
- Live streams maintain consistent performance during uploads

### 2. **Scalable Architecture**
- Automatically adapts to server upgrades (8→12→16 cores)
- No manual configuration required

### 3. **Optimal Resource Usage**
- Encoding gets dedicated cores for faster processing
- Streaming gets dedicated cores for stable performance

### 4. **Future-Proof Design**
- Ready for server upgrades without code changes
- Maintains 50:50 balance regardless of core count

## Monitoring & Administration

### Admin Dashboard

Access CPU allocation information via admin endpoints:

```
GET /api/admin/cpu/allocation
GET /api/admin/cpu/scenarios
```

### Real-time Monitoring

The system provides:
- Current CPU allocation status
- Per-process core usage
- Performance recommendations
- Platform compatibility info

### Log Messages

Look for these log entries to confirm CPU allocation:

```
[CPUManager] Initialized with 8 cores:
[CPUManager] Encoding cores: 4 (0-3)
[CPUManager] Streaming cores: 4 (4-7)
[VideoProcessing] Using 4 threads for encoding (cores: 0-3)
[CPU] Using 2 threads on cores 4-7 for streaming
```

## Platform Support

### Linux (Full Support)
- ✅ CPU affinity with `taskset`
- ✅ Per-core process binding
- ✅ Real-time CPU monitoring

### Windows/macOS (Partial Support)
- ✅ FFmpeg threading configuration
- ❌ CPU affinity (not supported)
- ⚠️ Relies on OS scheduler

## Performance Impact

### Before CPU Allocation
- Video upload processing could slow down live streams
- Inconsistent streaming performance during encoding
- CPU contention between processes

### After CPU Allocation
- ✅ Stable streaming performance during uploads
- ✅ Faster video processing with dedicated cores
- ✅ Predictable resource usage

## Configuration

### Automatic Configuration
The system automatically configures itself based on detected CPU cores. No manual setup required.

### Environment Variables
```bash
# Optional: Enable verbose CPU logging
ENABLE_CPU_LOGGING=true

# Optional: Force specific core count (for testing)
FORCE_CPU_CORES=8
```

## Troubleshooting

### Check CPU Allocation Status
```bash
# View current allocation
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  http://localhost:7575/api/admin/cpu/allocation
```

### Verify Process Binding (Linux)
```bash
# Check which cores a process is using
ps -eo pid,psr,comm | grep ffmpeg
```

### Common Issues

1. **No CPU Affinity on Windows/macOS**
   - Expected behavior - system falls back to threading only
   - Performance still improved through dedicated thread allocation

2. **Single Core Systems**
   - Minimum 1 core allocated to each process type
   - May not see significant performance improvement

3. **High CPU Usage**
   - Check if both encoding and streaming are running simultaneously
   - Monitor per-core usage in admin dashboard

## Future Enhancements

### Planned Features
- Dynamic core reallocation based on workload
- GPU acceleration integration
- Advanced CPU monitoring dashboard
- Custom allocation profiles

### Hardware Acceleration
The CPU allocation system is designed to work alongside:
- NVENC (NVIDIA GPU encoding)
- QSV (Intel Quick Sync)
- VAAPI (Linux hardware acceleration)

## Best Practices

### Server Sizing
- **Minimum**: 4 cores (2 encoding + 2 streaming)
- **Recommended**: 8+ cores (4 encoding + 4 streaming)
- **Optimal**: 16+ cores (8 encoding + 8 streaming)

### Monitoring
- Check CPU allocation logs during startup
- Monitor streaming performance during video uploads
- Use admin dashboard for real-time status

### Upgrades
When upgrading server cores:
1. Restart the application
2. Verify new allocation in logs
3. Test encoding and streaming performance
4. Check admin dashboard for updated limits

## Support

For issues related to CPU allocation:
1. Check platform compatibility (Linux recommended)
2. Verify core count detection in logs
3. Test with admin API endpoints
4. Monitor FFmpeg process binding

The CPU allocation system is designed to be transparent and automatic, requiring no manual intervention while providing significant performance improvements for concurrent encoding and streaming operations.
