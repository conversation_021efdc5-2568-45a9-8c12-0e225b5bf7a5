# Load Balancing System

## Overview

StreamFlow now includes an intelligent load balancing system that automatically adjusts stream quality based on server CPU usage. This ensures optimal server performance and prevents overload during peak usage periods.

## Features

### 🔄 Automatic Quality Adjustment
- **Real-time CPU monitoring** every 10 seconds
- **Automatic quality downgrade** when CPU usage is high
- **Automatic quality upgrade** when CPU usage returns to normal
- **Gradual quality transitions** to prevent stream interruptions

### 📊 Quality Levels

| Quality Level | Resolution | Bitrate | FPS | CPU Threshold |
|---------------|------------|---------|-----|---------------|
| **NORMAL**    | 1280x720   | 4000k   | 30  | < 60%         |
| **MEDIUM**    | 720x480    | 2500k   | 30  | 60-75%        |
| **LOW**       | 480x360    | 1500k   | 30  | 75-85%        |
| **MINIMAL**   | 360x240    | 800k    | 24  | > 85%         |

### 🎛️ Admin Controls
- **Real-time monitoring dashboard**
- **Configurable CPU thresholds**
- **Manual quality override**
- **Enable/disable load balancing**
- **Activity logs and metrics**

### 🔔 User Notifications
- **Automatic notifications** when quality changes
- **Clear explanations** of quality adjustments
- **Non-intrusive UI** with auto-hide functionality

## How It Works

### 1. CPU Monitoring
```javascript
// Monitor CPU every 10 seconds
setInterval(async () => {
  const systemStats = await getSystemStats();
  const cpuUsage = systemStats.cpu.usage;
  await streamingService.applyLoadBalancing(cpuUsage);
}, 10000);
```

### 2. Quality Decision Logic
```javascript
function determineQualityLevel(cpuUsage) {
  if (cpuUsage >= 85) return 'MINIMAL';      // CPU > 85%
  if (cpuUsage >= 75) return 'LOW';          // CPU 75-85%
  if (cpuUsage >= 60) return 'MEDIUM';       // CPU 60-75%
  return 'NORMAL';                           // CPU < 60%
}
```

### 3. Stream Quality Adjustment
- **Graceful restart** of affected streams
- **Database update** with new quality settings
- **FFmpeg parameter adjustment** for optimal encoding
- **Cooldown period** to prevent frequent changes

## Configuration

### Default Settings
```javascript
const LOAD_BALANCE_CONFIG = {
  CPU_CHECK_INTERVAL: 10000,          // Check every 10 seconds
  QUALITY_CHANGE_COOLDOWN: 30000,     // Wait 30s between changes
  CPU_THRESHOLDS: {
    HIGH: 85,      // Minimal quality threshold
    MEDIUM: 75,    // Low quality threshold  
    LOW: 60        // Medium quality threshold
  }
};
```

### Admin Configuration
Access the load balancer settings via:
- **Admin Dashboard** → **Load Balancer**
- **Real-time configuration** updates
- **Threshold customization**
- **Monitoring interval adjustment**

## API Endpoints

### Status and Metrics
```bash
GET /api/load-balancer/status    # Get current status
GET /api/load-balancer/metrics   # Get performance metrics
```

### Control
```bash
POST /api/load-balancer/start    # Start load balancer
POST /api/load-balancer/stop     # Stop load balancer
POST /api/load-balancer/config   # Update configuration
```

### Manual Override
```bash
POST /api/load-balancer/quality
{
  "qualityLevel": "MEDIUM"  # NORMAL, MEDIUM, LOW, MINIMAL
}
```

## Benefits

### 🚀 Server Performance
- **Prevents CPU overload** during peak usage
- **Maintains system stability** under high load
- **Automatic resource optimization**

### 👥 User Experience
- **Continuous streaming** even during high load
- **Transparent quality adjustments**
- **Clear notifications** about changes

### 📈 Scalability
- **Handle more concurrent streams**
- **Efficient resource utilization**
- **Automatic load distribution**

## Monitoring

### Real-time Dashboard
- **Current CPU usage** with visual indicators
- **Active quality level** and recent changes
- **Number of active streams** affected
- **Historical metrics** and trends

### Activity Logs
```
[2024-01-15 10:30:15] Quality changed to MEDIUM due to CPU usage: 72%
[2024-01-15 10:30:45] Stream 123 restarted with 720x480 quality
[2024-01-15 10:31:15] Quality changed to NORMAL due to CPU usage: 55%
```

## Best Practices

### 1. Threshold Configuration
- **Conservative thresholds** for production environments
- **Test thoroughly** before deploying changes
- **Monitor system behavior** after adjustments

### 2. Stream Management
- **Use advanced settings** for load balancing eligibility
- **Monitor stream quality** during peak hours
- **Plan capacity** based on usage patterns

### 3. User Communication
- **Inform users** about automatic quality adjustments
- **Provide manual quality controls** when needed
- **Document quality levels** and their purposes

## Troubleshooting

### Common Issues

#### Load Balancer Not Starting
```bash
# Check logs for errors
tail -f logs/app.log | grep LoadBalancer

# Verify system monitor is working
curl http://localhost:7575/api/system-stats
```

#### Quality Not Changing
- **Check CPU thresholds** in admin panel
- **Verify streams use advanced settings**
- **Check cooldown periods**

#### Frequent Quality Changes
- **Increase cooldown period**
- **Adjust CPU thresholds**
- **Monitor system stability**

## Future Enhancements

### Planned Features
- **Memory-based load balancing**
- **Network bandwidth consideration**
- **Machine learning optimization**
- **Multi-server load distribution**
- **Custom quality profiles**

### Advanced Configurations
- **Per-user quality limits**
- **Time-based quality schedules**
- **Platform-specific optimizations**
- **Hardware acceleration integration**

## Technical Details

### Architecture
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   System        │    │   Load           │    │   Streaming     │
│   Monitor       │───▶│   Balancer       │───▶│   Service       │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   CPU/Memory    │    │   Quality        │    │   FFmpeg        │
│   Metrics       │    │   Decision       │    │   Processes     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Performance Impact
- **Minimal overhead** from monitoring
- **Efficient quality transitions**
- **Optimized resource usage**
- **Scalable architecture**

---

For more information, visit the [StreamFlow Documentation](../README.md) or contact support.
