# 🔄 Sistem Prorated Upgrade StreamOnPod

## 📋 Overview

Sistem Prorated Upgrade adalah solusi yang adil untuk menangani upgrade plan subscription yang mencegah eksploitasi dan memberikan nilai yang tepat kepada user berdasarkan sisa waktu aktif plan mereka.

## 🎯 Tujuan

1. **Mencegah Eksploitasi**: User tidak bisa membeli plan murah lalu upgrade ke plan mahal untuk mendapat durasi lebih panjang dengan harga lebih murah
2. **Adil untuk User**: User tidak kehilangan nilai yang sudah dibayar dari plan lama
3. **Transparan**: Perhitungan jelas dan mudah dipahami
4. **Konsisten**: Masa aktif tetap sampai tanggal yang sama

## 🧮 Cara Kerja

### Rumus Perhitungan Prorated

```
1. Hitung nilai sisa hari dari plan lama:
   Nilai Sisa = (Harga Plan Lama / 30) × Si<PERSON> Hari

2. Hitung harga upgrade:
   Harga Upgrade = Harga Plan Baru - <PERSON>lai Sisa

3. <PERSON>ung biaya admin (1%):
   Biaya Admin = Harga Upgrade × 0.01

4. Total pembayaran:
   Total = Harga Upgrade + Biaya Admin
```

### Contoh Perhitungan

**Skenario**: User memiliki plan PodLite (Rp 24.900) dengan sisa 20 hari, ingin upgrade ke PodPrime (Rp 99.900)

```
1. Nilai sisa plan lama:
   (24.900 / 30) × 20 = 16.600

2. Harga upgrade:
   99.900 - 16.600 = 83.300

3. Biaya admin (1%):
   83.300 × 0.01 = 833

4. Total pembayaran:
   83.300 + 833 = 84.133
```

**Hasil**: User bayar Rp 84.133 dan mendapat plan PodPrime sampai tanggal expire yang sama.

## 🏗️ Implementasi Teknis

### 1. Model Subscription (models/Subscription.js)

#### Fungsi `calculateProratedUpgrade(userId, newPlanId)`

```javascript
// Menghitung perhitungan prorated upgrade
static async calculateProratedUpgrade(userId, newPlanId) {
  // 1. Ambil subscription aktif user
  // 2. Ambil detail plan lama dan baru
  // 3. Validasi upgrade (tidak boleh downgrade)
  // 4. Hitung sisa hari
  // 5. Hitung nilai sisa, harga upgrade, biaya admin
  // 6. Return object dengan semua detail perhitungan
}
```

#### Fungsi `createProratedUpgrade(userId, newPlanId, paymentId)`

```javascript
// Membuat subscription baru dengan logika prorated
static async createProratedUpgrade(userId, newPlanId, paymentId) {
  // 1. Ambil subscription aktif (jika ada)
  // 2. Untuk upgrade: gunakan tanggal expire yang sama
  // 3. Untuk subscription baru: 30 hari dari sekarang
  // 4. Mark subscription lama sebagai 'upgraded'
  // 5. Buat subscription baru
  // 6. Update user plan limits
}
```

### 2. Payment Routes (routes/payment.js)

#### Endpoint `POST /payment/calculate-upgrade`

```javascript
// Menghitung prorated upgrade sebelum pembayaran
router.post('/calculate-upgrade', isAuthenticated, async (req, res) => {
  const { planId } = req.body;
  const calculation = await Subscription.calculateProratedUpgrade(req.session.userId, planId);
  res.json({ success: true, calculation });
});
```

#### Modifikasi `POST /payment/create`

```javascript
// Menggunakan total payment dari perhitungan prorated (termasuk admin fee)
const proratedCalculation = await Subscription.calculateProratedUpgrade(userId, planId);
const amountIDR = proratedCalculation.totalPayment; // Sudah termasuk admin fee 1%
```

### 3. Frontend Handler (public/js/prorated-upgrade.js)

#### Class `ProratedUpgradeHandler`

```javascript
class ProratedUpgradeHandler {
  // Menghitung upgrade
  async calculateUpgrade(planId)
  
  // Menampilkan perhitungan di UI
  displayCalculation(calculation, containerId)
  
  // Memproses pembayaran
  async processPayment(planId)
  
  // Menampilkan modal konfirmasi
  showUpgradeModal(calculation, onConfirm)
  
  // Handle klik tombol upgrade
  async handleUpgradeClick(planId, planName)
}
```

## 🎨 UI Components

### 1. Calculation Display

```html
<div class="upgrade-calculation">
  <h4>📊 Perhitungan Upgrade Plan</h4>
  <div class="calculation-details">
    <!-- Current plan info -->
    <!-- New plan info -->
    <!-- Calculation breakdown -->
    <!-- Benefits -->
  </div>
</div>
```

### 2. Styling (public/css/prorated-upgrade.css)

- Responsive design
- Clear visual hierarchy
- Color coding untuk savings (hijau) dan total (orange)
- Smooth animations
- Mobile-friendly layout

## 🔒 Keamanan & Validasi

### 1. Validasi Server-Side

```javascript
// Mencegah downgrade
if (newPlan.price <= currentPlan.price) {
  throw new Error('Cannot downgrade or select same plan');
}

// Validasi plan exists
if (!currentPlan || !newPlan) {
  throw new Error('Plan not found');
}

// Validasi user ownership
if (transaction.user_id !== req.session.userId) {
  return res.status(403).json({ error: 'Access denied' });
}
```

### 2. Pencegahan Eksploitasi

- **Tidak boleh downgrade**: User hanya bisa upgrade ke plan yang lebih mahal
- **Prorated calculation**: Nilai sisa plan lama dipotong dari harga plan baru
- **Consistent end date**: Masa aktif tetap sampai tanggal yang sama
- **Admin fee**: Biaya admin 1% ditambahkan ke harga upgrade (bukan plan price)

## 📊 Contoh Skenario

### Skenario 1: Upgrade Normal
- **Current**: PodLite (Rp 24.900), sisa 20 hari
- **Target**: PodFlow (Rp 49.900)
- **Calculation**: 49.900 - 16.600 = 33.300 + 333 (admin) = **Rp 33.633**

### Skenario 2: Upgrade Premium
- **Current**: PodLite (Rp 24.900), sisa 20 hari  
- **Target**: PodPrime (Rp 99.900)
- **Calculation**: 99.900 - 16.600 = 83.300 + 833 (admin) = **Rp 84.133**

### Skenario 3: Subscription Baru
- **Current**: Preview (gratis)
- **Target**: PodFlow (Rp 49.900)
- **Calculation**: 49.900 + 499 (admin) = **Rp 50.399**

## 🚀 Deployment

### 1. Database Migration

Tidak diperlukan perubahan schema database. Sistem menggunakan tabel yang sudah ada:
- `user_subscriptions`
- `subscription_plans`
- `transactions`

### 2. Environment Variables

```env
# Midtrans configuration (sudah ada)
MIDTRANS_SERVER_KEY=your_server_key
MIDTRANS_CLIENT_KEY=your_client_key
MIDTRANS_IS_PRODUCTION=false
```

### 3. File Dependencies

```
models/Subscription.js          # ✅ Updated
routes/payment.js              # ✅ Updated  
routes/subscription.js         # ✅ Updated
public/js/prorated-upgrade.js  # ✅ New
public/css/prorated-upgrade.css # ✅ New
```

## 🧪 Testing

### 1. Unit Tests

```javascript
// Test prorated calculation
describe('Prorated Upgrade Calculation', () => {
  test('should calculate correct upgrade price', async () => {
    const calculation = await Subscription.calculateProratedUpgrade(userId, newPlanId);
    expect(calculation.upgradePrice).toBe(expectedPrice);
    expect(calculation.adminFee).toBe(expectedAdminFee);
    expect(calculation.totalPayment).toBe(expectedTotal);
  });
});
```

### 2. Integration Tests

```javascript
// Test payment flow
describe('Prorated Payment Flow', () => {
  test('should create payment with correct amount', async () => {
    const response = await request(app)
      .post('/payment/create')
      .send({ planId: 'test-plan-id' });
    
    expect(response.body.amount_idr).toBe(expectedTotalWithAdminFee);
  });
});
```

## 📈 Monitoring & Analytics

### 1. Metrics to Track

- Upgrade conversion rate
- Average upgrade value
- Admin fee collection
- User satisfaction with prorated pricing

### 2. Logging

```javascript
console.log('🧮 Prorated calculation:', {
  userId,
  currentPlan: currentPlan.name,
  newPlan: newPlan.name,
  remainingDays,
  savings: remainingValue,
  upgradePrice,
  adminFee,
  totalPayment
});
```

## 🔧 Maintenance

### 1. Regular Checks

- Monitor admin fee collection
- Verify calculation accuracy
- Check for edge cases
- User feedback analysis

### 2. Future Enhancements

- Support untuk annual plans
- Bulk upgrade discounts
- Loyalty program integration
- Advanced analytics dashboard

## 📞 Support

Untuk pertanyaan atau issues terkait sistem prorated upgrade:

1. Check logs untuk error details
2. Verify calculation manually
3. Test dengan different scenarios
4. Contact development team jika diperlukan

## 🎯 **Update Terbaru - Perbaikan UI & UX**

### ✅ **Perbaikan yang Telah Diimplementasikan:**

#### **1. Informasi Biaya Admin 1% yang Transparan**
- **Harga plan tetap ditampilkan tanpa admin fee** untuk menghindari kesan mahal
- **Informasi "+ biaya admin 1%"** ditampilkan di bawah harga plan
- **Breakdown detail biaya admin** ditampilkan saat preview harga dan di FAQ
- **Transparansi penuh** dalam perhitungan tanpa menyembunyikan biaya

#### **2. Tombol yang Tepat untuk Upgrade/Downgrade**
- **"Upgrade Plan"** untuk plan yang lebih mahal
- **"Downgrade (Tidak Tersedia)"** untuk plan yang lebih murah (disabled dengan warna merah)
- **"Ganti Plan"** untuk plan dengan harga sama
- **"Subscribe"** untuk user baru tanpa subscription

#### **3. Preview Harga Prorated Langsung di Plan Card**
- **Tombol "💰 Lihat Harga untuk Anda"** untuk melihat harga prorated
- **Preview muncul langsung di plan card** tanpa perlu modal
- **Breakdown detail** menampilkan:
  - Harga plan baru
  - Kredit dari plan lama (dengan sisa hari)
  - Harga upgrade setelah kredit
  - Biaya admin 1%
  - Total pembayaran final
  - Informasi penghematan

#### **4. FAQ Section yang Informatif**
- **"Bagaimana sistem harga upgrade bekerja?"** dengan contoh perhitungan
- **"Apa itu biaya admin 1%?"** dengan penjelasan transparansi
- **Contoh visual** dengan breakdown harga yang jelas

### 🎨 **Tampilan UI yang Diperbaiki:**

```
┌─────────────────────────────────────┐
│ PodPrime Plan                       │
│ Rp 99.900/bulan                    │
│ + biaya admin 1%                   │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 💰 Harga untuk Anda:           │ │
│ │ Plan PodPrime: Rp 99.900       │ │
│ │ Kredit plan lama (20 hari): -Rp│ │
│ │ 16.600                          │ │
│ │ Harga upgrade: Rp 83.300       │ │
│ │ Biaya admin (1%): Rp 833       │ │
│ │ ─────────────────────────────── │ │
│ │ Total Pembayaran: Rp 84.133    │ │
│ │ 💰 Hemat Rp 16.600 dari plan   │ │
│ │ lama!                           │ │
│ └─────────────────────────────────┘ │
│                                     │
│ [💰 Lihat Harga untuk Anda]        │
│ [Upgrade Plan]                      │
└─────────────────────────────────────┘
```

### 🔧 **File yang Dimodifikasi:**

1. **`views/subscription/plans.ejs`** ✅
   - Tambah CSS styling untuk prorated preview
   - Tambah logic tombol upgrade/downgrade yang tepat
   - Tambah preview harga prorated di plan card
   - Tambah FAQ section dengan informasi biaya admin
   - Tambah JavaScript untuk handle preview dan action

2. **`routes/subscription.js`** ✅
   - Tambah route `/upgrade-demo` untuk testing

### 🧪 **Testing:**

1. **Akses halaman plans**: `/subscription/plans`
2. **Klik "💰 Lihat Harga untuk Anda"** pada plan yang ingin di-upgrade
3. **Lihat preview harga** yang muncul dengan breakdown detail
4. **Klik "Upgrade Plan"** untuk melanjutkan ke pembayaran
5. **Baca FAQ section** untuk memahami sistem pricing

### 📱 **Demo Page:**

Akses `/subscription/upgrade-demo` untuk melihat demo lengkap sistem prorated upgrade.

## 🌐 **Update Terbaru - Dukungan Multi-bahasa**

### ✅ **Perbaikan yang Telah Diimplementasikan:**

#### **1. FAQ Section Multi-bahasa**
- **FAQ "Bagaimana sistem harga upgrade bekerja?"** → **"How does the upgrade pricing system work?"**
- **FAQ "Apa itu biaya admin 1%?"** → **"What is the 1% admin fee?"**
- **Contoh perhitungan** dalam bahasa Indonesia dan Inggris
- **Terminologi yang konsisten** untuk kedua bahasa

#### **2. UI Elements Multi-bahasa**
- **"+ biaya admin 1%"** → **"+ 1% admin fee"**
- **"💰 Lihat Harga untuk Anda"** → **"💰 See Your Price"**
- **"💰 Harga untuk Anda:"** → **"💰 Your Price:"**
- **"⏳ Menghitung..."** → **"⏳ Calculating..."**
- **"✅ Harga Dihitung"** → **"✅ Price Calculated"**

#### **3. Prorated Preview Multi-bahasa**

**Bahasa Indonesia:**
```
💰 Harga untuk Anda:
Plan PodPrime: Rp 99.900
Kredit plan lama (20 hari): -Rp 16.600
Harga upgrade: Rp 83.300
Biaya admin (1%): Rp 833
─────────────────────────────
Total Pembayaran: Rp 84.133
💰 Hemat Rp 16.600 dari plan lama!
```

**English:**
```
💰 Your Price:
PodPrime Plan: IDR 99,900
Old plan credit (20 days): -IDR 16,600
Upgrade price: IDR 83,300
Admin fee (1%): IDR 833
─────────────────────────────
Total Payment: IDR 84,133
💰 Save IDR 16,600 from your old plan!
```

#### **4. Error Messages Multi-bahasa**
- **"Gagal menghitung harga:"** → **"Failed to calculate price:"**
- **Consistent error handling** untuk kedua bahasa

### 🔧 **Technical Implementation:**

```javascript
// Dynamic language detection
const locale = '<%= locale %>';

// Conditional text rendering
previewBtn.textContent = locale === 'id' ?
  '⏳ Menghitung...' : '⏳ Calculating...';

// Template-based content generation
if (locale === 'id') {
  content = `Indonesian content...`;
} else {
  content = `English content...`;
}
```

### 📋 **FAQ Content Comparison:**

| **Bahasa Indonesia** | **English** |
|---------------------|-------------|
| Sistem Prorated Upgrade | Prorated Upgrade System |
| Anda mendapat kredit dari sisa hari plan lama | You get credit from remaining days of your old plan |
| Kredit dipotong dari harga plan baru | Credit is deducted from the new plan price |
| Biaya admin 1% ditambahkan ke harga upgrade | 1% admin fee is added to the upgrade price |
| Masa aktif tetap sampai tanggal yang sama | Active period remains until the same expiry date |

### 🎯 **Benefits:**

1. **User Experience**: User dapat memahami sistem dalam bahasa yang mereka kuasai
2. **Accessibility**: Lebih inklusif untuk user internasional
3. **Consistency**: Terminologi yang konsisten di seluruh aplikasi
4. **Professional**: Tampilan yang lebih profesional dengan dukungan multi-bahasa

---

**Status**: ✅ Implemented and Ready for Production
**Last Updated**: January 2025
**Version**: 1.2.0 (Updated with Multi-language Support)
