# 🚨 Sistem Penanganan Subscription Expired & Slot Limit

## 📋 Overview

Sistem ini menangani skenario kritis ketika user sedang live streaming tapi subscription expired atau melebihi slot limit. Sistem akan secara otomatis menghentikan stream dan mendowngrade user untuk mencegah penyalahgunaan.

## 🎯 Skenario yang Ditangani

### **Skenario Kritis:**
1. **User sedang live streaming** (FFmpeg berjalan)
2. **Subscription expired** pada saat streaming
3. **Slot tidak tersedia** karena downgrade ke Preview plan (0 slots)
4. **FFmpeg process tetap berjalan** tanpa penanganan

### **Masalah Tanpa Sistem Ini:**
- ❌ User bisa streaming gratis setelah subscription expired
- ❌ FFmpeg process orphaned dan menghabiskan resource server
- ❌ User bisa exploit dengan tidak memperpanjang subscription
- ❌ Server overload karena stream tidak terkontrol

## 🛠️ Solusi yang Diimplementasikan

### **1. Background Subscription Monitor**
```javascript
// services/subscriptionMonitor.js
class SubscriptionMonitor {
  - <PERSON><PERSON><PERSON><PERSON> setiap 5 menit (configurable)
  - Mengecek expired subscriptions
  - Menghentikan stream aktif user yang expired
  - Enforce slot limits untuk semua user
  - Mengirim notifikasi ke user dan admin
}
```

### **2. Enhanced Subscription Model**
```javascript
// models/Subscription.js
- checkAndHandleExpiredSubscriptions() // Background job
- enforceSlotLimits() // Force stop excess streams
- deleteAllUserStreamsForPreviewPlan() // Stop & delete streams
```

### **3. Real-time Notifications**
```javascript
// services/notificationService.js
- notifySubscriptionExpired()
- notifyStreamsStoppedDueToSlotLimit()
- notifyStreamStoppedDueToExpiry()
```

## 🔄 Alur Kerja Sistem

### **Background Monitor (Setiap 5 Menit):**

```
1. 🔍 Check Expired Subscriptions
   ├── Query database untuk subscription expired
   ├── Loop setiap user yang expired
   └── Jalankan handleExpiredSubscription()

2. 🛑 Handle Expired Subscription
   ├── Stop semua active streams user
   ├── Delete semua streams user  
   ├── Downgrade ke Preview plan (0 slots)
   ├── Mark subscription sebagai 'expired'
   └── Send notification ke user

3. ⚖️ Enforce Slot Limits
   ├── Get semua active streams
   ├── Group by user_id
   ├── Check slot limit setiap user
   ├── Stop excess streams (oldest first)
   └── Send notification jika ada stream dihentikan

4. 📢 Send Notifications
   ├── User notification (subscription expired)
   ├── Admin notification (summary)
   └── Real-time updates via Socket.IO
```

### **Contoh Skenario Real:**

```
User: John (PodFlow plan, 3 slots, expires 2025-01-15 10:00)
Streams: 2 active streams sedang live
Time: 2025-01-15 10:05 (5 menit setelah expired)

Background Monitor Check:
1. ✅ Detect subscription expired
2. 🛑 Stop 2 active streams (FFmpeg SIGTERM)
3. 🗑️ Delete streams dari database  
4. 📉 Downgrade John ke Preview plan (0 slots)
5. 📧 Send notification: "Subscription expired, streams stopped"
6. ✅ John tidak bisa streaming lagi sampai renew
```

## 🚀 Fitur Admin Dashboard

### **Subscription Monitor Page** (`/admin/subscription-monitor`)

#### **Real-time Statistics:**
- Monitor status (Running/Stopped)
- Total checks performed
- Expired subscriptions processed
- Streams stopped (expiry + slot limit)

#### **Configuration:**
- Check interval (1-60 minutes)
- Start/Stop monitor
- Force manual check

#### **Emergency Actions:**
- Emergency stop user streams
- Check specific user subscription
- Real-time activity log

#### **API Endpoints:**
```
GET  /admin/api/subscription-monitor/stats
POST /admin/api/subscription-monitor/force-check
POST /admin/api/subscription-monitor/toggle
POST /admin/api/subscription-monitor/interval
POST /admin/api/subscription-monitor/emergency-stop/:userId
GET  /admin/api/subscription-monitor/user/:userId
```

## 🔒 Keamanan & Validasi

### **Pencegahan Eksploitasi:**
1. **Background monitoring** - Tidak bergantung pada user action
2. **Force stop FFmpeg** - SIGTERM → SIGKILL jika perlu
3. **Database consistency** - Update status dan delete streams
4. **Notification system** - Transparansi untuk user dan admin
5. **Admin controls** - Emergency stop dan monitoring

### **Validasi Multi-layer:**
```
Layer 1: Quota Middleware (Real-time check saat API call)
Layer 2: Background Monitor (Periodic enforcement)
Layer 3: Admin Dashboard (Manual intervention)
```

## 📊 Monitoring & Logging

### **Metrics Tracked:**
- Total background checks
- Expired subscriptions processed
- Streams stopped due to expiry
- Streams stopped due to slot limit
- Last check timestamps
- Monitor uptime

### **Logging:**
```javascript
console.log('🔍 Checking for expired subscriptions...');
console.log('⚠️ Found 3 expired subscriptions');
console.log('🛑 Stopping active stream 123 due to subscription expiry');
console.log('📉 User 456 downgraded to Preview plan');
console.log('✅ Processed 3 expired subscriptions');
```

## 🚨 Emergency Procedures

### **Manual Intervention:**
1. **Emergency Stop User:**
   ```
   POST /admin/api/subscription-monitor/emergency-stop/USER_ID
   Body: { "reason": "Emergency stop reason" }
   ```

2. **Force Check:**
   ```
   POST /admin/api/subscription-monitor/force-check
   ```

3. **Check User Status:**
   ```
   GET /admin/api/subscription-monitor/user/USER_ID
   ```

### **Troubleshooting:**

#### **Monitor Not Running:**
```bash
# Check logs
tail -f logs/app.log | grep "SubscriptionMonitor"

# Restart via admin dashboard
POST /admin/api/subscription-monitor/toggle
Body: { "action": "start" }
```

#### **Streams Not Stopping:**
```bash
# Emergency stop specific user
curl -X POST /admin/api/subscription-monitor/emergency-stop/USER_ID \
  -H "Content-Type: application/json" \
  -d '{"reason":"Manual intervention"}'

# Check FFmpeg processes
tasklist | findstr ffmpeg  # Windows
ps aux | grep ffmpeg       # Linux
```

## 🔧 Configuration

### **Environment Variables:**
```env
# Subscription monitor interval (minutes)
SUBSCRIPTION_CHECK_INTERVAL=5

# Enable/disable background monitoring
ENABLE_SUBSCRIPTION_MONITOR=true

# Notification settings
ENABLE_EXPIRY_NOTIFICATIONS=true
```

### **Default Settings:**
- Check interval: 5 minutes
- Auto-start on app launch: Yes
- Notification enabled: Yes
- Emergency stop timeout: 5 seconds

## 📈 Performance Impact

### **Resource Usage:**
- **CPU**: Minimal (hanya saat check, ~1-2% spike setiap 5 menit)
- **Memory**: ~10MB untuk monitor service
- **Database**: 1-3 queries per check cycle
- **Network**: Minimal (hanya untuk notifications)

### **Optimization:**
- Batch processing untuk multiple users
- Efficient database queries dengan indexes
- Async operations untuk stream stopping
- Rate limiting untuk notifications

## 🧪 Testing

### **Test Scenarios:**
1. **Expired Subscription:**
   - Create user dengan subscription expired
   - Start stream
   - Wait for background check
   - Verify stream stopped dan user downgraded

2. **Slot Limit Exceeded:**
   - User dengan 1 slot limit
   - Start 2 streams
   - Wait for enforcement
   - Verify 1 stream stopped

3. **Emergency Stop:**
   - User dengan active streams
   - Trigger emergency stop
   - Verify all streams stopped immediately

### **Manual Testing:**
```javascript
// Force expire subscription
UPDATE user_subscriptions 
SET end_date = datetime('now', '-1 hour') 
WHERE user_id = 'test-user-id';

// Force check
await subscriptionMonitor.forceCheck();

// Verify results
SELECT * FROM streams WHERE user_id = 'test-user-id';
SELECT * FROM users WHERE id = 'test-user-id';
```

## 🎯 Benefits

### **Untuk Business:**
- ✅ Mencegah streaming gratis setelah expired
- ✅ Kontrol resource server yang ketat
- ✅ Enforcement subscription policy otomatis
- ✅ Monitoring dan reporting yang detail

### **Untuk User:**
- ✅ Notifikasi jelas saat subscription expired
- ✅ Transparansi dalam enforcement
- ✅ Tidak ada surprise stream termination
- ✅ Clear path untuk renewal

### **Untuk Admin:**
- ✅ Real-time monitoring dashboard
- ✅ Emergency controls
- ✅ Detailed activity logs
- ✅ Automated enforcement

---

**Status**: ✅ Implemented and Production Ready
**Last Updated**: January 2025
**Version**: 1.0.0
