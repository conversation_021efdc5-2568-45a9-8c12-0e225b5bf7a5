# Video Processing System

## Overview

The Video Processing System automatically converts uploaded videos to "streaming-ready" format to ensure optimal CPU performance during live streaming. This system processes videos in the background using a job queue and converts them to H.264 format with optimized settings.

## Key Benefits

- **🚀 Optimal CPU Performance**: All streaming uses copy mode (`-c:v copy -c:a copy`) for minimal CPU usage
- **📺 Consistent Quality**: All videos are standardized to H.264 format with optimal streaming settings
- **⚡ Background Processing**: Videos are processed asynchronously without blocking uploads
- **🔄 Automatic Retry**: Failed processing jobs are automatically retried
- **📊 Status Monitoring**: Real-time processing status and queue monitoring

## How It Works

### 1. Upload Process
1. User uploads video (MP4/MOV only)
2. Video metadata is extracted
3. Video is added to processing queue
4. User can immediately see the video in gallery with "Pending" status

### 2. Background Processing
1. Processing service checks if video needs conversion
2. Videos are converted to streaming-ready format:
   - **Codec**: H.264 (libx264)
   - **Max Bitrate**: 3 Mbps
   - **Audio**: AAC 128k stereo
   - **Format**: MP4 with web optimization
3. Processed video is saved alongside original
4. Database is updated with streaming-ready path

### 3. Streaming
1. Streaming service automatically uses processed version if available
2. Falls back to original if processed version is missing
3. Always uses copy mode for processed videos (optimal CPU usage)

## Processing Status

Videos can have the following processing statuses:

- **`pending`**: Waiting in processing queue
- **`processing`**: Currently being processed
- **`completed`**: Successfully processed and streaming-ready
- **`failed`**: Processing failed (can be retried)

## Video Compatibility

### Videos that DON'T need processing (already streaming-ready):
- H.264/AVC codec
- MP4 container
- Bitrate ≤ 5 Mbps
- Resolution ≤ 1080p

### Videos that NEED processing:
- HEVC/H.265 codec
- VP9, AV1 codecs
- MKV container format
- High bitrate (> 5 Mbps)
- High resolution (> 1080p)
- Unknown/unsupported codecs

## API Endpoints

### Get Processing Status
```
GET /api/video-processing/status
```

Returns queue status and processing statistics:
```json
{
  "success": true,
  "queue": {
    "queueLength": 2,
    "activeJobs": 1,
    "maxConcurrentJobs": 2,
    "isProcessing": true
  },
  "stats": {
    "total": 50,
    "pending": 2,
    "processing": 1,
    "completed": 45,
    "failed": 2,
    "streamingReady": 45
  }
}
```

### Reprocess Video
```
POST /api/video-processing/reprocess/:videoId
```

Adds a failed video back to the processing queue.

### Admin Endpoints (New)

#### Get Detailed Queue Status
```
GET /api/admin/video-processing/detailed-status
```

Returns comprehensive queue information including per-user details:
```json
{
  "success": true,
  "detailedInfo": {
    "globalLimits": {
      "maxGlobalConcurrentJobs": 3,
      "maxUserConcurrentJobs": 1,
      "currentActiveJobs": 2
    },
    "userDetails": [
      {
        "userId": "user123",
        "activeJobs": 1,
        "queueLength": 2,
        "canProcessMore": false
      }
    ],
    "lastProcessedUser": "user123",
    "canProcessMore": true
  }
}
```

#### Update Configuration
```
POST /api/admin/video-processing/config
Content-Type: application/json

{
  "maxGlobalConcurrentJobs": 5,
  "maxUserConcurrentJobs": 2
}
```

#### Remove User from Queue
```
DELETE /api/admin/video-processing/user-queue/:userId
```

Removes all pending jobs for a specific user (admin emergency control).

## Database Schema

New columns added to `videos` table:

```sql
-- Processing status: pending, processing, completed, failed
processing_status TEXT DEFAULT 'pending'

-- Path to processed streaming-ready file
streaming_ready_path TEXT

-- Path to original uploaded file (for reference)
original_filepath TEXT
```

## Configuration

### Processing Settings
- **Max Global Concurrent Jobs**: 3 (configurable, protects server resources)
- **Max User Concurrent Jobs**: 1 (configurable, ensures fairness between users)
- **Queue Check Interval**: 5 seconds
- **Max Retries**: 2 attempts per video
- **Processing Timeout**: No timeout (depends on video size)
- **Scheduling Algorithm**: Round-robin across users for fairness

### Concurrent Job Limits (New)
The system now implements sophisticated job limiting:

#### Global Limits
- **Purpose**: Protect server CPU and memory
- **Default**: 3 concurrent jobs maximum
- **Range**: 1-10 jobs (admin configurable)
- **Behavior**: No new jobs start if limit reached

#### Per-User Limits
- **Purpose**: Ensure fair resource allocation
- **Default**: 1 concurrent job per user
- **Range**: 1-5 jobs per user (admin configurable)
- **Behavior**: Prevents users from monopolizing resources

#### Round-Robin Scheduling
- **Algorithm**: Cycles through users with pending jobs
- **Fairness**: Each user gets equal processing opportunity
- **Tracking**: Remembers last processed user for rotation

### FFmpeg Settings for Processing
```bash
ffmpeg -hwaccel auto -i input.mp4 \
  -c:v libx264 \
  -preset medium \
  -crf 23 \
  -maxrate 3000k \
  -bufsize 6000k \
  -pix_fmt yuv420p \
  -profile:v high \
  -level 4.0 \
  -g 60 \
  -keyint_min 60 \
  -sc_threshold 0 \
  -c:a aac \
  -b:a 128k \
  -ar 44100 \
  -ac 2 \
  -movflags +faststart \
  output.mp4
```

## UI Indicators

### Gallery Status Indicators
- **🟡 Pending**: Clock icon with yellow color
- **🔵 Processing**: Spinning loader with blue color  
- **🔴 Failed**: Alert triangle with red color
- **🟢 Ready**: Check mark with green color

### Actions Available
- **Reprocess**: Available for failed videos (refresh icon)
- **Delete**: Always available (trash icon)

## Migration

To add video processing to existing installation:

1. **Run Migration Script**:
   ```bash
   node scripts/migrate-video-processing.js
   ```

2. **Restart Application**:
   ```bash
   npm restart
   ```

3. **Monitor Processing**:
   - Check gallery for status indicators
   - Use API endpoint for detailed statistics

## Troubleshooting

### Common Issues

1. **Processing Stuck**:
   - Check server logs for FFmpeg errors
   - Restart application to reset queue
   - Check disk space for output directory

2. **High CPU Usage**:
   - Reduce `maxConcurrentJobs` in service
   - Check if original videos are being used instead of processed ones

3. **Failed Processing**:
   - Check FFmpeg installation and codecs
   - Verify input video file integrity
   - Check disk space and permissions

### Monitoring

- **Queue Status**: `/api/video-processing/status`
- **Server Logs**: Check for `[VideoProcessing]` entries
- **Gallery UI**: Visual status indicators for each video

## Performance Impact

### CPU Usage
- **Upload**: Minimal impact (metadata extraction only)
- **Processing**: High CPU usage during conversion (background)
- **Streaming**: Minimal CPU usage (copy mode only)

### Storage Usage
- **Processed videos**: Additional storage required
- **Cleanup**: Original files can be deleted after processing (optional)
- **Optimization**: Processed files are typically smaller than originals

## Future Enhancements

- [ ] Batch processing controls
- [ ] Processing priority queue
- [ ] Automatic cleanup of original files
- [ ] Custom processing profiles
- [ ] Progress tracking for individual videos
- [ ] Processing analytics and reporting
