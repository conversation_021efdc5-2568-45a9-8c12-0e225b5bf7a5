# Video Processing Concurrent Job Limits

## Overview

The video processing service now implements sophisticated concurrent job limiting to protect server resources and ensure fair processing across all users. This system includes both global limits (to protect the server) and per-user limits (to ensure fairness).

## Features

### 1. Global Concurrent Job Limit
- **Purpose**: Protects server CPU and memory resources
- **Default**: 3 concurrent jobs maximum
- **Range**: 1-10 jobs (configurable)
- **Behavior**: No new jobs start if global limit is reached

### 2. Per-User Concurrent Job Limit
- **Purpose**: Ensures fair resource allocation between users
- **Default**: 1 concurrent job per user
- **Range**: 1-5 jobs per user (configurable)
- **Behavior**: Users cannot monopolize processing resources

### 3. Round-Robin Scheduling
- **Purpose**: Fair processing order across all users
- **Behavior**: Cycles through users with pending jobs
- **Algorithm**: Each user gets equal opportunity to process videos

## Architecture

### Queue Organization
```
Global Queue (Legacy)
├── Video1 (User A)
├── Video2 (User B)
└── Video3 (User A)

User-Based Queues (New)
├── User A Queue: [Video1, Video3]
├── User B Queue: [Video2]
└── User C Queue: [Video4, Video5]
```

### Processing Flow
1. **Job Addition**: Videos added to user-specific queues
2. **Round-Robin Selection**: Next job selected fairly across users
3. **Limit Checking**: Both global and per-user limits enforced
4. **Processing**: Job executed with progress tracking
5. **Cleanup**: Job counts decremented, queues cleaned

## Configuration

### Default Settings
```javascript
maxGlobalConcurrentJobs: 3    // Server protection
maxUserConcurrentJobs: 1      // User fairness
```

### Runtime Configuration
```javascript
// Update global limit (1-10)
videoProcessingService.setGlobalConcurrentJobLimit(5);

// Update per-user limit (1-5)
videoProcessingService.setUserConcurrentJobLimit(2);

// Get current configuration
const config = videoProcessingService.getConfiguration();
```

## API Endpoints

### Get Detailed Queue Status (Admin Only)
```
GET /api/admin/video-processing/detailed-status
```

**Response:**
```json
{
  "success": true,
  "detailedInfo": {
    "globalLimits": {
      "maxGlobalConcurrentJobs": 3,
      "maxUserConcurrentJobs": 1,
      "currentActiveJobs": 2
    },
    "userDetails": [
      {
        "userId": "user123",
        "activeJobs": 1,
        "queueLength": 2,
        "queuedJobs": [
          {
            "videoId": "video456",
            "addedAt": "2024-01-15T10:30:00Z",
            "retries": 0
          }
        ],
        "canProcessMore": false
      }
    ],
    "lastProcessedUser": "user123",
    "canProcessMore": true
  },
  "configuration": {
    "maxGlobalConcurrentJobs": 3,
    "maxUserConcurrentJobs": 1,
    "activeJobs": 2,
    "totalUsers": 3,
    "totalActiveUsers": 2
  }
}
```

### Update Configuration (Admin Only)
```
POST /api/admin/video-processing/config
Content-Type: application/json

{
  "maxGlobalConcurrentJobs": 5,
  "maxUserConcurrentJobs": 2
}
```

### Remove User from Queue (Admin Only)
```
DELETE /api/admin/video-processing/user-queue/:userId
```

## Monitoring

### Queue Status Information
- **Total Queue Length**: Videos waiting across all users
- **Active Jobs**: Currently processing videos
- **User Queue Info**: Per-user queue and active job counts
- **Last Processed User**: For round-robin tracking

### Performance Metrics
- **Global Utilization**: Active jobs vs. global limit
- **User Fairness**: Distribution of processing across users
- **Queue Efficiency**: Average wait times per user

## Benefits

### Server Protection
- **CPU Management**: Prevents server overload
- **Memory Control**: Limits concurrent FFmpeg processes
- **Stability**: Maintains system responsiveness

### User Fairness
- **Equal Access**: All users get fair processing time
- **No Monopolization**: Single users can't block others
- **Predictable Service**: Consistent processing experience

### Operational Control
- **Dynamic Configuration**: Adjust limits without restart
- **Admin Monitoring**: Detailed queue visibility
- **User Management**: Remove problematic users from queue

## Migration from Legacy System

### Backward Compatibility
- Legacy `getQueueStatus()` method still works
- Old `maxConcurrentJobs` property maintained
- Existing API endpoints unchanged

### New Features
- User-based queue organization
- Round-robin scheduling
- Enhanced monitoring and control
- Dynamic configuration

## Testing

### Test Script
```bash
node scripts/test-video-processing-limits.js
```

### Test Coverage
- Configuration validation
- Queue management
- Round-robin scheduling
- Limit enforcement
- User queue removal
- Error handling

## Troubleshooting

### Common Issues

**Queue Not Processing**
- Check global job limit not exceeded
- Verify users haven't reached per-user limits
- Ensure videos have valid user associations

**Unfair Processing**
- Verify round-robin scheduling is working
- Check for users with excessive queue lengths
- Monitor last processed user tracking

**Performance Issues**
- Reduce global concurrent job limit
- Lower per-user concurrent job limit
- Monitor server CPU and memory usage

### Debug Information
```javascript
// Get detailed queue information
const info = videoProcessingService.getDetailedQueueInfo();
console.log('Queue debug info:', JSON.stringify(info, null, 2));

// Check configuration
const config = videoProcessingService.getConfiguration();
console.log('Current config:', config);
```

## Future Enhancements

### Planned Features
- **Priority Queues**: VIP users get processing priority
- **Dynamic Limits**: Auto-adjust based on server load
- **Queue Analytics**: Historical processing statistics
- **User Notifications**: Queue position and estimated wait time

### Performance Optimizations
- **Batch Processing**: Group small videos together
- **Resource Prediction**: Estimate processing time and resources
- **Load Balancing**: Distribute across multiple servers
