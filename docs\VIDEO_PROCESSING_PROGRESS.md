# Video Processing Progress Tracking

## Overview

Sistem progress tracking untuk video re-encoding telah diimplementasikan untuk memberikan feedback real-time kepada user tentang status pemrosesan video mereka.

## Fitur

### 1. Real-time Progress Display
- **Progress Bar**: Menampilkan persentase progress (0-100%)
- **Status Message**: Pesan deskriptif tentang tahap pemrosesan
- **Visual Indicators**: Icon dan warna yang berbeda untuk setiap status

### 2. Status Tracking
- **Pending**: Video dalam antrian, belum diproses
- **Processing**: Video sedang diproses dengan persentase progress
- **Completed**: Video berhasil diproses dan siap streaming
- **Failed**: Video gagal diproses dengan pesan error

### 3. Automatic Updates
- **Polling**: Frontend melakukan polling setiap 2 detik
- **Auto-refresh**: Halaman otomatis refresh setelah processing selesai
- **Cleanup**: Progress data otomatis dibersihkan setelah selesai

## Implementasi Teknis

### Backend (services/videoProcessingService.js)

#### Progress Store
```javascript
this.progressStore = new Map(); // Store progress untuk setiap video
```

#### Progress Tracking Methods
```javascript
// Update progress
updateProgress(videoId, percent, status, message)

// Get progress
getProgress(videoId)

// Get all progress
getAllProgress()

// Clear progress
clearProgress(videoId)
```

#### FFmpeg Progress Integration
```javascript
.on('progress', (progress) => {
  if (progress.percent) {
    const progressPercent = Math.round(progress.percent);
    this.updateProgress(videoId, progressPercent, 'processing', `Processing video: ${progressPercent}%`);
  }
})
```

### API Endpoints

#### Get Progress for Specific Video
```
GET /api/video-processing/progress/:videoId
```

Response:
```json
{
  "success": true,
  "progress": {
    "percent": 75,
    "status": "processing",
    "message": "Processing video: 75%",
    "timestamp": "2025-06-03T03:18:56.036Z"
  }
}
```

#### Get All Progress
```
GET /api/video-processing/progress
```

Response:
```json
{
  "success": true,
  "progress": {
    "video-id-1": {
      "percent": 50,
      "status": "processing",
      "message": "Processing video: 50%",
      "timestamp": "2025-06-03T03:18:56.036Z"
    }
  }
}
```

### Frontend (views/gallery.ejs)

#### Progress Bar UI
```html
<!-- Progress Bar for Processing Videos -->
<div class="absolute bottom-0 left-0 right-0 bg-black/70 p-2">
  <div class="w-full bg-gray-600 rounded-full h-1.5">
    <div class="bg-blue-400 h-1.5 rounded-full transition-all duration-300 processing-progress" 
         data-video-id="<%= video.id %>" style="width: 0%"></div>
  </div>
  <div class="text-xs text-white mt-1 processing-message" data-video-id="<%= video.id %>">
    Starting processing...
  </div>
</div>
```

#### JavaScript Progress Tracking
```javascript
// Start tracking untuk video yang sedang diproses
function startProgressTracking(videoId) {
  const interval = setInterval(async () => {
    const response = await fetch(`/api/video-processing/progress/${videoId}`);
    const data = await response.json();
    
    if (data.success && data.progress) {
      updateProgressUI(videoId, data.progress);
    }
  }, 2000); // Poll setiap 2 detik
}

// Update UI dengan progress data
function updateProgressUI(videoId, progress) {
  const progressBar = document.querySelector(`.processing-progress[data-video-id="${videoId}"]`);
  const progressMessage = document.querySelector(`.processing-message[data-video-id="${videoId}"]`);
  
  if (progressBar) {
    progressBar.style.width = `${progress.percent}%`;
  }
  
  if (progressMessage) {
    progressMessage.textContent = progress.message;
  }
}
```

## Lifecycle Progress

### 1. Video Upload
- Status: `pending`
- Progress: 0%
- Message: "Video uploaded, waiting for processing..."

### 2. Processing Start
- Status: `processing`
- Progress: 0%
- Message: "Starting video processing..."

### 3. FFmpeg Processing
- Status: `processing`
- Progress: 1-99%
- Message: "Processing video: X%"

### 4. Processing Complete
- Status: `completed`
- Progress: 100%
- Message: "Processing completed successfully"

### 5. Processing Failed
- Status: `failed`
- Progress: 0%
- Message: "Processing failed: [error message]"

## Performance Considerations

### Memory Management
- Progress data disimpan di memory (Map)
- Auto-cleanup setelah completion (5 detik)
- Auto-cleanup setelah error (10 detik)
- Cleanup saat page unload

### Network Efficiency
- Polling interval: 2 detik (balance antara responsiveness dan load)
- Hanya poll video yang sedang processing
- Stop polling setelah completion/failure

### Error Handling
- Graceful degradation jika API tidak tersedia
- Retry mechanism untuk failed requests
- Fallback UI jika progress data tidak tersedia

## Configuration

### Polling Interval
```javascript
// Ubah interval polling (default: 2000ms)
const interval = setInterval(async () => {
  // polling logic
}, 2000);
```

### Cleanup Timeouts
```javascript
// Cleanup setelah completion (default: 5000ms)
setTimeout(() => {
  this.progressStore.delete(videoId);
}, 5000);

// Cleanup setelah error (default: 10000ms)
setTimeout(() => {
  this.progressStore.delete(videoId);
}, 10000);
```

## Testing

### Manual Testing
1. Upload video yang memerlukan re-encoding (MKV, HEVC, dll)
2. Buka gallery page
3. Observe progress bar dan message updates
4. Verify completion behavior

### API Testing
```bash
# Test progress endpoint
curl -X GET "http://localhost:7575/api/video-processing/progress/VIDEO_ID" \
  -H "Cookie: session_cookie_here"

# Test all progress endpoint
curl -X GET "http://localhost:7575/api/video-processing/progress" \
  -H "Cookie: session_cookie_here"
```

## Troubleshooting

### Progress Tidak Muncul
- Check apakah video status = 'processing'
- Verify API endpoint response
- Check browser console untuk errors

### Progress Stuck
- Check FFmpeg process status
- Verify video processing service
- Check server logs untuk errors

### Memory Leaks
- Monitor progressStore size
- Verify cleanup timeouts
- Check for orphaned intervals

## Future Enhancements

1. **WebSocket Integration**: Real-time updates tanpa polling
2. **Batch Progress**: Progress untuk multiple videos
3. **Detailed Stages**: Progress untuk setiap tahap (analysis, encoding, etc.)
4. **ETA Calculation**: Estimasi waktu completion
5. **Pause/Resume**: Kemampuan pause/resume processing
