# FFmpeg Process Management - Perbaikan

## Ma<PERSON>ah yang <PERSON>n

<PERSON>, proses FFmpeg masih berjalan setelah stream dihentikan karena beberapa masalah dalam konfigurasi spawn process:

### 1. **Proses Detached**
```javascript
// SEBELUM (BERMASALAH)
const ffmpegProcess = spawn(ffmpegPath, ffmpegArgs, {
  detached: true,  // ❌ Membuat proses berjalan terpisah
  stdio: ['ignore', 'pipe', 'pipe']
});
```

### 2. **Process Unref**
```javascript
// SEBELUM (BERMASALAH)
ffmpegProcess.unref(); // ❌ Node.js tidak menunggu proses selesai
```

### 3. **Terminasi Tidak Efektif**
```javascript
// SEBELUM (BERMASALAH)
ffmpegProcess.kill('SIGTERM'); // ❌ Hanya SIGTERM, tanpa fallback
```

## Sol<PERSON>i yang <PERSON>pkan

### 1. **Perbaikan Spawn Configuration**
```javascript
// SETELAH (DIPERBAIKI)
const ffmpegProcess = spawn(ffmpegPath, ffmpegArgs, {
  detached: false, // ✅ Proses terikat dengan parent
  stdio: ['ignore', 'pipe', 'pipe']
});
// ✅ Menghapus ffmpegProcess.unref()
```

### 2. **Enhanced Process Termination**
```javascript
// SETELAH (DIPERBAIKI)
try {
  console.log(`[StreamingService] Sending SIGTERM to FFmpeg process for stream ${streamId}`);
  ffmpegProcess.kill('SIGTERM');
  
  // Set timeout untuk force kill jika SIGTERM tidak bekerja
  const forceKillTimeout = setTimeout(() => {
    if (activeStreams.has(streamId)) {
      console.log(`[StreamingService] SIGTERM timeout, sending SIGKILL to FFmpeg process for stream ${streamId}`);
      try {
        ffmpegProcess.kill('SIGKILL'); // ✅ Fallback ke SIGKILL
      } catch (forceKillError) {
        console.error(`[StreamingService] Error force killing FFmpeg process: ${forceKillError.message}`);
      }
    }
  }, 5000); // 5 detik timeout
  
  // Clear timeout jika proses exit normal
  ffmpegProcess.once('exit', () => {
    clearTimeout(forceKillTimeout);
  });
  
} catch (killError) {
  console.error(`[StreamingService] Error killing FFmpeg process: ${killError.message}`);
  manuallyStoppingStreams.delete(streamId);
}
```

### 3. **Orphaned Process Cleanup**
```javascript
// Fungsi untuk membersihkan proses FFmpeg yang orphaned
async function forceCleanupOrphanedProcesses() {
  const { exec } = require('child_process');
  const os = require('os');
  
  if (os.platform() === 'win32') {
    // Windows - check for FFmpeg processes
    exec('tasklist /FI "IMAGENAME eq ffmpeg.exe" /FO CSV', (error, stdout) => {
      if (!error && stdout) {
        const lines = stdout.split('\n');
        const ffmpegProcesses = lines.filter(line => line.includes('ffmpeg.exe'));
        
        if (ffmpegProcesses.length > 1) {
          exec('taskkill /F /IM ffmpeg.exe', (killError) => {
            // Handle cleanup
          });
        }
      }
    });
  } else {
    // Linux/Mac - check for FFmpeg processes
    exec('pgrep -f ffmpeg', (error, stdout) => {
      if (!error && stdout) {
        const pids = stdout.trim().split('\n').filter(pid => pid);
        if (pids.length > 0) {
          exec(`kill -9 ${pids.join(' ')}`, (killError) => {
            // Handle cleanup
          });
        }
      }
    });
  }
}
```

### 4. **Automatic Cleanup Schedule**
```javascript
// Cleanup otomatis saat startup
syncStreamStatuses().then(() => {
  console.log('[StreamingService] Initial stream status sync completed');
  forceCleanupOrphanedProcesses(); // ✅ Cleanup saat startup
});

// Cleanup berkala setiap 15 menit
setInterval(forceCleanupOrphanedProcesses, 15 * 60 * 1000);
```

## Tools dan API yang Ditambahkan

### 1. **Cleanup Script**
```bash
# Menjalankan script cleanup manual
node scripts/cleanup-ffmpeg.js
```

### 2. **Admin API Endpoint**
```javascript
// POST /api/admin/cleanup-ffmpeg
// Hanya admin yang bisa mengakses
// Membersihkan proses FFmpeg orphaned secara manual
```

## Cara Menggunakan

### 1. **Automatic (Recommended)**
- Sistem akan otomatis membersihkan proses orphaned saat startup
- Cleanup berkala setiap 15 menit
- Enhanced termination dengan timeout dan SIGKILL fallback

### 2. **Manual Cleanup via Script**
```bash
cd /path/to/streamonpod
node scripts/cleanup-ffmpeg.js
```

### 3. **Manual Cleanup via Admin Panel**
- Login sebagai admin
- Akses admin dashboard
- Gunakan endpoint `/api/admin/cleanup-ffmpeg`

## Monitoring

### Log Messages yang Ditambahkan:
- `[StreamingService] Sending SIGTERM to FFmpeg process for stream {streamId}`
- `[StreamingService] SIGTERM timeout, sending SIGKILL to FFmpeg process for stream {streamId}`
- `[StreamingService] Checking for orphaned FFmpeg processes...`
- `[StreamingService] Found X FFmpeg processes, cleaning up...`
- `[StreamingService] Orphaned FFmpeg processes cleaned up`

## Hasil yang Diharapkan

✅ **Sebelum**: FFmpeg masih berjalan setelah stream stop
✅ **Setelah**: FFmpeg benar-benar berhenti saat stream stop
✅ **Bonus**: Cleanup otomatis untuk proses orphaned
✅ **Bonus**: Tools manual untuk troubleshooting

## Testing

Untuk memverifikasi perbaikan:

1. **Start stream** - Cek proses FFmpeg berjalan
2. **Stop stream** - Cek proses FFmpeg berhenti dalam 5 detik
3. **Check orphaned** - Jalankan cleanup script untuk memastikan tidak ada proses tersisa

```bash
# Windows
tasklist | findstr ffmpeg

# Linux/Mac  
ps aux | grep ffmpeg
```
