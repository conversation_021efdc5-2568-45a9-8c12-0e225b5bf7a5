# Stream Status Bug Fixes

## Problem Description

The application had a bug where streams would show status as "live" in the dashboard but were not actually streaming. This occurred due to inconsistencies between the database status and the actual FFmpeg process state.

## Root Causes Identified

1. **Timing Issues**: Status synchronization ran every 10 minutes, too infrequent for real-time accuracy
2. **Race Conditions**: FFmpeg processes could exit without properly updating database status
3. **Memory vs Database Inconsistency**: The `activeStreams` Map and database could get out of sync
4. **Insufficient Process Validation**: No validation of actual process health before showing "live" status
5. **Frontend Polling Frequency**: 30-second polling was too slow for real-time status updates

## Implemented Fixes

### 1. Enhanced Process Validation (`validateStreamProcess`)

**File**: `services/streamingService.js`

Added a new function that validates if a stream process is actually running:

```javascript
function validateStreamProcess(streamId) {
  const process = activeStreams.get(streamId);
  if (!process) return false;
  
  // Check if process is still alive
  if (process.killed || process.exitCode !== null) {
    activeStreams.delete(streamId);
    return false;
  }
  
  return process.pid ? true : false;
}
```

### 2. Improved Status Synchronization

**File**: `services/streamingService.js`

- **Reduced sync interval**: From 10 minutes to 2 minutes
- **Enhanced sync logic**: Uses `validateStreamProcess` for better accuracy
- **Better logging**: Shows number of fixed inconsistencies

### 3. Immediate Status Updates on Process Events

**File**: `services/streamingService.js`

- **FFmpeg exit events**: Immediately remove from `activeStreams` and update database
- **Process error events**: Immediate cleanup and status update
- **Better error handling**: More detailed logging for debugging

### 4. Frontend Status Display Improvements

**File**: `views/dashboard.ejs`

- **Enhanced status validation**: Shows "Status Issue" for inconsistent streams
- **Faster polling**: Reduced from 30 seconds to 15 seconds
- **New status badge**: Added "inconsistent" status with orange warning
- **Fix Status button**: Allows manual status synchronization

### 5. API Enhancements

**File**: `app.js`

- **Enhanced status endpoint**: Uses `validateStreamProcess` for real-time validation
- **Additional status info**: Provides `processValid` field for debugging

## New Features

### Status Issue Detection

Streams with inconsistent status now show:
- **Orange "Status Issue" badge** instead of incorrect "Live" status
- **"Fix Status" button** to manually trigger synchronization
- **Enhanced tooltips** explaining the issue

### Manual Status Correction

Users can now:
- Click "Fix Status" button to trigger immediate synchronization
- Admin users can use the existing sync functionality
- Real-time feedback on status correction

### Improved Monitoring

- **Better logging**: More detailed process lifecycle logging
- **Inconsistency tracking**: Counts and reports fixed inconsistencies
- **Process validation**: Real-time process health checks

## Testing

### Automated Testing

Run the test script to verify fixes:

```bash
node scripts/test-status-fix.js
```

### Manual Testing

1. **Start a stream** and verify it shows "Live" status
2. **Kill the FFmpeg process manually** (simulate crash)
3. **Wait 15 seconds** for frontend to detect inconsistency
4. **Verify "Status Issue" badge** appears
5. **Click "Fix Status"** to correct the status
6. **Verify status** changes to "Offline"

## Configuration Changes

### Timing Adjustments

- **Status sync interval**: 10 minutes → 2 minutes
- **Frontend polling**: 30 seconds → 15 seconds
- **Orphaned process cleanup**: 15 minutes → 10 minutes

### New Functions Exported

```javascript
// services/streamingService.js
module.exports = {
  // ... existing exports
  validateStreamProcess,  // NEW: Process validation function
  // ... rest of exports
};
```

## Monitoring and Maintenance

### Log Messages to Watch

- `[StreamingService] Found inconsistent stream X: marked as 'live' in DB but not active in memory`
- `[StreamingService] Process X is dead but still in activeStreams`
- `[StreamingService] Stream status sync completed. Active streams: X, Fixed inconsistencies: Y`

### Dashboard Indicators

- **Orange "Status Issue" badges**: Indicate streams needing attention
- **"Fix Status" buttons**: Allow manual correction
- **Real-time status updates**: More accurate stream states

### Performance Impact

- **Minimal CPU overhead**: Enhanced validation is lightweight
- **Reduced memory usage**: Better cleanup of orphaned data
- **Faster issue detection**: 2-minute sync vs 10-minute sync

## Future Improvements

1. **WebSocket real-time updates**: Instant status changes without polling
2. **Process health monitoring**: Periodic FFmpeg process health checks
3. **Automatic recovery**: Auto-restart failed streams under certain conditions
4. **Status history**: Track status change history for debugging
5. **Alert system**: Notify admins of persistent status issues

## Rollback Plan

If issues occur, revert these changes:

1. Change sync interval back to 10 minutes in `streamingService.js`
2. Change frontend polling back to 30 seconds in `dashboard.ejs`
3. Remove `validateStreamProcess` calls from status endpoints
4. Remove "inconsistent" status handling from frontend

The fixes are backward compatible and can be safely rolled back if needed.
