# Stream Status Issues - Comprehensive Fixes

## Problem Description

The stream status system was experiencing inconsistencies where FFmpeg processes were running successfully but the status wasn't being updated properly in the database or displayed correctly in the UI.

## Root Causes Identified

1. **Process Validation Issues**: The `validateStreamProcess` function wasn't properly detecting dead processes
2. **Race Conditions**: Status updates weren't happening immediately when FFmpeg processes started/stopped
3. **Missing User ID**: Status updates weren't including the user_id parameter consistently
4. **Sync Frequency**: Status synchronization was too infrequent (2 minutes) for real-time accuracy
5. **Incomplete Error Handling**: FFmpeg exit and error handlers weren't properly updating status

## Implemented Fixes

### 1. Enhanced Process Validation (`validateStreamProcess`)

**File**: `services/streamingService.js` (lines 1058-1112)

**Improvements**:
- Added proper process existence check using `process.kill(0)`
- Enhanced dead process detection (killed, exitCode checks)
- Added PID validation
- Improved timing validation (reduced from 5s to 3s for new processes)
- Added automatic cleanup when invalid processes are detected

**Key Changes**:
```javascript
// Check if process is actually running using Node.js process validation
try {
  process.kill(0); // Signal 0 just checks if process exists without killing it
  return true;
} catch (processError) {
  // Process doesn't exist anymore
  activeStreams.delete(streamId);
  cleanupStreamData(streamId);
  return false;
}
```

### 2. Improved Status Synchronization Logic

**File**: `services/streamingService.js` (lines 923-1025)

**Improvements**:
- Added comprehensive two-pass validation
- Better error handling with try-catch blocks
- Enhanced logging with ✅/❌ indicators
- Proper user_id inclusion in status updates
- Detailed inconsistency detection and reporting

**Key Features**:
- First pass: Check streams marked 'live' in database
- Second pass: Check streams in memory but not marked as 'live'
- Automatic cleanup of orphaned processes
- Detailed logging of validation results

### 3. Enhanced FFmpeg Event Handlers

**File**: `services/streamingService.js` (lines 690-835)

**Improvements**:
- Added proper user_id retrieval for status updates
- Enhanced error handling in exit events
- Better logging with success/warning indicators
- Proper stream info validation before status updates

**Key Changes**:
```javascript
// Get stream info for proper status update with user_id
const stream = await Stream.findById(streamId);
if (stream) {
  await Stream.updateStatus(streamId, 'offline', stream.user_id);
  console.log(`[StreamingService] ✅ Successfully updated stream ${streamId} status to offline`);
}
```

### 4. Increased Sync Frequency

**File**: `services/streamingService.js` (lines 1067-1069)

**Changes**:
- Reduced sync interval from 2 minutes to 30 seconds
- Reduced orphaned process cleanup from 10 minutes to 5 minutes

```javascript
setInterval(syncStreamStatuses, 30 * 1000); // Real-time status detection
setInterval(forceCleanupOrphanedProcesses, 5 * 60 * 1000);
```

### 5. Added Debug Endpoint

**File**: `app.js` (lines 3172-3249)

**New Endpoint**: `GET /api/admin/debug/stream-status`

**Features**:
- Real-time inconsistency detection
- Detailed process validation info
- Memory vs database comparison
- Admin-only access with proper authentication

### 6. Created Testing Script

**File**: `scripts/test-stream-status.js`

**Features**:
- Manual status testing and validation
- Inconsistency detection
- Process validation testing
- Manual sync execution
- Detailed reporting and recommendations

## Usage Instructions

### 1. Automatic Monitoring
The system now automatically:
- Syncs status every 30 seconds
- Validates processes in real-time
- Cleans up orphaned processes every 5 minutes
- Updates status immediately on FFmpeg events

### 2. Manual Testing
Run the test script to check current status:
```bash
node scripts/test-stream-status.js
```

### 3. Real-time Monitoring
Monitor status changes in real-time:
```bash
node scripts/debug-stream-status-realtime.js
```

### 4. Admin Debug Endpoint
Access the debug endpoint (admin only):
```
GET /api/admin/debug/stream-status
```

### 5. Manual Sync
Trigger manual sync (admin only):
```
POST /api/admin/sync-stream-status
```

### 7. Fixed Immediate Status Update Issue

**Problem**: Status was being updated to 'live' immediately when FFmpeg process started, before FFmpeg actually began streaming.

**Solution**: Added delayed status update with validation:

```javascript
// Wait 5 seconds for FFmpeg to initialize before updating status
const statusUpdateTimer = setTimeout(async () => {
  if (activeStreams.has(streamId) && !statusUpdated) {
    if (validateStreamProcess(streamId)) {
      await Stream.updateStatus(streamId, 'live', stream.user_id);
      statusUpdated = true;
      console.log(`✅ Stream ${streamId} status updated to 'live' after validation`);
    }
  }
}, 5000);
```

**Benefits**:
- Status only updates to 'live' after FFmpeg is actually running
- Prevents false 'live' status for failed streams
- Includes proper cleanup of timers on exit/error

## Expected Results

After implementing these fixes:

1. **Accurate Status Updates**: Status only changes to 'live' after FFmpeg is confirmed running (5 second delay)
2. **Real-time Monitoring**: Status changes detected within 30 seconds maximum
3. **Accurate Process Detection**: Dead processes detected and cleaned up immediately
4. **Consistent Database State**: Memory and database always in sync
5. **Better Error Handling**: Proper status updates on all FFmpeg events with timer cleanup
6. **Enhanced Debugging**: Comprehensive tools for monitoring and troubleshooting

## Monitoring and Maintenance

### Key Logs to Watch
- `[StreamingService] ✅ Stream status sync completed`
- `[StreamingService] INCONSISTENT: Stream X marked 'live' in DB but not in memory`
- `[StreamingService] ✅ Fixed: Updated stream X status to 'offline'`

### Performance Impact
- Increased sync frequency may use slightly more CPU
- Enhanced logging provides better debugging capabilities
- Process validation is more thorough but efficient

### Troubleshooting
If issues persist:
1. Run the test script: `node scripts/test-stream-status.js`
2. Check the debug endpoint for inconsistencies
3. Monitor logs for validation failures
4. Consider restarting the application if needed

## Files Modified

1. `services/streamingService.js` - Core streaming logic improvements
2. `app.js` - Added debug endpoint
3. `scripts/test-stream-status.js` - New testing script
4. `docs/stream-status-fixes-comprehensive.md` - This documentation

## Testing Recommendations

1. Start a stream and verify status updates immediately
2. Stop a stream and verify status changes to offline
3. Kill FFmpeg process manually and verify automatic cleanup
4. Use the test script to validate system state
5. Monitor the debug endpoint for ongoing inconsistencies
