{"timestamp": "2025-06-01T15:17:06.132Z", "overall": "HEALTHY", "checks": [{"name": "Application Responsiveness", "status": "PASS", "message": "Pre-deployment check - application not expected to be running", "details": {"preDeployment": true}, "timestamp": "2025-06-01T15:17:06.135Z"}, {"name": "Database Connectivity", "status": "PASS", "message": "Database connection successful", "details": {"testQuery": "SELECT 1", "result": {"test": 1}}, "timestamp": "2025-06-01T15:17:06.175Z"}, {"name": "File System Access", "status": "PASS", "message": "All critical directories accessible", "details": {"./db": {"accessible": true, "description": "Database directory"}, "./logs": {"accessible": true, "description": "Logs directory"}, "./uploads": {"accessible": true, "description": "Uploads directory"}, "./public": {"accessible": true, "description": "Public assets directory"}}, "timestamp": "2025-06-01T15:17:06.177Z"}, {"name": "Environment Configuration", "status": "PASS", "message": "All required environment variables present (3 required, 4 optional)", "details": {"required": ["PORT", "SESSION_SECRET", "BASE_URL"], "optional": ["NODE_ENV", "LOG_LEVEL", "MIDTRANS_SERVER_KEY", "MIDTRANS_CLIENT_KEY"], "missing": []}, "timestamp": "2025-06-01T15:17:06.179Z"}, {"name": "Service Dependencies", "status": "PASS", "message": "All required services available", "details": {"FFmpeg": {"available": true}, "Node.js Version": {"available": true, "version": "v20.10.0", "supported": true}}, "timestamp": "2025-06-01T15:17:06.231Z"}, {"name": "Performance Metrics", "status": "PASS", "message": "System performance within normal ranges", "details": {"memory": {"total": 13.88, "used": 10.49, "percentage": 75.57}, "cpu": {"cores": 8, "model": "AMD Ryzen 5 3550H with Radeon Vega Mobile Gfx  ", "loadAverage": [0, 0, 0]}, "uptime": 17336}, "timestamp": "2025-06-01T15:17:06.233Z"}], "summary": {"total": 6, "passed": 6, "failed": 0, "warnings": 0}}