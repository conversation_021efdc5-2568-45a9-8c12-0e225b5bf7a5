{"stats": {"total": 5, "passed": 4, "failed": 1, "skipped": 0, "critical_failures": 0, "required_failures": 1}, "results": [{"name": "Streaming Service Tests", "priority": "HIGH", "status": "PASSED", "message": "Test completed successfully", "duration": 298, "output": "Testing streaming fixes...\n\n1. Checking for orphaned live streams:\nFound 0 streams marked as 'live':\n\n2. Testing streaming slots calculation:\nNo users with streams found for testing\n\n3. Stream status breakdown:\n  - offline: 2 streams\n\n4. Checking RTMP URLs for common issues:\n\n  Stream: EKSPLORASI DRONE HORROR\n    RTMP URL: rtmp://a.rtmp.youtube.com/live2\n    Stream Key: kgy2-7p0g-956d-cw1s-3jv4\n    ✅ No obvious issues detected\n\n  Stream: Golden Sunshine - NRSD\n    RTMP URL: rtmp://a.rtmp.youtube", "required": true}, {"name": "Stream Management Tests", "priority": "HIGH", "status": "FAILED", "message": "Command failed: node scripts/testStreamFixes.js\n❌ Stream fixes test failed: TypeError: streamingService.autoStopStream is not a function\n    at testStreamFixes (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\scripts\\testStreamFixes.js:25:30)\n❌ Stream fixes test failed: TypeError: streamingService.autoStopStream is not a function\n    at testStreamFixes (C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\scripts\\testStreamFixes.js:25:30)\n", "duration": 298, "output": "Using bundled FFmpeg at: C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffmpeg-installer\\win32-x64\\ffmpeg.exe\n[StreamingService] Running initial stream status sync on startup...\n[StreamingService] Syncing stream statuses...\n[StreamingService] Starting periodic memory cleanup...\nStream scheduler initialized\nChecking for scheduled streams (2025-06-01T15:17:08.362Z to 2025-06-01T15:18:08.362Z)\n🧪 Testing Stream Fixes...\n\n1. Testing clear failed stream on edit:\n[StreamingService] Found inconsistent stream 862f1755-1bac-4ba6-a780-2b146775bb41: marked as 'live' in DB but not active in memory\n   Using test stream: EKSPLORASI DRONE HORROR (ba27201e-3e7a-4453-b1c0-1f86102324dd)\n   Simulating failed stream...\n", "required": true}, {"name": "Upload System Tests", "priority": "MEDIUM", "status": "PASSED", "message": "Test completed successfully", "duration": 835, "output": "🧪 STARTING UPLOAD TESTS\n\nNaN\n🔐 Logging in as aufanirsad...\n❌ Cannot proceed without login\n", "required": false}, {"name": "Production Configuration Tests", "priority": "HIGH", "status": "PASSED", "message": "Test completed successfully", "duration": 110, "output": "🔍 StreamOnPod Production Configuration Validation\n\n1. 🌍 Environment Configuration:\n✅ NODE_ENV is set to production\n✅ Port configured: 7575\n\n2. 🔒 Security Configuration:\n✅ SESSION_SECRET is properly configured\n✅ CSRF_SECRET is properly configured\n\n3. 🏦 Midtrans Configuration:\n✅ MIDTRANS_SERVER_KEY is configured\n✅ MIDTRANS_CLIENT_KEY is configured\n✅ MIDTRANS_IS_PRODUCTION is configured: false\n✅ MIDTRANS_MERCHANT_ID is configured: G463893303\n\n4. 🌐 URL Configuration:\n✅ Base URL configured: http", "required": true}, {"name": "Midtrans Payment Tests", "priority": "MEDIUM", "status": "PASSED", "message": "Test completed successfully", "duration": 2066, "output": "✅ Midtrans configuration validated successfully\n🏦 Midtrans initialized in PRODUCTION mode\n🔑 Server Key: Mid-server...\n🔑 Client Key: Mid-client...\n🧪 Testing Midtrans Integration...\n\n1. Checking environment variables:\n   MIDTRANS_SERVER_KEY: ✓ Set\n   MIDTRANS_CLIENT_KEY: ✓ Set\n   MIDTRANS_IS_PRODUCTION: true\n   MIDTRANS_MERCHANT_ID: ✓ Set\n\n2. Testing IDR amount validation and formatting:\n   150000 IDR = Rp 150.000\n   300000 IDR = Rp 300.000\n   500000 IDR = Rp 500.000\n\n3. Testing transaction cr", "required": false}], "overallSuccess": false, "timestamp": "2025-06-01T15:17:11.445Z"}