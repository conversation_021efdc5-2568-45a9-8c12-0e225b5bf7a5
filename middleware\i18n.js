const i18n = require('i18n');
const path = require('path');
const fs = require('fs');

// Load translation files manually
const localesDir = path.join(__dirname, '..', 'locales');
const translations = {};

// Load English translations
try {
  const enPath = path.join(localesDir, 'en.json');
  translations.en = JSON.parse(fs.readFileSync(enPath, 'utf8'));
} catch (error) {
  console.error('Error loading English translations:', error);
  translations.en = {};
}

// Load Indonesian translations
try {
  const idPath = path.join(localesDir, 'id.json');
  translations.id = JSON.parse(fs.readFileSync(idPath, 'utf8'));
} catch (error) {
  console.error('Error loading Indonesian translations:', error);
  translations.id = {};
}

// Configure i18n
i18n.configure({
  locales: ['id', 'en'], // Indonesian first, then English
  defaultLocale: 'id', // Default to Indonesian
  directory: localesDir,
  objectNotation: true,
  updateFiles: false,
  syncFiles: false,
  cookie: 'lang',
  queryParameter: 'lang',
  staticCatalog: translations
});

// Middleware to set locale based on user preference
const setLocale = (req, res, next) => {
  let locale = 'id'; // default locale changed to Indonesian

  // Priority order for locale detection:
  // 1. Query parameter (?lang=id)
  // 2. Session preference
  // 3. Cookie
  // 4. Accept-Language header
  // 5. Default locale

  if (req.query.lang && i18n.getLocales().includes(req.query.lang)) {
    locale = req.query.lang;
    // Save to session and cookie
    if (req.session) {
      req.session.locale = locale;
    }
    res.cookie('lang', locale, { 
      maxAge: 365 * 24 * 60 * 60 * 1000, // 1 year
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production'
    });
  } else if (req.session && req.session.locale && i18n.getLocales().includes(req.session.locale)) {
    locale = req.session.locale;
  } else if (req.cookies && req.cookies.lang && i18n.getLocales().includes(req.cookies.lang)) {
    locale = req.cookies.lang;
    // Save to session
    if (req.session) {
      req.session.locale = locale;
    }
  } else if (req.headers['accept-language']) {
    // Parse Accept-Language header
    const acceptedLanguages = req.headers['accept-language']
      .split(',')
      .map(lang => lang.split(';')[0].trim().toLowerCase());
    
    for (const lang of acceptedLanguages) {
      if (lang.startsWith('id') || lang.startsWith('in')) {
        locale = 'id';
        break;
      } else if (lang.startsWith('en')) {
        locale = 'en';
        break;
      }
    }
  }

  // Set locale for this request
  i18n.setLocale(req, locale);
  i18n.setLocale(res, locale);

  // Make locale available in templates
  res.locals.locale = locale;
  res.locals.__ = req.__;
  res.locals.__n = req.__n;

  // Add helper functions for templates
  res.locals.t = (key, options) => {
    try {
      // Try using i18n first
      const result = req.__(key, options);
      if (result && result !== key) {
        return result;
      }

      // Fallback to manual translation lookup
      const keys = key.split('.');
      let value = translations[locale];

      for (const k of keys) {
        if (value && typeof value === 'object' && value[k] !== undefined) {
          value = value[k];
        } else {
          // Fallback to English if key not found in current locale
          value = translations.en;
          for (const k2 of keys) {
            if (value && typeof value === 'object' && value[k2] !== undefined) {
              value = value[k2];
            } else {
              return key; // Return key if not found
            }
          }
          break;
        }
      }

      return typeof value === 'string' ? value : key;
    } catch (error) {
      console.error('Translation error for key:', key, error);
      return key;
    }
  };

  res.locals.tn = (singular, plural, count, options) => {
    return req.__n(singular, plural, count, options);
  };

  // Add language switcher helper
  res.locals.getLanguages = () => {
    return [
      { code: 'en', name: 'English', native: 'English' },
      { code: 'id', name: 'Indonesian', native: 'Bahasa Indonesia' }
    ];
  };

  res.locals.getCurrentLanguage = () => {
    const languages = res.locals.getLanguages();
    return languages.find(lang => lang.code === locale) || languages[0];
  };

  res.locals.getLanguageUrl = (langCode) => {
    const url = new URL(req.originalUrl, `${req.protocol}://${req.get('host')}`);
    url.searchParams.set('lang', langCode);
    return url.pathname + url.search;
  };

  next();
};

// Helper function to format dates according to locale
const formatDate = (date, locale = 'en', options = {}) => {
  const defaultOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  };

  const formatOptions = { ...defaultOptions, ...options };
  
  try {
    return new Intl.DateTimeFormat(locale === 'id' ? 'id-ID' : 'en-US', formatOptions).format(new Date(date));
  } catch (error) {
    return new Date(date).toLocaleString();
  }
};

// Helper function to format numbers according to locale
const formatNumber = (number, locale = 'en', options = {}) => {
  try {
    return new Intl.NumberFormat(locale === 'id' ? 'id-ID' : 'en-US', options).format(number);
  } catch (error) {
    return number.toString();
  }
};

// Helper function to format file sizes
const formatFileSize = (bytes, locale = 'en') => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = locale === 'id' 
    ? ['Bytes', 'KB', 'MB', 'GB', 'TB'] 
    : ['Bytes', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const size = parseFloat((bytes / Math.pow(k, i)).toFixed(2));

  return `${formatNumber(size, locale)} ${sizes[i]}`;
};

// Helper function to format duration
const formatDuration = (seconds, locale = 'en') => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
};

// Add helper functions to response locals
const addHelpers = (req, res, next) => {
  res.locals.formatDate = (date, options) => formatDate(date, res.locals.locale, options);
  res.locals.formatNumber = (number, options) => formatNumber(number, res.locals.locale, options);
  res.locals.formatFileSize = (bytes) => formatFileSize(bytes, res.locals.locale);
  res.locals.formatDuration = (seconds) => formatDuration(seconds, res.locals.locale);
  
  next();
};

module.exports = {
  i18n,
  setLocale,
  addHelpers,
  formatDate,
  formatNumber,
  formatFileSize,
  formatDuration
};
