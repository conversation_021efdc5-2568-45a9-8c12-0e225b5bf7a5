const path = require('path');
const fs = require('fs');
const zlib = require('zlib');
const crypto = require('crypto');

class StaticOptimization {
  constructor() {
    this.compressionCache = new Map();
    this.etagCache = new Map();
    this.mimeTypes = {
      '.html': 'text/html',
      '.css': 'text/css',
      '.js': 'application/javascript',
      '.json': 'application/json',
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.svg': 'image/svg+xml',
      '.ico': 'image/x-icon',
      '.woff': 'font/woff',
      '.woff2': 'font/woff2',
      '.ttf': 'font/ttf',
      '.eot': 'application/vnd.ms-fontobject',
      '.mp4': 'video/mp4',
      '.webm': 'video/webm',
      '.ogg': 'video/ogg'
    };
  }

  // Generate ETag for file - DISABLED (no caching)
  generateETag(filePath, stats) {
    // Cache disabled - no ETag generation
    return null;
  }

  // Check if file should be compressed
  shouldCompress(filePath, size) {
    const ext = path.extname(filePath).toLowerCase();
    const compressibleTypes = ['.html', '.css', '.js', '.json', '.svg', '.xml', '.txt'];
    
    // Only compress text files larger than 1KB
    return compressibleTypes.includes(ext) && size > 1024;
  }

  // Compress file content
  async compressContent(content, encoding = 'gzip') {
    return new Promise((resolve, reject) => {
      if (encoding === 'gzip') {
        zlib.gzip(content, (err, compressed) => {
          if (err) reject(err);
          else resolve(compressed);
        });
      } else if (encoding === 'deflate') {
        zlib.deflate(content, (err, compressed) => {
          if (err) reject(err);
          else resolve(compressed);
        });
      } else {
        resolve(content);
      }
    });
  }

  // Get best compression encoding
  getBestEncoding(acceptEncoding) {
    if (!acceptEncoding) return null;
    
    if (acceptEncoding.includes('gzip')) return 'gzip';
    if (acceptEncoding.includes('deflate')) return 'deflate';
    return null;
  }

  // Set cache headers - DISABLED (no caching)
  setCacheHeaders(res, filePath, stats) {
    // Cache disabled - set no-cache headers but allow basic functionality
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate, max-age=0');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    // Still set Last-Modified for conditional requests
    res.setHeader('Last-Modified', stats.mtime.toUTCString());

    return null; // No ETag when caching is disabled
  }

  // Check if client has cached version - DISABLED (no caching)
  isNotModified(req, etag, lastModified) {
    // Cache disabled - always return false (never cached)
    return false;
  }

  // Optimize static file serving middleware
  middleware() {
    return async (req, res, next) => {
      try {
        // Skip if not a static file request
        if (!req.path.startsWith('/public/') && !req.path.startsWith('/uploads/')) {
          return next();
        }

        const filePath = path.join(__dirname, '..', req.path);
        
        // Check if file exists
        if (!fs.existsSync(filePath)) {
          return next();
        }

        const stats = fs.statSync(filePath);
        
        // Skip directories
        if (stats.isDirectory()) {
          return next();
        }

        // Set content type
        const ext = path.extname(filePath).toLowerCase();
        const contentType = this.mimeTypes[ext] || 'application/octet-stream';
        res.setHeader('Content-Type', contentType);

        // Set cache headers and get ETag
        const etag = this.setCacheHeaders(res, filePath, stats);
        
        // Check if client has cached version
        if (this.isNotModified(req, etag, stats.mtime.toUTCString())) {
          res.status(304).end();
          return;
        }

        // Read file content
        const content = fs.readFileSync(filePath);
        
        // Check if compression is supported and beneficial
        const acceptEncoding = req.headers['accept-encoding'];
        const encoding = this.getBestEncoding(acceptEncoding);
        
        if (encoding && this.shouldCompress(filePath, stats.size)) {
          // Cache disabled - compress on-the-fly without caching
          const compressedContent = await this.compressContent(content, encoding);

          res.setHeader('Content-Encoding', encoding);
          res.setHeader('Content-Length', compressedContent.length);
          res.end(compressedContent);
        } else {
          // Serve uncompressed
          res.setHeader('Content-Length', stats.size);
          res.end(content);
        }

      } catch (error) {
        console.error('[Static Optimization] Error:', error);
        next();
      }
    };
  }

  // Precompress static assets
  async precompressAssets(publicDir) {
    try {
      console.log('[Static Optimization] Precompressing static assets...');
      
      const compressibleExts = ['.css', '.js', '.html', '.json', '.svg'];
      let compressedCount = 0;

      const processDirectory = async (dir) => {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
          const itemPath = path.join(dir, item);
          const stats = fs.statSync(itemPath);
          
          if (stats.isDirectory()) {
            await processDirectory(itemPath);
          } else if (stats.isFile()) {
            const ext = path.extname(itemPath).toLowerCase();
            
            if (compressibleExts.includes(ext) && stats.size > 1024) {
              const content = fs.readFileSync(itemPath);
              
              // Create gzip version
              const gzipPath = `${itemPath}.gz`;
              if (!fs.existsSync(gzipPath) || fs.statSync(gzipPath).mtime < stats.mtime) {
                const compressed = await this.compressContent(content, 'gzip');
                fs.writeFileSync(gzipPath, compressed);
                compressedCount++;
              }
            }
          }
        }
      };

      await processDirectory(publicDir);
      console.log(`✅ Precompressed ${compressedCount} static assets`);
      
    } catch (error) {
      console.error('❌ Error precompressing assets:', error);
    }
  }

  // Clean up old compressed files
  cleanupCompressedFiles(publicDir, maxAge = 7 * 24 * 60 * 60 * 1000) { // 7 days
    try {
      const now = Date.now();
      let cleanedCount = 0;

      const processDirectory = (dir) => {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
          const itemPath = path.join(dir, item);
          const stats = fs.statSync(itemPath);
          
          if (stats.isDirectory()) {
            processDirectory(itemPath);
          } else if (item.endsWith('.gz')) {
            // Check if original file still exists
            const originalPath = itemPath.slice(0, -3);
            
            if (!fs.existsSync(originalPath) || (now - stats.mtime.getTime()) > maxAge) {
              fs.unlinkSync(itemPath);
              cleanedCount++;
            }
          }
        }
      };

      processDirectory(publicDir);
      
      if (cleanedCount > 0) {
        console.log(`[Static Optimization] Cleaned up ${cleanedCount} old compressed files`);
      }
      
    } catch (error) {
      console.error('[Static Optimization] Error cleaning up compressed files:', error);
    }
  }

  // Get optimization statistics
  getStats() {
    return {
      compressionCacheSize: this.compressionCache.size,
      etagCacheSize: this.etagCache.size,
      supportedMimeTypes: Object.keys(this.mimeTypes).length
    };
  }

  // Clear caches
  clearCaches() {
    this.compressionCache.clear();
    this.etagCache.clear();
    console.log('[Static Optimization] Caches cleared');
  }
}

// Create singleton instance
const staticOptimization = new StaticOptimization();

module.exports = staticOptimization;
