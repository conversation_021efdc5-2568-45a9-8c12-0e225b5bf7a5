const { db } = require('../db/database');
const { v4: uuidv4 } = require('uuid');

class Notification {
  static async create(data) {
    const id = uuidv4();
    const {
      title,
      message,
      type = 'info', // info, warning, error, success
      category = 'system', // system, users, streams, performance
      priority = 'normal', // low, normal, high, critical
      target_user_id = null, // null for admin notifications
      metadata = null,
      expires_at = null
    } = data;

    return new Promise((resolve, reject) => {
      db.run(`
        INSERT INTO notifications (
          id, title, message, type, category, priority,
          target_user_id, metadata, is_read, expires_at, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 0, ?, datetime('now'))
      `, [
        id, title, message, type, category, priority,
        target_user_id, JSON.stringify(metadata), expires_at
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({
            id,
            title,
            message,
            type,
            category,
            priority,
            target_user_id,
            metadata,
            is_read: false,
            expires_at,
            created_at: new Date().toISOString()
          });
        }
      });
    });
  }



  static async findAll(options = {}) {
    const {
      target_user_id = null,
      is_read = null,
      type = null,
      category = null,
      priority = null,
      limit = 50,
      offset = 0
    } = options;

    let query = `
      SELECT * FROM notifications
      WHERE (expires_at IS NULL OR expires_at > datetime('now'))
    `;
    const params = [];

    if (target_user_id !== undefined) {
      query += ` AND target_user_id ${target_user_id === null ? 'IS NULL' : '= ?'}`;
      if (target_user_id !== null) params.push(target_user_id);
    }

    if (is_read !== null) {
      query += ` AND is_read = ?`;
      params.push(is_read ? 1 : 0);
    }

    if (type) {
      query += ` AND type = ?`;
      params.push(type);
    }

    if (category) {
      query += ` AND category = ?`;
      params.push(category);
    }

    if (priority) {
      query += ` AND priority = ?`;
      params.push(priority);
    }

    query += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    return new Promise((resolve, reject) => {
      db.all(query, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          const notifications = rows.map(row => ({
            ...row,
            metadata: row.metadata ? JSON.parse(row.metadata) : null,
            is_read: Boolean(row.is_read)
          }));
          resolve(notifications);
        }
      });
    });
  }

  static async findById(id) {
    return new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM notifications WHERE id = ?',
        [id],
        (err, row) => {
          if (err) {
            reject(err);
          } else if (row) {
            resolve({
              ...row,
              metadata: row.metadata ? JSON.parse(row.metadata) : null,
              is_read: Boolean(row.is_read)
            });
          } else {
            resolve(null);
          }
        }
      );
    });
  }

  static async markAsRead(id) {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE notifications SET is_read = 1 WHERE id = ?',
        [id],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve(this.changes > 0);
          }
        }
      );
    });
  }

  static async markAllAsRead(target_user_id = null) {
    const query = target_user_id === null
      ? 'UPDATE notifications SET is_read = 1 WHERE target_user_id IS NULL'
      : 'UPDATE notifications SET is_read = 1 WHERE target_user_id = ?';

    const params = target_user_id === null ? [] : [target_user_id];

    return new Promise((resolve, reject) => {
      db.run(query, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });
  }

  static async delete(id) {
    return new Promise((resolve, reject) => {
      db.run(
        'DELETE FROM notifications WHERE id = ?',
        [id],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve(this.changes > 0);
          }
        }
      );
    });
  }

  static async deleteExpired() {
    return new Promise((resolve, reject) => {
      db.run(
        'DELETE FROM notifications WHERE expires_at IS NOT NULL AND expires_at <= datetime("now")',
        [],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve(this.changes);
          }
        }
      );
    });
  }

  static async getUnreadCount(target_user_id = null) {
    const query = target_user_id === null
      ? 'SELECT COUNT(*) as count FROM notifications WHERE target_user_id IS NULL AND is_read = 0 AND (expires_at IS NULL OR expires_at > datetime("now"))'
      : 'SELECT COUNT(*) as count FROM notifications WHERE target_user_id = ? AND is_read = 0 AND (expires_at IS NULL OR expires_at > datetime("now"))';

    const params = target_user_id === null ? [] : [target_user_id];

    return new Promise((resolve, reject) => {
      db.get(query, params, (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row.count);
        }
      });
    });
  }

  static async getStats() {
    return new Promise((resolve, reject) => {
      db.all(`
        SELECT
          type,
          category,
          priority,
          COUNT(*) as count,
          SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread_count
        FROM notifications
        WHERE (expires_at IS NULL OR expires_at > datetime('now'))
        GROUP BY type, category, priority
      `, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // Clean up old notifications (older than 30 days)
  static async cleanup(daysToKeep = 30) {
    return new Promise((resolve, reject) => {
      db.run(
        'DELETE FROM notifications WHERE created_at < datetime("now", "-' + daysToKeep + ' days")',
        [],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve(this.changes);
          }
        }
      );
    });
  }
}

module.exports = Notification;
