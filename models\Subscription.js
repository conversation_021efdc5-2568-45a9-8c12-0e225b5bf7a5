const { db } = require('../db/database');
const { v4: uuidv4 } = require('uuid');

// Storage update locking mechanism to prevent race conditions
const storageLocks = new Map();

class Subscription {
  // Get all subscription plans
  static getAllPlans() {
    return new Promise((resolve, reject) => {
      db.all('SELECT * FROM subscription_plans WHERE is_active = 1 ORDER BY price ASC', [], (err, rows) => {
        if (err) {
          return reject(err);
        }
        resolve(rows.map(row => ({
          ...row,
          features: JSON.parse(row.features || '[]')
        })));
      });
    });
  }

  // Get all subscription plans with subscriber counts
  static getAllPlansWithSubscribers() {
    return new Promise((resolve, reject) => {
      db.all(`
        SELECT
          sp.*,
          COUNT(u.id) as subscriber_count
        FROM subscription_plans sp
        LEFT JOIN users u ON u.plan_type = sp.name
        WHERE sp.is_active = 1
        GROUP BY sp.id
        ORDER BY sp.price ASC
      `, [], (err, rows) => {
        if (err) {
          return reject(err);
        }
        resolve(rows.map(row => ({
          ...row,
          features: JSON.parse(row.features || '[]'),
          subscriber_count: row.subscriber_count || 0
        })));
      });
    });
  }

  // Get plan by ID
  static getPlanById(planId) {
    return new Promise((resolve, reject) => {
      db.get('SELECT * FROM subscription_plans WHERE id = ?', [planId], (err, row) => {
        if (err) {
          return reject(err);
        }
        if (row) {
          row.features = JSON.parse(row.features || '[]');
        }
        resolve(row);
      });
    });
  }

  // Get user's current subscription (excluding Preview plan as it's the default)
  static getUserSubscription(userId) {
    return new Promise((resolve, reject) => {
      db.get(`
        SELECT us.*, sp.name as plan_name, sp.max_streaming_slots, sp.max_storage_gb, sp.features
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.user_id = ? AND us.status = 'active' AND sp.name != 'Preview'
        ORDER BY us.created_at DESC
        LIMIT 1
      `, [userId], (err, row) => {
        if (err) {
          return reject(err);
        }
        if (row) {
          row.features = JSON.parse(row.features || '[]');

          // Check if subscription has expired
          if (row.end_date && new Date(row.end_date) < new Date()) {
            // console.log(`⚠️ Subscription expired for user ${userId}, plan: ${row.plan_name}`); // Removed for production
            // Mark subscription as expired
            this.updateSubscriptionStatus(row.id, 'expired').catch(console.error);
            resolve(null); // Return null for expired subscription
            return;
          }
        }
        resolve(row);
      });
    });
  }

  // Create new subscription
  static createSubscription(subscriptionData) {
    const id = uuidv4();
    const {
      user_id,
      plan_id,
      status = 'active',
      start_date = new Date().toISOString(),
      end_date,
      payment_method,
      payment_id
    } = subscriptionData;

    return new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO user_subscriptions
         (id, user_id, plan_id, status, start_date, end_date, payment_method, payment_id)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [id, user_id, plan_id, status, start_date, end_date, payment_method, payment_id],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({ id, ...subscriptionData });
        }
      );
    });
  }

  // Update subscription status
  static updateSubscriptionStatus(subscriptionId, status) {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE user_subscriptions SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [status, subscriptionId],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({ id: subscriptionId, status });
        }
      );
    });
  }

  // Handle expired subscriptions - downgrade users to Preview plan
  static async handleExpiredSubscription(userId) {
    try {
      const User = require('./User');

      // Get Preview plan details
      const previewPlan = await this.getPlanByName('Preview');
      if (!previewPlan) {
        console.error('❌ Preview plan not found for expired subscription handling');
        return;
      }

      // Delete all user streams since Preview plan has 0 slots
      await this.deleteAllUserStreamsForPreviewPlan(userId);

      // Downgrade user to Preview plan
      await User.updatePlan(
        userId,
        'Preview',
        previewPlan.max_streaming_slots,
        previewPlan.max_storage_gb
      );

      // console.log(`📉 User ${userId} downgraded to Preview plan due to expired subscription`); // Removed for production
      return true;
    } catch (error) {
      console.error('❌ Error handling expired subscription:', error);
      return false;
    }
  }

  // Delete all user streams when downgrading to Preview plan (0 slots)
  static async deleteAllUserStreamsForPreviewPlan(userId) {
    try {
      const Stream = require('./Stream');

      // Stop any active streams first
      const streamingService = require('../services/streamingService');
      const activeStreamIds = streamingService.getActiveStreams();

      // Get user's streams
      const userStreams = await new Promise((resolve, reject) => {
        db.all('SELECT id FROM streams WHERE user_id = ?', [userId], (err, rows) => {
          if (err) reject(err);
          else resolve(rows.map(row => row.id));
        });
      });

      // Stop any active streams belonging to this user
      for (const streamId of userStreams) {
        if (activeStreamIds.includes(streamId)) {
          console.log(`🛑 Stopping active stream ${streamId} due to subscription expiry`);
          try {
            await streamingService.stopStream(streamId);
            // Add delay to ensure stream is properly stopped
            await new Promise(resolve => setTimeout(resolve, 2000));
          } catch (error) {
            console.error(`❌ Error stopping stream ${streamId}:`, error.message);
          }
        }
      }

      // Delete all streams for the user
      const result = await Stream.deleteAllUserStreams(userId);

      if (result.deleted > 0) {
        console.log(`🗑️ Deleted ${result.deleted} streams for user ${userId} due to subscription expiry`);
      }

      return result;
    } catch (error) {
      console.error('❌ Error deleting user streams for Preview plan:', error);
      return { success: false, deleted: 0, streams: [] };
    }
  }

  // Check and handle all expired subscriptions (background job)
  static async checkAndHandleExpiredSubscriptions() {
    try {
      console.log('🔍 Checking for expired subscriptions...');

      const expiredSubscriptions = await new Promise((resolve, reject) => {
        db.all(`
          SELECT us.*, u.username, sp.name as plan_name
          FROM user_subscriptions us
          JOIN users u ON us.user_id = u.id
          JOIN subscription_plans sp ON us.plan_id = sp.id
          WHERE us.status = 'active'
          AND us.end_date < datetime('now')
          AND sp.name != 'Preview'
        `, [], (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });

      if (expiredSubscriptions.length === 0) {
        console.log('✅ No expired subscriptions found');
        return { processed: 0, expired: [] };
      }

      console.log(`⚠️ Found ${expiredSubscriptions.length} expired subscriptions`);

      const processedUsers = [];
      for (const subscription of expiredSubscriptions) {
        try {
          console.log(`📉 Processing expired subscription for user ${subscription.username} (${subscription.plan_name})`);

          // Handle expired subscription (this will stop active streams and downgrade)
          await this.handleExpiredSubscription(subscription.user_id);

          // Mark subscription as expired
          await this.updateSubscriptionStatus(subscription.id, 'expired');

          processedUsers.push({
            userId: subscription.user_id,
            username: subscription.username,
            planName: subscription.plan_name,
            expiredDate: subscription.end_date
          });

        } catch (error) {
          console.error(`❌ Error processing expired subscription for user ${subscription.user_id}:`, error);
        }
      }

      console.log(`✅ Processed ${processedUsers.length} expired subscriptions`);
      return { processed: processedUsers.length, expired: processedUsers };

    } catch (error) {
      console.error('❌ Error checking expired subscriptions:', error);
      return { processed: 0, expired: [], error: error.message };
    }
  }

  // Force stop all active streams for users with insufficient slots
  static async enforceSlotLimits() {
    try {
      console.log('🔍 Enforcing slot limits for all users...');

      const streamingService = require('../services/streamingService');
      const activeStreamIds = streamingService.getActiveStreams();

      if (activeStreamIds.length === 0) {
        console.log('✅ No active streams to check');
        return { stopped: 0, streams: [] };
      }

      const Stream = require('./Stream');
      const stoppedStreams = [];

      // Group active streams by user
      const userStreams = new Map();
      for (const streamId of activeStreamIds) {
        try {
          const stream = await Stream.findById(streamId);
          if (stream) {
            if (!userStreams.has(stream.user_id)) {
              userStreams.set(stream.user_id, []);
            }
            userStreams.get(stream.user_id).push(stream);
          }
        } catch (error) {
          console.error(`❌ Error getting stream ${streamId}:`, error);
        }
      }

      // Check each user's slot limits
      for (const [userId, streams] of userStreams) {
        try {
          const quotaInfo = await this.checkStreamingSlotLimit(userId);
          const activeStreamCount = streams.length;

          if (quotaInfo.hasLimit && activeStreamCount > quotaInfo.maxSlots) {
            const excessStreams = activeStreamCount - quotaInfo.maxSlots;
            console.log(`⚠️ User ${userId} has ${activeStreamCount} active streams but only ${quotaInfo.maxSlots} slots allowed. Stopping ${excessStreams} streams.`);

            // Stop excess streams (oldest first)
            const streamsToStop = streams.slice(0, excessStreams);
            for (const stream of streamsToStop) {
              try {
                console.log(`🛑 Stopping excess stream ${stream.id} for user ${userId}`);
                await streamingService.stopStream(stream.id);
                stoppedStreams.push({
                  streamId: stream.id,
                  userId: userId,
                  title: stream.title,
                  reason: 'Exceeded slot limit'
                });
              } catch (error) {
                console.error(`❌ Error stopping excess stream ${stream.id}:`, error);
              }
            }
          }
        } catch (error) {
          console.error(`❌ Error checking slot limits for user ${userId}:`, error);
        }
      }

      console.log(`✅ Slot limit enforcement completed. Stopped ${stoppedStreams.length} streams.`);
      return { stopped: stoppedStreams.length, streams: stoppedStreams };

    } catch (error) {
      console.error('❌ Error enforcing slot limits:', error);
      return { stopped: 0, streams: [], error: error.message };
    }
  }

  // Get live streams count (for display purposes)
  static async getLiveStreamsCount(userId) {
    try {
      const liveStreams = await new Promise((resolve, reject) => {
        db.get(
          "SELECT COUNT(*) as count FROM streams WHERE user_id = ? AND status = 'live'",
          [userId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row.count);
          }
        );
      });
      return liveStreams;
    } catch (error) {
      console.error('Error getting live streams count:', error);
      return 0;
    }
  }

  // Check if user has reached streaming slot limit
  static async checkStreamingSlotLimit(userId) {
    try {
      const subscription = await this.getUserSubscription(userId);

      // Get user plan limits if no subscription
      let maxSlots = 0; // Default to 0 slots (Preview plan default)
      if (subscription) {
        maxSlots = subscription.max_streaming_slots;
      } else {
        // Get user's plan limits
        const user = await new Promise((resolve, reject) => {
          db.get('SELECT max_streaming_slots FROM users WHERE id = ?', [userId], (err, row) => {
            if (err) reject(err);
            else resolve(row);
          });
        });
        maxSlots = user ? user.max_streaming_slots : 0; // Use user's actual limit, default to 0
      }

      // Count current streams (all streams except those that are explicitly deleted)
      // This includes 'offline', 'live', and 'scheduled' streams as they all occupy slots
      const currentStreams = await new Promise((resolve, reject) => {
        db.get(
          "SELECT COUNT(*) as count FROM streams WHERE user_id = ?",
          [userId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row.count);
          }
        );
      });

      const hasLimit = maxSlots !== -1 && currentStreams >= maxSlots;

      // console.log(`[Quota Check] User ${userId}: ${currentStreams}/${maxSlots} slots used`); // Disabled for cleaner output

      return {
        hasLimit,
        maxSlots: maxSlots === -1 ? 'Unlimited' : maxSlots,
        currentSlots: currentStreams
      };
    } catch (error) {
      console.error('Error checking streaming slot limit:', error);
      return { hasLimit: true, maxSlots: 0, currentSlots: 0 };
    }
  }

  // Check storage limit
  static async checkStorageLimit(userId, additionalSizeGB = 0) {
    try {
      const subscription = await this.getUserSubscription(userId);

      // Get current storage usage from user table
      const user = await new Promise((resolve, reject) => {
        db.get('SELECT used_storage_gb, max_storage_gb FROM users WHERE id = ?', [userId], (err, row) => {
          if (err) reject(err);
          else resolve(row);
        });
      });

      const currentStorage = user ? user.used_storage_gb || 0 : 0;

      // Use subscription limits if available, otherwise use user plan limits
      const maxStorage = subscription ? subscription.max_storage_gb : (user ? user.max_storage_gb || 5 : 5);
      const wouldExceed = (currentStorage + additionalSizeGB) > maxStorage;

      return {
        hasLimit: wouldExceed,
        maxStorage,
        currentStorage,
        availableStorage: maxStorage - currentStorage
      };
    } catch (error) {
      console.error('Error checking storage limit:', error);
      return { hasLimit: true, maxStorage: 5, currentStorage: 0 };
    }
  }

  // Update user storage usage with race condition protection
  static async updateStorageUsage(userId, sizeGB) {
    // Implement simple locking mechanism
    if (storageLocks.has(userId)) {
      await storageLocks.get(userId);
    }

    const lockPromise = this._updateStorageUsageWithLock(userId, sizeGB);
    storageLocks.set(userId, lockPromise);

    try {
      const result = await lockPromise;
      return result;
    } finally {
      storageLocks.delete(userId);
    }
  }

  // Internal storage update function with database serialization
  static _updateStorageUsageWithLock(userId, sizeGB) {
    return new Promise((resolve, reject) => {
      db.serialize(() => {
        db.get('SELECT used_storage_gb FROM users WHERE id = ?', [userId], (err, row) => {
          if (err) {
            console.error('❌ Error getting current storage:', err);
            return reject(err);
          }

          const currentStorage = row ? (row.used_storage_gb || 0) : 0;
          const newStorage = currentStorage + sizeGB;
          const finalStorage = Math.max(0, newStorage);

          if (newStorage < 0) {
            console.warn(`⚠️ Storage would go negative for user ${userId}: ${currentStorage}GB + ${sizeGB}GB = ${newStorage}GB. Setting to 0GB instead.`);
          }

          db.run(
            'UPDATE users SET used_storage_gb = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [finalStorage, userId],
            function (err) {
              if (err) {
                console.error('❌ Error updating storage:', err);
                return reject(err);
              }
              resolve({
                userId,
                addedStorage: sizeGB,
                previousStorage: currentStorage,
                newStorage: finalStorage,
                wasNegativePrevented: newStorage < 0
              });
            }
          );
        });
      });
    });
  }

  // Create new subscription plan
  static createPlan(planData) {
    const id = uuidv4();
    const {
      name,
      price,
      currency = 'USD',
      billing_period = 'monthly',
      max_streaming_slots = 1,
      max_storage_gb = 5,
      features = []
    } = planData;

    return new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO subscription_plans
         (id, name, price, currency, billing_period, max_streaming_slots, max_storage_gb, features)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [id, name, price, currency, billing_period, max_streaming_slots, max_storage_gb, JSON.stringify(features)],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({
            id,
            name,
            price,
            currency,
            billing_period,
            max_streaming_slots,
            max_storage_gb,
            features
          });
        }
      );
    });
  }

  // Update subscription plan
  static updatePlan(planId, planData) {
    const {
      name,
      price,
      currency,
      billing_period,
      max_streaming_slots,
      max_storage_gb,
      features
    } = planData;

    return new Promise((resolve, reject) => {
      db.run(
        `UPDATE subscription_plans
         SET name = ?, price = ?, currency = ?, billing_period = ?,
             max_streaming_slots = ?, max_storage_gb = ?, features = ?,
             updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [name, price, currency, billing_period, max_streaming_slots, max_storage_gb, JSON.stringify(features), planId],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({
            id: planId,
            name,
            price,
            currency,
            billing_period,
            max_streaming_slots,
            max_storage_gb,
            features
          });
        }
      );
    });
  }

  // Delete subscription plan
  static deletePlan(planId) {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE subscription_plans SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [planId],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({ id: planId, deleted: true });
        }
      );
    });
  }

  // Get all plans including inactive ones (admin only)
  static getAllPlansAdmin() {
    return new Promise((resolve, reject) => {
      db.all('SELECT * FROM subscription_plans ORDER BY price ASC', [], (err, rows) => {
        if (err) {
          return reject(err);
        }
        resolve(rows.map(row => ({
          ...row,
          features: JSON.parse(row.features || '[]')
        })));
      });
    });
  }

  // Update plan status (activate/deactivate)
  static updatePlanStatus(planId, isActive) {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE subscription_plans SET is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [isActive ? 1 : 0, planId],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({ id: planId, is_active: isActive });
        }
      );
    });
  }

  // Get subscribers for a specific plan
  static getPlanSubscribers(planId, planName) {
    return new Promise((resolve, reject) => {
      db.all(
        'SELECT id, username, email, role, is_active, created_at FROM users WHERE plan_type = ? ORDER BY created_at DESC',
        [planName],
        (err, rows) => {
          if (err) {
            return reject(err);
          }
          resolve(rows);
        }
      );
    });
  }

  // Check and handle expired trials
  static async checkExpiredTrials() {
    try {
      const User = require('./User');

      return new Promise((resolve, reject) => {
        db.all(`
          SELECT id, username, trial_end_date
          FROM users
          WHERE trial_end_date IS NOT NULL
          AND trial_end_date < datetime('now')
          AND (trial_slots > 0 OR trial_storage_gb > 0)
        `, [], async (err, rows) => {
          if (err) {
            return reject(err);
          }

          const expiredUsers = [];
          for (const user of rows) {
            try {
              await User.removeExpiredTrial(user.id);
              expiredUsers.push({
                id: user.id,
                username: user.username,
                trial_end_date: user.trial_end_date
              });
            } catch (error) {
              console.error(`Failed to remove expired trial for user ${user.id}:`, error);
            }
          }

          resolve(expiredUsers);
        });
      });
    } catch (error) {
      console.error('Error checking expired trials:', error);
      throw error;
    }
  }

  // Get user's subscription including expired ones (for debugging)
  static getUserSubscriptionAll(userId) {
    return new Promise((resolve, reject) => {
      db.get(`
        SELECT us.*, sp.name as plan_name, sp.max_streaming_slots, sp.max_storage_gb, sp.features
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.user_id = ?
        ORDER BY us.created_at DESC
        LIMIT 1
      `, [userId], (err, row) => {
        if (err) {
          return reject(err);
        }
        if (row) {
          row.features = JSON.parse(row.features || '[]');
        }
        resolve(row);
      });
    });
  }

  // Get plan by name (only active plans, case-insensitive)
  static getPlanByName(name) {
    return new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM subscription_plans WHERE LOWER(TRIM(name)) = LOWER(TRIM(?)) AND is_active = 1',
        [name],
        (err, row) => {
          if (err) {
            return reject(err);
          }
          if (row) {
            resolve({
              ...row,
              features: JSON.parse(row.features || '[]')
            });
          } else {
            resolve(null);
          }
        }
      );
    });
  }

  // Get plan by name including inactive plans (for admin operations, case-insensitive)
  static getPlanByNameAll(name) {
    return new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM subscription_plans WHERE LOWER(TRIM(name)) = LOWER(TRIM(?))',
        [name],
        (err, row) => {
          if (err) {
            return reject(err);
          }
          if (row) {
            resolve({
              ...row,
              features: JSON.parse(row.features || '[]')
            });
          } else {
            resolve(null);
          }
        }
      );
    });
  }

  // Calculate prorated upgrade pricing
  static async calculateProratedUpgrade(userId, newPlanId) {
    try {
      // Get current subscription
      const currentSubscription = await this.getUserSubscription(userId);
      if (!currentSubscription) {
        // No current subscription, return full price
        const newPlan = await this.getPlanById(newPlanId);
        if (!newPlan) {
          throw new Error('New plan not found');
        }

        return {
          isUpgrade: false,
          currentPlan: null,
          newPlan: newPlan,
          remainingDays: 0,
          remainingValue: 0,
          newPlanPrice: newPlan.price,
          upgradePrice: newPlan.price,
          adminFee: Math.round(newPlan.price * 0.01), // 1% admin fee
          totalPayment: newPlan.price + Math.round(newPlan.price * 0.01),
          savings: 0
        };
      }

      // Get current and new plan details
      const [currentPlan, newPlan] = await Promise.all([
        this.getPlanById(currentSubscription.plan_id),
        this.getPlanById(newPlanId)
      ]);

      if (!currentPlan || !newPlan) {
        throw new Error('Plan not found');
      }

      // Prevent downgrade or same plan
      if (newPlan.price <= currentPlan.price) {
        throw new Error('Cannot downgrade or select same plan');
      }

      // Calculate remaining days
      const now = new Date();
      const endDate = new Date(currentSubscription.end_date);
      const remainingDays = Math.max(0, Math.ceil((endDate - now) / (1000 * 60 * 60 * 24)));

      // Calculate remaining value from current plan
      const dailyRate = currentPlan.price / 30;
      const remainingValue = Math.round(dailyRate * remainingDays);

      // Calculate upgrade price (new plan price - remaining value)
      const upgradePrice = Math.max(0, newPlan.price - remainingValue);

      // Calculate admin fee (1% of upgrade price)
      const adminFee = Math.round(upgradePrice * 0.01);

      // Total payment including admin fee
      const totalPayment = upgradePrice + adminFee;

      // Calculate savings
      const savings = remainingValue;

      return {
        isUpgrade: true,
        currentPlan: currentPlan,
        newPlan: newPlan,
        remainingDays: remainingDays,
        remainingValue: remainingValue,
        newPlanPrice: newPlan.price,
        upgradePrice: upgradePrice,
        adminFee: adminFee,
        totalPayment: totalPayment,
        savings: savings,
        currentSubscriptionEndDate: currentSubscription.end_date
      };

    } catch (error) {
      console.error('Error calculating prorated upgrade:', error);
      throw error;
    }
  }

  // Create prorated upgrade subscription
  static async createProratedUpgrade(userId, newPlanId, paymentId = null) {
    try {
      // Get current subscription
      const currentSubscription = await this.getUserSubscription(userId);

      // Get new plan details
      const newPlan = await this.getPlanById(newPlanId);
      if (!newPlan) {
        throw new Error('New plan not found');
      }

      let endDate;

      if (currentSubscription) {
        // For upgrades, keep the same end date
        endDate = currentSubscription.end_date;

        // Mark current subscription as upgraded
        await this.updateSubscriptionStatus(currentSubscription.id, 'upgraded');
      } else {
        // For new subscriptions, set 30 days from now
        const newEndDate = new Date();
        newEndDate.setDate(newEndDate.getDate() + 30);
        endDate = newEndDate.toISOString();
      }

      // Create new subscription
      const subscription = await this.createSubscription({
        user_id: userId,
        plan_id: newPlanId,
        status: 'active',
        end_date: endDate,
        payment_method: paymentId ? 'midtrans' : 'manual',
        payment_id: paymentId
      });

      // Update user's plan limits
      const User = require('./User');
      await User.updatePlan(
        userId,
        newPlan.name,
        newPlan.max_streaming_slots,
        newPlan.max_storage_gb
      );

      return subscription;

    } catch (error) {
      console.error('Error creating prorated upgrade:', error);
      throw error;
    }
  }

  // Get all subscriptions with user and plan details (for admin)
  static getAllSubscriptionsWithDetails(limit = 20, offset = 0) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT
          s.*,
          u.username,
          u.email,
          u.plan_type as current_plan,
          p.name as plan_name,
          p.price as plan_price,
          p.currency as plan_currency
        FROM user_subscriptions s
        LEFT JOIN users u ON s.user_id = u.id
        LEFT JOIN subscription_plans p ON s.plan_id = p.id
        ORDER BY s.created_at DESC
        LIMIT ? OFFSET ?
      `;

      db.all(query, [limit, offset], (err, rows) => {
        if (err) {
          return reject(err);
        }
        resolve(rows || []);
      });
    });
  }

  // Count all subscriptions
  static countAllSubscriptions() {
    return new Promise((resolve, reject) => {
      db.get('SELECT COUNT(*) as count FROM user_subscriptions', (err, row) => {
        if (err) {
          return reject(err);
        }
        resolve(row.count || 0);
      });
    });
  }

  // Get subscription by ID
  static getSubscriptionById(subscriptionId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT s.*, p.name as plan_name, u.username
        FROM user_subscriptions s
        LEFT JOIN subscription_plans p ON s.plan_id = p.id
        LEFT JOIN users u ON s.user_id = u.id
        WHERE s.id = ?
      `;

      db.get(query, [subscriptionId], (err, row) => {
        if (err) {
          return reject(err);
        }
        resolve(row || null);
      });
    });
  }

  // Update subscription end date
  static updateSubscriptionEndDate(subscriptionId, endDate) {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE user_subscriptions SET end_date = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [endDate, subscriptionId],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({ id: subscriptionId, end_date: endDate });
        }
      );
    });
  }

  // Get user subscription history
  static getUserSubscriptionHistory(userId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT
          s.*,
          p.name as plan_name,
          p.price as plan_price,
          p.currency as plan_currency
        FROM user_subscriptions s
        LEFT JOIN subscription_plans p ON s.plan_id = p.id
        WHERE s.user_id = ?
        ORDER BY s.created_at DESC
      `;

      db.all(query, [userId], (err, rows) => {
        if (err) {
          return reject(err);
        }
        resolve(rows || []);
      });
    });
  }

  // Update subscription status
  static updateSubscriptionStatus(subscriptionId, status) {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE user_subscriptions SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [status, subscriptionId],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({ id: subscriptionId, status });
        }
      );
    });
  }

  // Get subscription statistics for admin dashboard
  static getSubscriptionStats() {
    return new Promise((resolve, reject) => {
      const queries = [
        'SELECT COUNT(*) as total FROM user_subscriptions',
        'SELECT COUNT(*) as active FROM user_subscriptions WHERE status = "active"',
        'SELECT COUNT(*) as expired FROM user_subscriptions WHERE status = "expired"',
        'SELECT COUNT(*) as cancelled FROM user_subscriptions WHERE status = "cancelled"',
        `SELECT
           p.name as plan_name,
           COUNT(*) as subscribers
         FROM user_subscriptions s
         LEFT JOIN subscription_plans p ON s.plan_id = p.id
         WHERE s.status = "active"
         GROUP BY s.plan_id, p.name`
      ];

      Promise.all(queries.map(query => {
        return new Promise((resolve, reject) => {
          if (query.includes('GROUP BY')) {
            db.all(query, (err, rows) => {
              if (err) reject(err);
              else resolve(rows || []);
            });
          } else {
            db.get(query, (err, row) => {
              if (err) reject(err);
              else resolve(row || {});
            });
          }
        });
      }))
      .then(results => {
        resolve({
          total: results[0].total || 0,
          active: results[1].active || 0,
          expired: results[2].expired || 0,
          cancelled: results[3].cancelled || 0,
          planDistribution: results[4] || []
        });
      })
      .catch(reject);
    });
  }
}

module.exports = Subscription;
