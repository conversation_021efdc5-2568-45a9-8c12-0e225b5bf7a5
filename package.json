{"name": "streamonpod", "version": "2.0.0", "description": "Advanced cloud streaming platform with multi-platform support", "main": "app.js", "scripts": {"start": "node app.js", "start:verbose": "set ENABLE_CONSOLE_LOGGING=true && node app.js", "dev": "set NODE_ENV=development && nodemon app.js", "production": "set NODE_ENV=production && node scripts/production-start.js", "logs:cleanup": "node scripts/cleanup-logs.js", "logs:tail": "tail -f logs/app.log", "logs:errors": "tail -f logs/error.log", "optimize:logs": "node scripts/remove-debug-logs.js", "validate:production": "node scripts/validateProductionConfig.js", "midtrans:switch": "node scripts/switchMidtransMode.js", "midtrans:test": "node scripts/testMidtrans.js", "deploy:production": "node scripts/deploy-production.js", "test:comprehensive": "node scripts/comprehensive-test-suite.js", "test:critical": "node test-critical-fixes.js", "test:high": "node test-high-priority-fixes.js", "test:medium": "node test-medium-priority-fixes.js", "test:deployment": "node test-deployment.js", "monitor:start": "node monitor-deployment.js", "monitor:performance": "node scripts/performance-monitor.js", "health:check": "node scripts/health-check.js", "deploy:complete": "node deploy-complete.js", "deploy:simple": "node simple-deploy.js", "deploy:quick": "node quick-deploy.js", "setup:production": "cp .env.production .env && npm run validate:production", "fix:production-login": "node scripts/fixProductionLogin.js", "optimize:production-logs": "node scripts/optimizeProductionLogs.js", "status:production": "node scripts/productionLoginSummary.js", "test:login": "node scripts/testLogin.js", "check:database": "node scripts/checkDatabase.js", "fix:tunnel": "node scripts/fixTunnelLogin.js", "test:tunnel": "node scripts/testTunnelConfig.js", "start:tunnel": "node scripts/startTunnelApp.js", "reset:password": "node scripts/resetUserPassword.js", "test:login-now": "node scripts/testLoginNow.js", "setup:production-clean": "node scripts/setupProductionClean.js", "production:clean": "npm run setup:production-clean && npm run production", "debug:enable": "node scripts/enableDebugMode.js", "debug:login": "node scripts/testLoginDirect.js", "debug:realtime": "node scripts/realTimeDebug.js", "fix:session-csrf": "node scripts/fixSessionCSRF.js", "test:no-csrf": "node scripts/testWithoutCSRF.js", "fix:prod-logs": "node scripts/fixProductionAndLogs.js", "clean:browser-console": "node scripts/cleanBrowserConsole.js", "restore:browser-console": "node scripts/restoreBrowserConsole.js", "clean:console-logs": "node scripts/remove-production-console-logs.js", "restore:console-logs": "node scripts/restore-console-logs.js"}, "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "axios": "^1.8.1", "bcrypt": "^5.1.1", "cheerio": "^1.0.0", "csrf": "^3.1.0", "dotenv": "^16.5.0", "ejs": "^3.1.9", "ejs-mate": "^4.0.0", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "file-type": "^20.4.0", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.2", "fs-extra": "^11.3.0", "get-video-duration": "^4.1.0", "googleapis": "^146.0.0", "i18n": "^0.15.1", "memorystore": "^1.6.7", "midtrans-client": "^1.4.2", "multer": "^1.4.5-lts.1", "os-utils": "^0.0.14", "socket.io": "^4.8.1", "sqlite3": "^5.1.7", "systeminformation": "^5.25.11", "uuid": "^11.1.0", "video.js": "^8.21.0"}, "devDependencies": {"clean-css": "^5.3.3", "nodemon": "^3.0.1", "sharp": "^0.34.2", "terser": "^5.40.0"}}