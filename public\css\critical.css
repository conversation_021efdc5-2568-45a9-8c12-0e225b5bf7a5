/* Critical CSS for above-the-fold content */
body {
  font-family: 'Inter', sans-serif;
  background-color: #121212;
  color: #ffffff;
  margin: 0;
  padding: 0;
}

.header {
  background-color: #252525;
  padding: 1rem;
  position: sticky;
  top: 0;
  z-index: 50;
}

.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.logo {
  height: 2rem;
  width: auto;
}

.main-content {
  min-height: calc(100vh - 80px);
  padding: 2rem 1rem;
}

.loading {
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.loaded {
  opacity: 1;
}

.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Skeleton loading styles */
.skeleton {
  background: linear-gradient(90deg, #2D2D2D 25%, #3D3D3D 50%, #2D2D2D 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}