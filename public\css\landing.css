/*
 * StreamOnPod Landing Page Styles
 * Modern, responsive design for the landing page
 */

/* Hero Section */
.hero-gradient {
  background: linear-gradient(135deg, #121212 0%, #252525 50%, #121212 100%);
  position: relative;
  overflow: hidden;
}

.hero-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(173, 102, 16, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(173, 102, 16, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.hero-content {
  position: relative;
  z-index: 10;
}

/* Animated background elements */
.floating-element {
  position: absolute;
  opacity: 0.1;
  animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(1) {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.floating-element:nth-child(2) {
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.floating-element:nth-child(3) {
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
}

/* Feature Cards */
.feature-card {
  background: rgba(37, 37, 37, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(173, 102, 16, 0.2);
  border-radius: 16px;
  padding: 2rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #ad6610, #d17f14);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-card:hover {
  transform: translateY(-8px);
  border-color: rgba(173, 102, 16, 0.4);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.feature-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #ad6610, #d17f14);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  transition: transform 0.3s ease;
  box-shadow: 0 4px 12px rgba(173, 102, 16, 0.3);
  position: relative;
}

.feature-icon::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 16px;
  background: linear-gradient(135deg, rgba(255,255,255,0.1), transparent);
  pointer-events: none;
}

.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
}

/* CTA Buttons */
.cta-primary {
  background: linear-gradient(135deg, #ad6610 0%, #d17f14 100%);
  border: none;
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.cta-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(173, 102, 16, 0.4);
  color: white;
  text-decoration: none;
}

.cta-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.cta-primary:hover::before {
  left: 100%;
}

.cta-secondary {
  background: transparent;
  border: 2px solid #ad6610;
  border-radius: 12px;
  padding: 1rem 2rem;
  color: #ad6610;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.cta-secondary:hover {
  background: #ad6610;
  color: white;
  transform: translateY(-2px);
  text-decoration: none;
}

/* Section Styling */
.section-padding {
  padding: 5rem 0;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #ad6610, #d17f14);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 1.2rem;
  text-align: center;
  color: #8F8F8F;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* FAQ Accordion */
.faq-item {
  background: rgba(37, 37, 37, 0.8);
  border: 1px solid rgba(173, 102, 16, 0.2);
  border-radius: 12px;
  margin-bottom: 1rem;
  overflow: hidden;
  transition: all 0.3s ease;
}

.faq-item:hover {
  border-color: rgba(173, 102, 16, 0.4);
}

.faq-question {
  padding: 1.5rem;
  cursor: pointer;
  display: flex;
  justify-content: between;
  align-items: center;
  transition: all 0.3s ease;
}

.faq-question:hover {
  background: rgba(173, 102, 16, 0.1);
}

.faq-answer {
  padding: 0 1.5rem 1.5rem;
  color: #CFCFCF;
  line-height: 1.6;
  display: none;
}

.faq-item.active .faq-answer {
  display: block;
}

.faq-icon {
  transition: transform 0.3s ease;
}

.faq-item.active .faq-icon {
  transform: rotate(180deg);
}

/* Testimonial Cards */
.testimonial-card {
  background: rgba(37, 37, 37, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(173, 102, 16, 0.2);
  border-radius: 16px;
  padding: 2rem;
  transition: all 0.3s ease;
  height: 100%;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  border-color: rgba(173, 102, 16, 0.4);
}

/* Responsive Design */

/* Small mobile devices (up to 480px) */
@media (max-width: 480px) {
  .hero-title {
    font-size: 2.5rem !important;
    line-height: 1.1;
    margin-bottom: 1rem;
  }

  .section-title {
    font-size: 1.75rem;
    line-height: 1.2;
  }

  .section-subtitle {
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .feature-card {
    padding: 1.25rem;
    margin-bottom: 1rem;
  }

  .cta-primary,
  .cta-secondary {
    padding: 0.75rem 1.25rem;
    font-size: 0.9rem;
    width: 100%;
    text-align: center;
  }

  .section-padding {
    padding: 2rem 0;
  }

  /* Extra small mobile logo */
  nav img {
    height: 2.5rem !important;
  }

  /* Platform icons for small screens */
  .platform-icon {
    width: 32px;
    height: 32px;
  }

  .platform-icon i {
    font-size: 1rem !important;
  }

  /* Mobile stats grid */
  .grid.grid-cols-1.md\\:grid-cols-3 {
    gap: 1rem;
  }

  .stat-number {
    font-size: 1.75rem !important;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    letter-spacing: -0.02em;
    /* Ensure proper rendering of special characters like / on mobile */
    font-feature-settings: "kern" 1, "liga" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Force text rendering on mobile */
    text-rendering: optimizeLegibility;
    font-variant-numeric: tabular-nums;
    white-space: nowrap;
  }

  /* Mobile feature icons */
  .feature-icon {
    width: 40px;
    height: 40px;
    margin-bottom: 0.75rem;
  }

  .feature-icon i {
    font-size: 1.25rem !important;
  }

  /* Mobile pricing adjustments */
  .popular-badge {
    font-size: 0.5rem;
    padding: 0.2rem 0.5rem;
    margin-top: -6px !important;
  }

  /* Mobile FAQ adjustments */
  .faq-question h3 {
    font-size: 0.9rem;
  }

  /* Mobile testimonial cards */
  .testimonial-card {
    padding: 1.25rem;
  }

  /* Mobile hero content spacing */
  .hero-content {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  /* Mobile CTA section */
  .text-3xl.md\\:text-4xl {
    font-size: 1.75rem !important;
  }

  .text-xl {
    font-size: 1rem !important;
  }
}

/* Medium mobile devices (481px to 640px) */
@media (min-width: 481px) and (max-width: 640px) {
  .hero-title {
    font-size: 3rem !important;
  }

  .platform-icon {
    width: 36px;
    height: 36px;
  }

  .platform-icon i {
    font-size: 1.1rem !important;
  }

  .feature-icon {
    width: 45px;
    height: 45px;
  }

  .feature-icon i {
    font-size: 1.4rem !important;
  }
}

/* Tablet and larger mobile devices (up to 768px) */
@media (max-width: 768px) {
  .section-title {
    font-size: 2rem;
  }

  .section-subtitle {
    font-size: 1rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .cta-primary,
  .cta-secondary {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }

  .section-padding {
    padding: 3rem 0;
  }

  /* Mobile logo adjustments */
  nav img {
    height: 3rem !important;
  }

  /* Mobile popular badge */
  .popular-badge {
    font-size: 0.625rem;
    padding: 0.25rem 0.75rem;
    margin-top: -8px !important;
  }

  /* Mobile pricing card adjustments */
  .pricing-card-popular {
    margin-top: 16px;
  }

  /* Mobile pricing section */
  .pricing-section {
    padding-top: 4rem;
  }

  /* Mobile feature icons */
  .feature-icon {
    width: 50px;
    height: 50px;
  }

  .feature-icon i {
    font-size: 1.5rem !important;
  }

  /* Mobile navigation improvements */
  .mobile-menu-btn {
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s ease;
  }

  .mobile-menu-btn:hover {
    background-color: rgba(173, 102, 16, 0.1);
  }

  /* Mobile hero section improvements */
  .hero-gradient {
    min-height: 80vh;
    display: flex;
    align-items: center;
  }

  /* Mobile pricing grid improvements */
  .grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4 {
    gap: 1.5rem;
  }

  /* Mobile testimonial improvements */
  .grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3 {
    gap: 1.5rem;
  }
}

/* Scroll animations */
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in-up.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Stats counter animation */
.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #ad6610;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  letter-spacing: -0.02em;
  line-height: 1.2;
  /* Ensure proper rendering of special characters like / */
  font-feature-settings: "kern" 1, "liga" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Force text rendering */
  text-rendering: optimizeLegibility;
  /* Prevent font substitution issues */
  font-variant-numeric: tabular-nums;
  /* Ensure slash character is visible */
  white-space: nowrap;
}

/* CSS-based slash fix using pseudo-element */
.slash-fix::after {
  content: "/";
  display: inline;
  font-weight: inherit;
  color: inherit;
  font-family: inherit;
  font-size: inherit;
  /* Ensure visibility */
  opacity: 1 !important;
  visibility: visible !important;
}

/* Platform icons */
.platform-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.platform-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.platform-youtube { background: #FF0000; }
.platform-facebook { background: #1877F2; }
.platform-twitch { background: #9146FF; }
.platform-tiktok { background: #000000; }
.platform-instagram { background: linear-gradient(45deg, #F58529, #DD2A7B, #8134AF, #515BD4); }

/* Enhanced hero section */
.hero-title {
  background: linear-gradient(135deg, #ffffff 0%, #ad6610 50%, #d17f14 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  0% {
    filter: drop-shadow(0 0 10px rgba(173, 102, 16, 0.3));
  }
  100% {
    filter: drop-shadow(0 0 20px rgba(173, 102, 16, 0.6));
  }
}

/* Enhanced navigation */
.navbar-blur {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(18, 18, 18, 0.9);
  border-bottom: 1px solid rgba(173, 102, 16, 0.2);
}

/* Improved button animations */
.btn-glow {
  position: relative;
  overflow: hidden;
}

.btn-glow::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-glow:hover::after {
  width: 300px;
  height: 300px;
}

/* Enhanced scroll progress */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: linear-gradient(90deg, #ad6610, #d17f14);
  z-index: 9999;
  transition: width 0.1s ease;
  box-shadow: 0 0 10px rgba(173, 102, 16, 0.5);
}

/* Loading animations */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* Popular badge styling */
.popular-badge {
  background: linear-gradient(135deg, #ad6610, #d17f14);
  color: white;
  padding: 0.5rem 1.25rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 700;
  box-shadow: 0 8px 25px rgba(173, 102, 16, 0.6);
  border: 2px solid rgba(255, 255, 255, 0.3);
  position: relative;
  z-index: 50;
  display: inline-block;
  white-space: nowrap;
  transform: translateY(0);
  animation: badgePulse 2s ease-in-out infinite;
  min-width: fit-content;
}

.popular-badge::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 9999px;
  background: linear-gradient(135deg, #ad6610, #d17f14);
  opacity: 0.3;
  z-index: -1;
  animation: badgeGlow 2s ease-in-out infinite alternate;
}

@keyframes badgePulse {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-2px) scale(1.02);
  }
}

@keyframes badgeGlow {
  0% {
    box-shadow: 0 0 20px rgba(173, 102, 16, 0.4);
  }
  100% {
    box-shadow: 0 0 30px rgba(173, 102, 16, 0.8);
  }
}

/* Pricing card enhancements */
.pricing-card-popular {
  border: 2px solid #ad6610;
  box-shadow: 0 10px 30px rgba(173, 102, 16, 0.3);
  transform: scale(1.02);
  margin-top: 20px; /* Extra space for badge */
  overflow: visible; /* Ensure badge is not clipped */
}

.pricing-card-popular:hover {
  transform: scale(1.05) translateY(-8px);
}

/* Ensure pricing section has enough top padding */
.pricing-section {
  padding-top: 6rem;
  overflow: visible;
}

/* Badge container */
.badge-container {
  position: absolute;
  top: -16px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  width: 100%;
  display: flex;
  justify-content: center;
  pointer-events: none;
}

/* Logo enhancements */
.navbar-logo {
  height: 3.5rem;
  transition: all 0.3s ease;
}

.navbar-logo:hover {
  transform: scale(1.05);
}

@media (min-width: 768px) {
  .navbar-logo {
    height: 4.5rem;
  }
}

/* Icon visibility fixes */
.ti {
  font-family: 'tabler-icons' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Ensure icons are visible */
.feature-icon i,
.platform-icon i,
.cta-primary i,
.cta-secondary i {
  position: relative;
  z-index: 10;
  display: inline-block;
}

/* Platform icon improvements */
.platform-icon {
  position: relative;
  overflow: hidden;
}

.platform-icon::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 8px;
  background: linear-gradient(135deg, rgba(255,255,255,0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.platform-icon:hover::after {
  opacity: 1;
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  /* Touch-friendly interactions */
  .cta-primary,
  .cta-secondary,
  .feature-card,
  .faq-question,
  .platform-icon {
    -webkit-tap-highlight-color: rgba(173, 102, 16, 0.2);
    tap-highlight-color: rgba(173, 102, 16, 0.2);
  }

  /* Prevent text selection on touch elements */
  .cta-primary,
  .cta-secondary,
  .faq-question,
  .platform-icon {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Smooth scrolling for mobile */
  html {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Mobile font optimization */
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Mobile button active states */
  .cta-primary:active,
  .cta-secondary:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  /* Mobile card hover effects (touch) */
  .feature-card:active {
    transform: translateY(2px);
    transition: transform 0.1s ease;
  }

  /* Mobile FAQ improvements */
  .faq-question:active {
    background-color: rgba(173, 102, 16, 0.1);
  }

  /* Mobile platform icon touch feedback */
  .platform-icon:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  /* Mobile testimonial card spacing */
  .testimonial-card {
    margin-bottom: 1rem;
  }

  /* Mobile hero section text optimization */
  .hero-title {
    word-break: break-word;
    hyphens: auto;
  }

  /* Mobile pricing card touch improvements */
  .pricing-card-popular:active {
    transform: scale(1.01) translateY(-4px);
  }

  /* Mobile navigation touch improvements */
  #mobile-menu a:active {
    background-color: rgba(173, 102, 16, 0.1);
    border-radius: 0.375rem;
  }
}

/* Performance optimizations for mobile */
@media (max-width: 768px) {
  /* Reduce animations on mobile for better performance */
  .floating-element {
    animation: none;
  }

  /* Simplify gradients on mobile */
  .hero-gradient::before {
    background: rgba(173, 102, 16, 0.05);
  }

  /* Optimize shadows for mobile */
  .feature-card,
  .testimonial-card {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  /* Reduce blur effects on mobile */
  .navbar-blur {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
}

/* Mobile navigation fixes */
@media (max-width: 768px) {
  /* Ensure mobile navigation doesn't block content */
  nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
  }

  /* Add padding to body to account for fixed nav */
  body {
    padding-top: 0;
  }

  /* Hero section should account for fixed nav */
  .hero-gradient {
    padding-top: 4rem; /* Account for fixed nav height */
  }

  /* Mobile menu improvements */
  #mobile-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 999;
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Mobile menu animation */
  #mobile-menu.hidden {
    transform: translateY(-100%);
    opacity: 0;
    transition: all 0.3s ease;
  }

  #mobile-menu:not(.hidden) {
    transform: translateY(0);
    opacity: 1;
    transition: all 0.3s ease;
  }

  /* Mobile platform icons responsive layout */
  .platform-icons-container {
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
  }

  /* Mobile stats responsive layout */
  .stats-container {
    gap: 1rem;
  }

  /* Mobile pricing cards stack properly */
  .pricing-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    max-width: 400px;
    margin: 0 auto;
  }

  /* Mobile testimonials layout */
  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  /* Mobile FAQ improvements */
  .faq-item {
    margin-bottom: 0.5rem;
  }

  .faq-question {
    padding: 1rem;
    font-size: 0.9rem;
  }

  .faq-answer {
    padding: 1rem;
    font-size: 0.85rem;
    line-height: 1.5;
  }

  /* Mobile CTA buttons full width */
  .mobile-cta-container .cta-primary,
  .mobile-cta-container .cta-secondary {
    width: 100%;
    margin-bottom: 0.75rem;
  }

  /* Mobile hero title responsive */
  .hero-title {
    font-size: 2.25rem !important;
    line-height: 1.1;
    margin-bottom: 1rem;
  }

  /* Mobile section spacing */
  .section-padding {
    padding: 2.5rem 0;
  }

  /* Mobile container padding */
  .mobile-container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* Extra small mobile devices (up to 375px) */
@media (max-width: 375px) {
  .hero-title {
    font-size: 2rem !important;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .platform-icon {
    width: 28px;
    height: 28px;
  }

  .platform-icon i {
    font-size: 0.875rem !important;
  }

  .feature-icon {
    width: 36px;
    height: 36px;
  }

  .feature-icon i {
    font-size: 1.125rem !important;
  }

  .cta-primary,
  .cta-secondary {
    padding: 0.625rem 1rem;
    font-size: 0.875rem;
  }

  .feature-card {
    padding: 1rem;
  }

  .testimonial-card {
    padding: 1rem;
  }

  .popular-badge {
    font-size: 0.45rem;
    padding: 0.15rem 0.4rem;
  }

  /* Enhanced footer mobile adjustments */
  .enhanced-footer {
    padding: 3rem 0 2rem;
  }

  .footer-logo {
    height: 3.5rem !important;
  }

  .footer-section-title {
    font-size: 1rem;
  }

  .footer-social-icon {
    width: 40px;
    height: 40px;
  }
}

/* Enhanced Footer Styles */
.enhanced-footer {
  position: relative;
  background: linear-gradient(135deg, #252525 0%, #1a1a1a 100%);
}

.enhanced-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #ad6610, transparent);
}

.footer-logo-container {
  position: relative;
}

.footer-logo {
  height: 4.5rem;
  filter: drop-shadow(0 0 20px rgba(173, 102, 16, 0.3));
  transition: all 0.3s ease;
}

.footer-logo:hover {
  filter: drop-shadow(0 0 30px rgba(173, 102, 16, 0.5));
  transform: scale(1.02);
}

.footer-section {
  position: relative;
}

.footer-section-title {
  font-size: 1.125rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1rem;
  position: relative;
  display: inline-block;
}

.footer-section-divider {
  width: 2rem;
  height: 2px;
  background: linear-gradient(90deg, #ad6610, #d17f14);
  border-radius: 1px;
  margin-bottom: 1.5rem;
}

.footer-links-list {
  list-style: none;
  padding: 0;
  margin: 0;
  space-y: 0.75rem;
}

.footer-links-list li {
  margin-bottom: 0.75rem;
}

.footer-link {
  color: #9ca3af;
  text-decoration: none;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  position: relative;
  display: inline-block;
  padding: 0.25rem 0;
}

.footer-link::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 1px;
  background: linear-gradient(90deg, #ad6610, #d17f14);
  transition: width 0.3s ease;
}

.footer-link:hover {
  color: #ad6610;
  transform: translateX(4px);
}

.footer-link:hover::before {
  width: 100%;
}

.footer-social-link {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: all 0.3s ease;
}

.footer-social-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, rgba(173, 102, 16, 0.1), rgba(209, 127, 20, 0.1));
  border: 1px solid rgba(173, 102, 16, 0.3);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.footer-social-icon::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #ad6610, #d17f14);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12px;
}

.footer-social-link:hover .footer-social-icon {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(173, 102, 16, 0.4);
  border-color: #ad6610;
}

.footer-social-link:hover .footer-social-icon::before {
  opacity: 1;
}

.footer-social-link:hover .footer-social-icon i {
  color: white;
  position: relative;
  z-index: 1;
}

/* Footer social tooltip styles removed */

.footer-bottom {
  position: relative;
}

.footer-copyright {
  font-size: 0.9rem;
}

.footer-brand-accent {
  display: flex;
  align-items: center;
}

/* Mobile footer enhancements */
@media (max-width: 768px) {
  .enhanced-footer {
    padding: 2rem 0 1.5rem;
  }

  .footer-logo {
    height: 3rem;
  }

  .footer-section-title {
    font-size: 1rem;
    margin-bottom: 0.75rem;
  }

  .footer-section-divider {
    width: 1.5rem;
    margin-bottom: 1rem;
  }

  .footer-links-list li {
    margin-bottom: 0.5rem;
  }

  .footer-link {
    font-size: 0.875rem;
    padding: 0.375rem 0;
  }

  .footer-social-icon {
    width: 40px;
    height: 40px;
  }

  .footer-social-icon i {
    font-size: 1.25rem;
  }

  .footer-bottom {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1.5rem;
  }

  .footer-brand-accent {
    justify-content: center;
    margin-top: 0.75rem;
  }

  .footer-copyright p {
    font-size: 0.875rem;
  }

  /* Mobile touch improvements */
  .footer-link {
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .footer-social-link {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .enhanced-footer {
    padding: 1.5rem 0 1rem;
  }

  .footer-logo {
    height: 2.5rem;
  }

  .footer-section-title {
    font-size: 0.9rem;
  }

  .footer-link {
    font-size: 0.8rem;
  }

  .footer-social-icon {
    width: 36px;
    height: 36px;
  }

  .footer-social-icon i {
    font-size: 1.125rem;
  }

  .footer-copyright p {
    font-size: 0.8rem;
    line-height: 1.4;
  }

  /* Improve spacing for very small screens */
  .footer-section {
    margin-bottom: 1.5rem;
  }

  .footer-bottom {
    margin-top: 1.5rem;
    padding-top: 1rem;
  }
}

/* Extra small mobile devices */
@media (max-width: 360px) {
  .enhanced-footer {
    padding: 1rem 0 0.75rem;
  }

  .footer-logo {
    height: 2rem;
  }

  .footer-section-title {
    font-size: 0.85rem;
  }

  .footer-link {
    font-size: 0.75rem;
    padding: 0.25rem 0;
  }

  .footer-social-icon {
    width: 32px;
    height: 32px;
  }

  .footer-social-icon i {
    font-size: 1rem;
  }

  .footer-copyright p {
    font-size: 0.75rem;
  }

  .footer-section {
    margin-bottom: 1rem;
  }

  .footer-bottom {
    margin-top: 1rem;
    padding-top: 0.75rem;
  }

  .footer-brand-accent div {
    height: 2px;
    width: 12px;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .footer-link:hover {
    transform: none;
  }

  .footer-link:active {
    transform: translateX(2px);
    color: #ad6610;
  }

  .footer-social-link:hover .footer-social-icon {
    transform: none;
  }

  .footer-social-link:active .footer-social-icon {
    transform: scale(0.95);
  }

  /* Footer social tooltip touch styles removed */
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .footer-link {
    color: #ffffff;
  }

  .footer-section-title {
    color: #ffffff;
  }

  .footer-section-divider {
    background: #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .footer-link,
  .footer-social-icon,
  .footer-logo {
    transition: none;
  }

  .footer-link:hover {
    transform: none;
  }

  .footer-social-link:hover .footer-social-icon {
    transform: none;
  }
}
