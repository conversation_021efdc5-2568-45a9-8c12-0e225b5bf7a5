/**
 * Prorated Upgrade Styles
 * Styling for plan upgrade calculations and payment UI
 */

/* Upgrade Calculation Container */
.upgrade-calculation,
.new-subscription {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin: 15px 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.upgrade-calculation h4,
.new-subscription h4 {
  color: #ad6610;
  margin-bottom: 15px;
  font-weight: 600;
  border-bottom: 2px solid #ad6610;
  padding-bottom: 8px;
}

/* Plan Information */
.current-plan,
.new-plan,
.plan-info {
  background: white;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 10px;
  border-left: 4px solid #ad6610;
}

.current-plan strong,
.new-plan strong,
.plan-info strong {
  color: #ad6610;
}

.current-plan small,
.new-plan small {
  color: #6c757d;
  font-size: 0.85em;
}

/* Calculation Breakdown */
.calculation-breakdown,
.subscription-details {
  margin-top: 15px;
}

.calculation-breakdown > div,
.subscription-details > div {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.calculation-breakdown > div:last-child,
.subscription-details > div:last-child {
  border-bottom: none;
}

.label {
  font-weight: 500;
  color: #495057;
}

.value {
  font-weight: 600;
  color: #212529;
}

.value.positive {
  color: #28a745;
}

.value.total {
  color: #ad6610;
  font-size: 1.1em;
}

/* Benefits Section */
.upgrade-benefits,
.subscription-benefits {
  background: #e8f5e8;
  border: 1px solid #c3e6c3;
  border-radius: 6px;
  padding: 12px;
  margin-top: 15px;
}

.benefit-text {
  margin: 0;
  color: #155724;
  font-size: 0.9em;
  line-height: 1.5;
}

/* Total Payment Highlight */
.total-payment {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 12px !important;
  margin: 10px 0 !important;
}

/* Modal Specific Styles */
.modal-body #modalCalculation {
  margin: 0;
}

.modal-body .upgrade-calculation,
.modal-body .new-subscription {
  margin: 0;
  box-shadow: none;
  border: none;
  background: transparent;
}

/* Responsive Design */
@media (max-width: 768px) {
  .calculation-breakdown > div,
  .subscription-details > div {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .value {
    align-self: flex-end;
  }
}

/* Loading States */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-spinner {
  background: white;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
}

.loading-spinner .spinner-border {
  color: #ad6610;
}

/* Plan Card Enhancements */
.plan-card {
  position: relative;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.plan-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.plan-card .upgrade-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ad6610;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75em;
  font-weight: 600;
}

/* Price Display */
.plan-price {
  font-size: 1.5em;
  font-weight: 700;
  color: #ad6610;
}

.plan-price .currency {
  font-size: 0.7em;
  font-weight: 500;
}

.plan-price .period {
  font-size: 0.6em;
  color: #6c757d;
  font-weight: 400;
}

/* Upgrade Button Styles */
.btn-upgrade {
  background: linear-gradient(135deg, #ad6610, #d4851a);
  border: none;
  color: white;
  font-weight: 600;
  padding: 10px 20px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.btn-upgrade:hover {
  background: linear-gradient(135deg, #8b5209, #ad6610);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(173, 102, 16, 0.3);
  color: white;
}

.btn-upgrade:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Success/Error Messages */
.alert-upgrade {
  border-radius: 8px;
  border: none;
  font-weight: 500;
}

.alert-upgrade.alert-success {
  background: #d4edda;
  color: #155724;
  border-left: 4px solid #28a745;
}

.alert-upgrade.alert-danger {
  background: #f8d7da;
  color: #721c24;
  border-left: 4px solid #dc3545;
}

.alert-upgrade.alert-warning {
  background: #fff3cd;
  color: #856404;
  border-left: 4px solid #ffc107;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Calculation Table */
.calculation-table {
  width: 100%;
  border-collapse: collapse;
  margin: 15px 0;
}

.calculation-table th,
.calculation-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

.calculation-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #ad6610;
}

.calculation-table .amount {
  text-align: right;
  font-weight: 600;
}

.calculation-table .savings {
  color: #28a745;
}

.calculation-table .total-row {
  background: #fff3cd;
  font-weight: 700;
}

.calculation-table .total-row td {
  border-top: 2px solid #ad6610;
  border-bottom: 2px solid #ad6610;
}
