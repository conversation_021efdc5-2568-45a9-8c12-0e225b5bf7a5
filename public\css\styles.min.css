/* StreamOnPod v2.0 - Enhanced Header Styles */
.sidebar-icon{position:relative;display:flex;flex-direction:column;align-items:center;justify-content:center;height:4rem;width:4rem;margin:0 auto .75rem;color:#fff;border-radius:.75rem;transition-property:all;transition-timing-function:linear;transition-duration:.3s;cursor:pointer}.sidebar-icon:hover{background-color:var(--primary-color);color:#fff;border-radius:.75rem}.sidebar-tooltip{position:absolute;width:auto;padding:.5rem;margin:.5rem;min-width:max-content;left:5rem;border-radius:.375rem;box-shadow:0 1px 3px 0 rgb(0 0 0 / .1);color:#fff;background-color:#121212;font-size:.75rem;font-weight:700;transition-property:all;transition-timing-function:cubic-bezier(0.4,0,0.2,1);transition-duration:.1s;transform:scale(0);transform-origin:left}.sidebar-icon:hover .sidebar-tooltip{transform:scale(1)}.h-20{height:5rem}.header-nav-item{display:flex;align-items:center;gap:.75rem;padding:1rem 1.5rem;color:#9CA3AF;border-radius:1rem;transition:all .4s cubic-bezier(.4,0,.2,1);text-decoration:none;font-size:.875rem;font-weight:600;position:relative;white-space:nowrap;overflow:hidden;margin:0 .25rem}.header-nav-item::before{content:'';position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.1),transparent);transition:left .6s ease}.header-nav-item:hover::before{left:100%}.header-nav-item:hover{background-color:rgba(173,102,16,.2);color:white;transform:translateY(-2px) scale(1.02);box-shadow:0 8px 25px rgba(173,102,16,.3)}.header-nav-item i{font-size:1.25rem;flex-shrink:0;transition:transform .3s ease}.header-nav-item:hover i{transform:scale(1.1)}.header-nav-active{background:linear-gradient(135deg,var(--primary-color),var(--secondary-color));color:white;box-shadow:0 6px 20px rgba(173,102,16,.4);transform:translateY(-1px);position:relative}.header-nav-active::after{content:'';position:absolute;bottom:-4px;left:50%;transform:translateX(-50%);width:90%;height:4px;background:linear-gradient(90deg,transparent,#ffffff,transparent);border-radius:2px;animation:activeGlow 2s ease-in-out infinite alternate;box-shadow:0 0 8px rgba(255,255,255,.5)}@keyframes activeGlow{0%{opacity:.7;box-shadow:0 0 8px rgba(255,255,255,.3)}100%{opacity:1;box-shadow:0 0 12px rgba(255,255,255,.7)}}.header-nav-active:hover{background:linear-gradient(135deg,var(--primary-hover),var(--primary-color));color:white;transform:translateY(-3px) scale(1.02);box-shadow:0 10px 30px rgba(173,102,16,.5)}.header-logo{transition:all .3s cubic-bezier(.4,0,.2,1);filter:brightness(1)}.header-logo:hover{transform:scale(1.05) rotate(1deg);filter:brightness(1.1);cursor:pointer}.header-action-btn{padding:.75rem 1rem;border-radius:.75rem;transition:all .3s cubic-bezier(.4,0,.2,1);color:#9CA3AF;position:relative;overflow:hidden;border:2px solid transparent;background:rgba(45,45,45,.3);backdrop-filter:blur(10px);margin:0 .125rem;cursor:pointer;z-index:10;pointer-events:auto}.header-action-btn::before{content:'';position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(173,102,16,.3),transparent);transition:left .5s ease;z-index:0}.header-action-btn:hover::before{left:100%}.header-action-btn:hover{background:linear-gradient(135deg,rgba(173,102,16,.2),rgba(209,127,20,.2));border-color:rgba(173,102,16,.5);color:white;transform:translateY(-3px) scale(1.05);box-shadow:0 8px 25px rgba(173,102,16,.3),0 0 20px rgba(173,102,16,.1)}.header-action-btn:active{transform:translateY(-1px) scale(1.02)}.header-action-btn i{transition:all .3s ease;position:relative;z-index:1}.header-action-btn:hover i{transform:scale(1.15) rotate(5deg);color:#ffffff;text-shadow:0 0 10px rgba(173,102,16,.5)}.header-action-btn span{position:relative;z-index:1;transition:all .3s ease}.header-action-btn:hover span{color:white;text-shadow:0 0 5px rgba(173,102,16,.3)}.header-action-btn:focus{outline:2px solid rgba(173,102,16,.5);outline-offset:2px}.header-nav-item:focus{outline:2px solid rgba(173,102,16,.5);outline-offset:2px}.header-action-btn.loading{pointer-events:none;opacity:.7}.header-action-btn.loading i{animation:spin 1s linear infinite}@keyframes spin{from{transform:rotate(0deg)}to{transform:rotate(360deg)}}#desktop-profile-dropdown{transform-origin:top right;transition:all .2s cubic-bezier(.4,0,.2,1);opacity:0;transform:translateY(-10px) scale(.95);visibility:hidden;min-width:200px;margin-top:.5rem}#desktop-profile-dropdown.show{opacity:1;transform:translateY(0) scale(1);visibility:visible}#language-dropdown{transition:opacity .2s cubic-bezier(.4,0,.2,1),transform .2s cubic-bezier(.4,0,.2,1);transform-origin:top right}#language-dropdown.show{opacity:1!important;transform:translateY(0) scale(1)!important;visibility:visible}.header-action-btn + div a,.header-action-btn + div button{transition:all .2s ease;transform:translateX(0)}.header-action-btn + div a:hover,.header-action-btn + div button:hover{transform:translateX(4px);background-color:rgba(173,102,16,.1)}#profile-chevron{transition:transform .3s ease}#desktop-profile-btn[aria-expanded="true"] #profile-chevron{transform:rotate(180deg)}.page-indicator{animation:slideInLeft .6s ease-out}@keyframes slideInLeft{from{opacity:0;transform:translateX(-20px)}to{opacity:1;transform:translateX(0)}}:root{--primary-color:#ad6610;--primary-hover:#8b5209;--secondary-color:#d17f14;--accent-color:#FF6B35;--success-color:#10B981;--warning-color:#F59E0B;--error-color:#EF4444;--dark-900:#121212;--dark-800:#252525;--dark-700:#2D2D2D;--dark-600:#3D3D3D;--gradient-primary:linear-gradient(135deg, #ad6610 0%, #d17f14 100%);--gradient-accent:linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%);--shadow-sm:0 1px 2px 0 rgb(0 0 0 / 0.05);--shadow-md:0 4px 6px -1px rgb(0 0 0 / 0.1),0 2px 4px -2px rgb(0 0 0 / 0.1);--shadow-lg:0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);--shadow-xl:0 20px 25px -5px rgb(0 0 0 / 0.1),0 8px 10px -6px rgb(0 0 0 / 0.1)}body{font-family:Inter,sans-serif}.modal-overlay{backdrop-filter:blur(4px);transition:opacity .2s ease-in-out}.modal-container{transform:scale(.95);opacity:0;transition:.2s ease-in-out}.modal-overlay.active{opacity:1}.modal-overlay.active .modal-container{transform:scale(1);opacity:1}[onclick^=setVideoOrientation]{position:relative;overflow:hidden}[onclick^=setVideoOrientation]:before{content:'';position:absolute;inset:0;background:var(--primary-color);opacity:0;transition:opacity .2s ease-in-out}[onclick^=setVideoOrientation].bg-primary:before{opacity:.1}[onclick^=setVideoOrientation]:hover:before{opacity:.05}[onclick^=setVideoOrientation] i{transition:transform .2s ease-in-out}[onclick^=setVideoOrientation]:active i{transform:scale(.9)}@media (max-width:768px){.modal-container{margin:1rem;max-height:calc(100vh - 2rem);overflow-y:auto}.grid-cols-2{grid-template-columns:1fr}.grid-cols-3{grid-template-columns:repeat(2,1fr)}.modal-form-group{margin-bottom:1rem}.modal-input{padding:.75rem;font-size:16px}}@media (max-width:1400px){.header-nav-item{padding:.75rem 1rem;font-size:.8rem}}@media (max-width:1200px){.header-nav-item span{display:none}.header-nav-item{padding:.75rem;min-width:3rem;justify-content:center}}@media (max-width:1024px){.header-nav-item{padding:.5rem;min-width:2.5rem}}.card-enhanced{background:var(--dark-800);border:1px solid var(--dark-600);border-radius:12px;box-shadow:var(--shadow-lg);transition:.3s;position:relative;overflow:hidden}.card-enhanced:hover{transform:translateY(-2px);box-shadow:var(--shadow-xl);border-color:var(--primary-color)}.card-enhanced::before{content:'';position:absolute;top:0;left:0;right:0;height:3px;background:var(--gradient-primary);opacity:0;transition:opacity .3s}.card-enhanced:hover::before{opacity:1}.btn-primary-enhanced{background:var(--gradient-primary);border:none;border-radius:8px;padding:.75rem 1.5rem;color:#fff;font-weight:600;transition:.3s;position:relative;overflow:hidden}.btn-primary-enhanced:hover{transform:translateY(-1px);box-shadow:var(--shadow-lg)}.btn-primary-enhanced::before{content:'';position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.btn-primary-enhanced:hover::before{left:100%}.status-badge{display:inline-flex;align-items:center;gap:.5rem;padding:.375rem .75rem;border-radius:9999px;font-size:.875rem;font-weight:500;text-transform:capitalize}.status-live{background:rgba(16,185,129,.1);color:var(--success-color);border:1px solid rgba(16,185,129,.2)}.status-offline{background:rgba(107,114,128,.1);color:#9ca3af;border:1px solid rgba(107,114,128,.2)}.status-error{background:rgba(239,68,68,.1);color:var(--error-color);border:1px solid rgba(239,68,68,.2)}.status-scheduled{background:rgba(245,158,11,.1);color:var(--warning-color);border:1px solid rgba(245,158,11,.2)}.pulse-dot{width:8px;height:8px;border-radius:50%;animation:2s infinite pulse}@keyframes pulse{0%,100%{opacity:1}50%{opacity:.5}}.glass-effect{background:rgba(37,37,37,.8);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.1)}.gradient-text{background:var(--gradient-primary);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.loading-shimmer{background:linear-gradient(90deg,var(--dark-700) 25%,var(--dark-600) 50%,var(--dark-700) 75%);background-size:200% 100%;animation:1.5s infinite shimmer}@keyframes shimmer{0%{background-position:-200% 0}100%{background-position:200% 0}}