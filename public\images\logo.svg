<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="48" height="48">
  <defs>
    <linearGradient id="streamGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0055FF;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#00AAFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00DDFF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="podGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B35;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8E53;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Main streaming icon -->
  <circle cx="24" cy="24" r="20" fill="url(#streamGradient)" opacity="0.1"/>

  <!-- Streaming waves -->
  <path d="M12 24 Q18 18 24 24 Q30 30 36 24" stroke="url(#streamGradient)" stroke-width="3" fill="none" stroke-linecap="round"/>
  <path d="M8 24 Q16 14 24 24 Q32 34 40 24" stroke="url(#streamGradient)" stroke-width="2" fill="none" stroke-linecap="round" opacity="0.6"/>

  <!-- Pod/microphone element -->
  <ellipse cx="24" cy="28" rx="6" ry="8" fill="url(#podGradient)"/>
  <rect x="21" y="36" width="6" height="4" fill="url(#podGradient)" rx="1"/>
  <circle cx="24" cy="20" r="3" fill="#FFFFFF" opacity="0.9"/>
</svg>
