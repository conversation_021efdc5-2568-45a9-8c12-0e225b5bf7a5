/*
 * StreamOnPod Landing Page JavaScript
 * Handles interactive elements and animations
 */

document.addEventListener('DOMContentLoaded', function() {
  // Mobile menu toggle
  const mobileMenuBtn = document.getElementById('mobile-menu-btn');
  const mobileMenu = document.getElementById('mobile-menu');
  
  if (mobileMenuBtn && mobileMenu) {
    mobileMenuBtn.addEventListener('click', function() {
      mobileMenu.classList.toggle('hidden');
    });
  }

  // Smooth scrolling for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        const offsetTop = target.offsetTop - 80; // Account for fixed navbar
        window.scrollTo({
          top: offsetTop,
          behavior: 'smooth'
        });
        
        // Close mobile menu if open
        if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
          mobileMenu.classList.add('hidden');
        }
      }
    });
  });

  // Scroll animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
      }
    });
  }, observerOptions);

  // Observe all fade-in-up elements
  document.querySelectorAll('.fade-in-up').forEach(el => {
    observer.observe(el);
  });

  // Navbar background on scroll
  const navbar = document.querySelector('nav');
  if (navbar) {
    window.addEventListener('scroll', function() {
      if (window.scrollY > 50) {
        navbar.classList.add('bg-dark-900');
        navbar.classList.remove('bg-dark-900/80');
      } else {
        navbar.classList.remove('bg-dark-900');
        navbar.classList.add('bg-dark-900/80');
      }
    });
  }

  // Stats counter animation
  const statsNumbers = document.querySelectorAll('.stat-number');
  const statsObserver = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        animateCounter(entry.target);
        statsObserver.unobserve(entry.target);
      }
    });
  }, { threshold: 0.5 });

  statsNumbers.forEach(stat => {
    statsObserver.observe(stat);
  });

  // Platform icons hover effect (desktop) and touch effect (mobile)
  document.querySelectorAll('.platform-icon').forEach(icon => {
    // Desktop hover effects
    icon.addEventListener('mouseenter', function() {
      if (!isMobileDevice()) {
        this.style.transform = 'scale(1.1) rotate(5deg)';
      }
    });

    icon.addEventListener('mouseleave', function() {
      if (!isMobileDevice()) {
        this.style.transform = 'scale(1) rotate(0deg)';
      }
    });

    // Mobile touch effects
    icon.addEventListener('touchstart', function(e) {
      if (isMobileDevice()) {
        this.style.transform = 'scale(0.95)';
        e.preventDefault(); // Prevent mouse events on mobile
      }
    });

    icon.addEventListener('touchend', function(e) {
      if (isMobileDevice()) {
        this.style.transform = 'scale(1)';
        e.preventDefault();
      }
    });
  });

  // Feature cards stagger animation
  const featureCards = document.querySelectorAll('.feature-card');
  featureCards.forEach((card, index) => {
    card.style.animationDelay = `${index * 0.1}s`;
  });

  // Testimonial cards stagger animation
  const testimonialCards = document.querySelectorAll('.testimonial-card');
  testimonialCards.forEach((card, index) => {
    card.style.animationDelay = `${index * 0.15}s`;
  });

  // Video tutorial loading handler
  const videoLoading = document.getElementById('video-loading');
  const iframe = document.querySelector('iframe[src*="drive.google.com"]');

  if (iframe && videoLoading) {
    // Hide loading overlay after iframe loads
    iframe.addEventListener('load', function() {
      setTimeout(() => {
        videoLoading.style.opacity = '0';
        setTimeout(() => {
          videoLoading.style.display = 'none';
        }, 500);
      }, 1000); // Wait 1 second before hiding
    });

    // Fallback: hide loading after 5 seconds regardless
    setTimeout(() => {
      if (videoLoading.style.display !== 'none') {
        videoLoading.style.opacity = '0';
        setTimeout(() => {
          videoLoading.style.display = 'none';
        }, 500);
      }
    }, 5000);
  }
});

// FAQ toggle functionality
function toggleFaq(element) {
  const faqItem = element.closest('.faq-item');
  const answer = faqItem.querySelector('.faq-answer');
  const icon = faqItem.querySelector('.faq-icon');
  
  // Close all other FAQ items
  document.querySelectorAll('.faq-item').forEach(item => {
    if (item !== faqItem) {
      item.classList.remove('active');
      item.querySelector('.faq-answer').style.display = 'none';
      item.querySelector('.faq-icon').style.transform = 'rotate(0deg)';
    }
  });
  
  // Toggle current FAQ item
  if (faqItem.classList.contains('active')) {
    faqItem.classList.remove('active');
    answer.style.display = 'none';
    icon.style.transform = 'rotate(0deg)';
  } else {
    faqItem.classList.add('active');
    answer.style.display = 'block';
    icon.style.transform = 'rotate(180deg)';
  }
}

// Counter animation function
function animateCounter(element) {
  const text = element.textContent;
  const hasPercent = text.includes('%');
  const hasPlus = text.includes('+');
  const number = parseFloat(text.replace(/[^\d.]/g, ''));
  
  if (isNaN(number)) return;
  
  let current = 0;
  const increment = number / 50; // 50 steps
  const timer = setInterval(() => {
    current += increment;
    if (current >= number) {
      current = number;
      clearInterval(timer);
    }
    
    let displayValue = Math.floor(current * 10) / 10;
    if (number % 1 === 0) {
      displayValue = Math.floor(current);
    }
    
    element.textContent = displayValue + (hasPercent ? '%' : '') + (hasPlus ? '+' : '');
  }, 20);
}

// Parallax effect for floating elements
window.addEventListener('scroll', function() {
  const scrolled = window.pageYOffset;
  const parallaxElements = document.querySelectorAll('.floating-element');
  
  parallaxElements.forEach((element, index) => {
    const speed = 0.5 + (index * 0.1);
    const yPos = -(scrolled * speed);
    element.style.transform = `translateY(${yPos}px) rotate(${scrolled * 0.01}deg)`;
  });
});

// Button hover effects
document.querySelectorAll('.cta-primary, .cta-secondary').forEach(button => {
  button.addEventListener('mouseenter', function() {
    this.style.transform = 'translateY(-2px)';
  });
  
  button.addEventListener('mouseleave', function() {
    this.style.transform = 'translateY(0)';
  });
});

// Lazy loading for images
if ('IntersectionObserver' in window) {
  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        img.classList.remove('loading');
        imageObserver.unobserve(img);
      }
    });
  });

  document.querySelectorAll('img[data-src]').forEach(img => {
    imageObserver.observe(img);
  });
}

// Smooth reveal animation for sections
const revealSections = document.querySelectorAll('section');
const sectionObserver = new IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      entry.target.style.opacity = '1';
      entry.target.style.transform = 'translateY(0)';
    }
  });
}, {
  threshold: 0.1
});

revealSections.forEach(section => {
  section.style.opacity = '0';
  section.style.transform = 'translateY(20px)';
  section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
  sectionObserver.observe(section);
});

// Add loading state to CTA buttons
document.querySelectorAll('.cta-primary, .cta-secondary').forEach(button => {
  button.addEventListener('click', function(e) {
    // Don't prevent default for external links
    if (this.href && (this.href.includes('register') || this.href.includes('login'))) {
      // Add loading state
      const originalText = this.innerHTML;
      this.innerHTML = '<i class="ti ti-loader-2 animate-spin"></i> Loading...';
      this.style.pointerEvents = 'none';
      
      // Reset after a short delay if navigation doesn't happen
      setTimeout(() => {
        this.innerHTML = originalText;
        this.style.pointerEvents = 'auto';
      }, 3000);
    }
  });
});

// Add scroll progress indicator
const scrollProgress = document.createElement('div');
scrollProgress.className = 'scroll-progress';
document.body.appendChild(scrollProgress);

window.addEventListener('scroll', () => {
  const scrollTop = window.pageYOffset;
  const docHeight = document.body.offsetHeight - window.innerHeight;
  const scrollPercent = (scrollTop / docHeight) * 100;
  scrollProgress.style.width = scrollPercent + '%';
});

// Add typing effect to hero title (optional enhancement)
function typeWriter(element, text, speed = 100) {
  let i = 0;
  element.innerHTML = '';
  
  function type() {
    if (i < text.length) {
      element.innerHTML += text.charAt(i);
      i++;
      setTimeout(type, speed);
    }
  }
  
  type();
}

// Initialize typing effect for hero title if desired
// const heroTitle = document.querySelector('.hero-content h1');
// if (heroTitle) {
//   const originalText = heroTitle.textContent;
//   typeWriter(heroTitle, originalText, 50);
// }

// Mobile device detection
function isMobileDevice() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
         window.innerWidth <= 768;
}

// Mobile-specific optimizations
if (isMobileDevice()) {
  // Disable parallax on mobile for better performance
  window.removeEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const parallaxElements = document.querySelectorAll('.floating-element');

    parallaxElements.forEach((element, index) => {
      const speed = 0.5 + (index * 0.1);
      const yPos = -(scrolled * speed);
      element.style.transform = `translateY(${yPos}px) rotate(${scrolled * 0.01}deg)`;
    });
  });

  // Optimize scroll events for mobile
  let ticking = false;
  function updateScrollProgress() {
    const scrollTop = window.pageYOffset;
    const docHeight = document.body.offsetHeight - window.innerHeight;
    const scrollPercent = (scrollTop / docHeight) * 100;
    document.querySelector('.scroll-progress').style.width = scrollPercent + '%';
    ticking = false;
  }

  window.addEventListener('scroll', function() {
    if (!ticking) {
      requestAnimationFrame(updateScrollProgress);
      ticking = true;
    }
  });

  // Add touch feedback for mobile buttons
  document.querySelectorAll('.cta-primary, .cta-secondary').forEach(button => {
    button.addEventListener('touchstart', function() {
      this.style.transform = 'scale(0.98)';
    });

    button.addEventListener('touchend', function() {
      this.style.transform = 'scale(1)';
    });
  });

  // Optimize mobile menu interactions
  const mobileMenuBtn = document.getElementById('mobile-menu-btn');
  const mobileMenu = document.getElementById('mobile-menu');

  if (mobileMenuBtn && mobileMenu) {
    // Add touch feedback
    mobileMenuBtn.addEventListener('touchstart', function() {
      this.style.backgroundColor = 'rgba(173, 102, 16, 0.1)';
    });

    mobileMenuBtn.addEventListener('touchend', function() {
      this.style.backgroundColor = 'transparent';
    });

    // Close menu when clicking outside
    document.addEventListener('touchstart', function(e) {
      if (!mobileMenu.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
        mobileMenu.classList.add('hidden');
      }
    });
  }

  // Optimize FAQ interactions for mobile
  document.querySelectorAll('.faq-question').forEach(question => {
    question.addEventListener('touchstart', function() {
      this.style.backgroundColor = 'rgba(173, 102, 16, 0.1)';
    });

    question.addEventListener('touchend', function() {
      setTimeout(() => {
        this.style.backgroundColor = 'transparent';
      }, 150);
    });
  });

  // Add swipe gesture for mobile menu (optional enhancement)
  let startX = 0;
  let startY = 0;

  document.addEventListener('touchstart', function(e) {
    startX = e.touches[0].clientX;
    startY = e.touches[0].clientY;
  });

  document.addEventListener('touchmove', function(e) {
    if (!startX || !startY) return;

    const diffX = startX - e.touches[0].clientX;
    const diffY = startY - e.touches[0].clientY;

    // Horizontal swipe detection
    if (Math.abs(diffX) > Math.abs(diffY)) {
      if (diffX > 50 && mobileMenu && !mobileMenu.classList.contains('hidden')) {
        // Swipe left to close menu
        mobileMenu.classList.add('hidden');
      }
    }

    startX = 0;
    startY = 0;
  });
}

// Performance optimization: Reduce animation frequency on mobile
function optimizeAnimationsForMobile() {
  if (isMobileDevice()) {
    // Reduce intersection observer frequency
    const optimizedObserverOptions = {
      threshold: 0.2,
      rootMargin: '0px 0px -100px 0px'
    };

    // Re-initialize observers with optimized settings
    const optimizedObserver = new IntersectionObserver(function(entries) {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('visible');
          optimizedObserver.unobserve(entry.target); // Stop observing once visible
        }
      });
    }, optimizedObserverOptions);

    document.querySelectorAll('.fade-in-up').forEach(el => {
      optimizedObserver.observe(el);
    });
  }
}

// Initialize mobile optimizations
document.addEventListener('DOMContentLoaded', function() {
  optimizeAnimationsForMobile();

  // Add mobile-specific classes
  if (isMobileDevice()) {
    document.body.classList.add('mobile-device');
  }
});
