function e(e){const t=e.closest(".faq-item"),n=t.querySelector(".faq-answer"),s=t.querySelector(".faq-icon");document.querySelectorAll(".faq-item").forEach((e=>{e!==t&&(e.classList.remove("active"),e.querySelector(".faq-answer").style.display="none",e.querySelector(".faq-icon").style.transform="rotate(0deg)")})),t.classList.contains("active")?(t.classList.remove("active"),n.style.display="none",s.style.transform="rotate(0deg)"):(t.classList.add("active"),n.style.display="block",s.style.transform="rotate(180deg)")}function t(e){const t=e.textContent,n=t.includes("%"),s=t.includes("+"),o=parseFloat(t.replace(/[^\d.]/g,""));if(isNaN(o))return;let r=0;const a=o/50,c=setInterval((()=>{r+=a,r>=o&&(r=o,clearInterval(c));let t=Math.floor(10*r)/10;o%1==0&&(t=Math.floor(r)),e.textContent=t+(n?"%":"")+(s?"+":"")}),20)}if(document.addEventListener("DOMContentLoaded",(function(){const e=document.getElementById("mobile-menu-btn"),n=document.getElementById("mobile-menu");e&&n&&e.addEventListener("click",(function(){n.classList.toggle("hidden")})),document.querySelectorAll('a[href^="#"]').forEach((e=>{e.addEventListener("click",(function(e){e.preventDefault();const t=document.querySelector(this.getAttribute("href"));if(t){const e=t.offsetTop-80;window.scrollTo({top:e,behavior:"smooth"}),n&&!n.classList.contains("hidden")&&n.classList.add("hidden")}}))}));const s=new IntersectionObserver((function(e){e.forEach((e=>{e.isIntersecting&&e.target.classList.add("visible")}))}),{threshold:.1,rootMargin:"0px 0px -50px 0px"});document.querySelectorAll(".fade-in-up").forEach((e=>{s.observe(e)}));const o=document.querySelector("nav");o&&window.addEventListener("scroll",(function(){window.scrollY>50?(o.classList.add("bg-dark-900"),o.classList.remove("bg-dark-900/80")):(o.classList.remove("bg-dark-900"),o.classList.add("bg-dark-900/80"))}));const r=document.querySelectorAll(".stat-number"),a=new IntersectionObserver((function(e){e.forEach((e=>{e.isIntersecting&&(t(e.target),a.unobserve(e.target))}))}),{threshold:.5});r.forEach((e=>{a.observe(e)})),document.querySelectorAll(".platform-icon").forEach((e=>{e.addEventListener("mouseenter",(function(){this.style.transform="scale(1.1) rotate(5deg)"})),e.addEventListener("mouseleave",(function(){this.style.transform="scale(1) rotate(0deg)"}))})),document.querySelectorAll(".feature-card").forEach(((e,t)=>{e.style.animationDelay=.1*t+"s"})),document.querySelectorAll(".testimonial-card").forEach(((e,t)=>{e.style.animationDelay=.15*t+"s"}))})),window.addEventListener("scroll",(function(){const e=window.pageYOffset;document.querySelectorAll(".floating-element").forEach(((t,n)=>{const s=-e*(.5+.1*n);t.style.transform=`translateY(${s}px) rotate(${.01*e}deg)`}))})),document.querySelectorAll(".cta-primary, .cta-secondary").forEach((e=>{e.addEventListener("mouseenter",(function(){this.style.transform="translateY(-2px)"})),e.addEventListener("mouseleave",(function(){this.style.transform="translateY(0)"}))})),"IntersectionObserver"in window){const e=new IntersectionObserver(((t,n)=>{t.forEach((t=>{if(t.isIntersecting){const n=t.target;n.src=n.dataset.src,n.classList.remove("loading"),e.unobserve(n)}}))}));document.querySelectorAll("img[data-src]").forEach((t=>{e.observe(t)}))}const n=document.querySelectorAll("section"),s=new IntersectionObserver((e=>{e.forEach((e=>{e.isIntersecting&&(e.target.style.opacity="1",e.target.style.transform="translateY(0)")}))}),{threshold:.1});n.forEach((e=>{e.style.opacity="0",e.style.transform="translateY(20px)",e.style.transition="opacity 0.6s ease, transform 0.6s ease",s.observe(e)})),document.querySelectorAll(".cta-primary, .cta-secondary").forEach((e=>{e.addEventListener("click",(function(e){if(this.href&&(this.href.includes("register")||this.href.includes("login"))){const e=this.innerHTML;this.innerHTML='<i class="ti ti-loader-2 animate-spin"></i> Loading...',this.style.pointerEvents="none",setTimeout((()=>{this.innerHTML=e,this.style.pointerEvents="auto"}),3e3)}}))}));const o=document.createElement("div");function r(e,t,n=100){let s=0;e.innerHTML="",function o(){s<t.length&&(e.innerHTML+=t.charAt(s),s++,setTimeout(o,n))}()}o.className="scroll-progress",document.body.appendChild(o),window.addEventListener("scroll",(()=>{const e=window.pageYOffset/(document.body.offsetHeight-window.innerHeight)*100;o.style.width=e+"%"}));