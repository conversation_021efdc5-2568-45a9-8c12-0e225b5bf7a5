/**
 * Lazy Loading Utilities for StreamOnPod
 * Improves page load performance by loading content on demand
 */

class LazyLoader {
  constructor() {
    this.imageObserver = null;
    this.contentObserver = null;
    this.init();
  }

  init() {
    // Initialize lazy loading for images
    this.initImageLazyLoading();
    
    // Initialize lazy loading for content sections
    this.initContentLazyLoading();
    
    // Initialize lazy loading for video thumbnails
    this.initVideoThumbnailLazyLoading();
  }

  // Lazy load images
  initImageLazyLoading() {
    if ('IntersectionObserver' in window) {
      this.imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            this.loadImage(img);
            this.imageObserver.unobserve(img);
          }
        });
      }, {
        rootMargin: '50px 0px',
        threshold: 0.01
      });

      // Observe all images with data-src attribute
      document.querySelectorAll('img[data-src]').forEach(img => {
        this.imageObserver.observe(img);
      });
    } else {
      // Fallback for browsers without IntersectionObserver
      this.loadAllImages();
    }
  }

  // Load individual image
  loadImage(img) {
    const src = img.dataset.src;
    const srcset = img.dataset.srcset;

    if (src) {
      img.src = src;
    }
    
    if (srcset) {
      img.srcset = srcset;
    }

    img.classList.remove('lazy');
    img.classList.add('loaded');

    // Handle load and error events
    img.onload = () => {
      img.classList.add('fade-in');
    };

    img.onerror = () => {
      img.src = '/images/placeholder.png'; // Fallback image
      img.classList.add('error');
    };
  }

  // Fallback: load all images immediately
  loadAllImages() {
    document.querySelectorAll('img[data-src]').forEach(img => {
      this.loadImage(img);
    });
  }

  // Lazy load content sections
  initContentLazyLoading() {
    if ('IntersectionObserver' in window) {
      this.contentObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const element = entry.target;
            this.loadContent(element);
            this.contentObserver.unobserve(element);
          }
        });
      }, {
        rootMargin: '100px 0px',
        threshold: 0.1
      });

      // Observe elements with lazy-content class
      document.querySelectorAll('.lazy-content').forEach(element => {
        this.contentObserver.observe(element);
      });
    }
  }

  // Load content for lazy sections
  async loadContent(element) {
    const contentType = element.dataset.contentType;
    const contentUrl = element.dataset.contentUrl;

    element.classList.add('loading');

    try {
      switch (contentType) {
        case 'videos':
          await this.loadVideos(element, contentUrl);
          break;
        case 'streams':
          await this.loadStreams(element, contentUrl);
          break;
        case 'stats':
          await this.loadStats(element, contentUrl);
          break;
        default:
          await this.loadGenericContent(element, contentUrl);
      }
    } catch (error) {
      console.error('Error loading lazy content:', error);
      element.innerHTML = '<p class="text-red-400">Failed to load content</p>';
    } finally {
      element.classList.remove('loading');
      element.classList.add('loaded');
    }
  }

  // Load videos lazily
  async loadVideos(element, url) {
    const response = await fetch(url);
    const data = await response.json();

    if (data.success && data.videos) {
      element.innerHTML = this.renderVideoGrid(data.videos);
      // Re-initialize lazy loading for new images
      this.observeNewImages(element);
    }
  }

  // Load streams lazily
  async loadStreams(element, url) {
    const response = await fetch(url);
    const data = await response.json();

    if (data.success && data.streams) {
      element.innerHTML = this.renderStreamList(data.streams);
    }
  }

  // Load stats lazily
  async loadStats(element, url) {
    const response = await fetch(url);
    const data = await response.json();

    if (data.success) {
      element.innerHTML = this.renderStats(data.stats);
    }
  }

  // Load generic content
  async loadGenericContent(element, url) {
    const response = await fetch(url);
    const html = await response.text();
    element.innerHTML = html;
  }

  // Render video grid
  renderVideoGrid(videos) {
    return videos.map(video => `
      <div class="video-card bg-gray-800 rounded-lg overflow-hidden">
        <div class="aspect-video relative">
          <img data-src="${video.thumbnail_path || '/images/default-thumbnail.jpg'}" 
               alt="${video.title}"
               class="lazy w-full h-full object-cover">
          <div class="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
            ${this.formatDuration(video.duration)}
          </div>
        </div>
        <div class="p-4">
          <h3 class="font-semibold text-white truncate">${video.title}</h3>
          <p class="text-gray-400 text-sm">${this.formatFileSize(video.file_size)}</p>
        </div>
      </div>
    `).join('');
  }

  // Render stream list
  renderStreamList(streams) {
    return streams.map(stream => `
      <div class="stream-item flex items-center justify-between p-4 bg-gray-800 rounded-lg">
        <div>
          <h3 class="font-semibold text-white">${stream.title}</h3>
          <p class="text-gray-400 text-sm">${stream.platform}</p>
        </div>
        <div class="flex items-center space-x-2">
          <span class="status-badge ${stream.status === 'live' ? 'bg-green-500' : 'bg-gray-500'} text-white px-2 py-1 rounded text-xs">
            ${stream.status}
          </span>
        </div>
      </div>
    `).join('');
  }

  // Render stats
  renderStats(stats) {
    return `
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="stat-card bg-gray-800 p-4 rounded-lg text-center">
          <div class="text-2xl font-bold text-primary">${stats.total_videos || 0}</div>
          <div class="text-gray-400 text-sm">Videos</div>
        </div>
        <div class="stat-card bg-gray-800 p-4 rounded-lg text-center">
          <div class="text-2xl font-bold text-primary">${stats.total_streams || 0}</div>
          <div class="text-gray-400 text-sm">Streams</div>
        </div>
        <div class="stat-card bg-gray-800 p-4 rounded-lg text-center">
          <div class="text-2xl font-bold text-primary">${stats.active_streams || 0}</div>
          <div class="text-gray-400 text-sm">Live</div>
        </div>
        <div class="stat-card bg-gray-800 p-4 rounded-lg text-center">
          <div class="text-2xl font-bold text-primary">${this.formatFileSize(stats.total_storage_bytes || 0)}</div>
          <div class="text-gray-400 text-sm">Storage</div>
        </div>
      </div>
    `;
  }

  // Observe new images added to the DOM
  observeNewImages(container) {
    if (this.imageObserver) {
      container.querySelectorAll('img[data-src]').forEach(img => {
        this.imageObserver.observe(img);
      });
    }
  }

  // Lazy load video thumbnails with WebP support
  initVideoThumbnailLazyLoading() {
    if ('IntersectionObserver' in window) {
      const thumbnailObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            this.loadVideoThumbnail(img);
            thumbnailObserver.unobserve(img);
          }
        });
      }, {
        rootMargin: '100px 0px',
        threshold: 0.01
      });

      document.querySelectorAll('.video-thumbnail[data-src]').forEach(img => {
        thumbnailObserver.observe(img);
      });
    }
  }

  // Load video thumbnail with WebP fallback
  loadVideoThumbnail(img) {
    const src = img.dataset.src;
    const webpSrc = src.replace(/\.(jpg|jpeg|png)$/i, '.webp');

    // Check if WebP is supported
    if (this.supportsWebP()) {
      img.src = webpSrc;
      img.onerror = () => {
        img.src = src; // Fallback to original format
      };
    } else {
      img.src = src;
    }

    img.classList.remove('lazy');
    img.classList.add('loaded');
  }

  // Check WebP support
  supportsWebP() {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  }

  // Utility functions
  formatDuration(seconds) {
    if (!seconds) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  formatFileSize(bytes) {
    if (!bytes) return '0 B';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  }

  // Public method to manually trigger lazy loading
  loadVisibleContent() {
    document.querySelectorAll('img[data-src]').forEach(img => {
      const rect = img.getBoundingClientRect();
      if (rect.top < window.innerHeight && rect.bottom > 0) {
        this.loadImage(img);
      }
    });
  }

  // Cleanup observers
  destroy() {
    if (this.imageObserver) {
      this.imageObserver.disconnect();
    }
    if (this.contentObserver) {
      this.contentObserver.disconnect();
    }
  }
}

// Initialize lazy loading when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.lazyLoader = new LazyLoader();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = LazyLoader;
}
