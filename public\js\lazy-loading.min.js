class t{constructor(){this.imageObserver=null,this.contentObserver=null,this.init()}init(){this.initImageLazyLoading(),this.initContentLazyLoading(),this.initVideoThumbnailLazyLoading()}initImageLazyLoading(){"IntersectionObserver"in window?(this.imageObserver=new IntersectionObserver((t=>{t.forEach((t=>{if(t.isIntersecting){const e=t.target;this.loadImage(e),this.imageObserver.unobserve(e)}}))}),{rootMargin:"50px 0px",threshold:.01}),document.querySelectorAll("img[data-src]").forEach((t=>{this.imageObserver.observe(t)}))):this.loadAllImages()}loadImage(t){const e=t.dataset.src,s=t.dataset.srcset;e&&(t.src=e),s&&(t.srcset=s),t.classList.remove("lazy"),t.classList.add("loaded"),t.onload=()=>{t.classList.add("fade-in")},t.onerror=()=>{t.src="/images/placeholder.png",t.classList.add("error")}}loadAllImages(){document.querySelectorAll("img[data-src]").forEach((t=>{this.loadImage(t)}))}initContentLazyLoading(){"IntersectionObserver"in window&&(this.contentObserver=new IntersectionObserver((t=>{t.forEach((t=>{if(t.isIntersecting){const e=t.target;this.loadContent(e),this.contentObserver.unobserve(e)}}))}),{rootMargin:"100px 0px",threshold:.1}),document.querySelectorAll(".lazy-content").forEach((t=>{this.contentObserver.observe(t)})))}async loadContent(t){const e=t.dataset.contentType,s=t.dataset.contentUrl;t.classList.add("loading");try{switch(e){case"videos":await this.loadVideos(t,s);break;case"streams":await this.loadStreams(t,s);break;case"stats":await this.loadStats(t,s);break;default:await this.loadGenericContent(t,s)}}catch(e){console.error("Error loading lazy content:",e),t.innerHTML='<p class="text-red-400">Failed to load content</p>'}finally{t.classList.remove("loading"),t.classList.add("loaded")}}async loadVideos(t,e){const s=await fetch(e),a=await s.json();a.success&&a.videos&&(t.innerHTML=this.renderVideoGrid(a.videos),this.observeNewImages(t))}async loadStreams(t,e){const s=await fetch(e),a=await s.json();a.success&&a.streams&&(t.innerHTML=this.renderStreamList(a.streams))}async loadStats(t,e){const s=await fetch(e),a=await s.json();a.success&&(t.innerHTML=this.renderStats(a.stats))}async loadGenericContent(t,e){const s=await fetch(e),a=await s.text();t.innerHTML=a}renderVideoGrid(t){return t.map((t=>`\n      <div class="video-card bg-gray-800 rounded-lg overflow-hidden">\n        <div class="aspect-video relative">\n          <img data-src="${t.thumbnail_path||"/images/default-thumbnail.jpg"}" \n               alt="${t.title}"\n               class="lazy w-full h-full object-cover">\n          <div class="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">\n            ${this.formatDuration(t.duration)}\n          </div>\n        </div>\n        <div class="p-4">\n          <h3 class="font-semibold text-white truncate">${t.title}</h3>\n          <p class="text-gray-400 text-sm">${this.formatFileSize(t.file_size)}</p>\n        </div>\n      </div>\n    `)).join("")}renderStreamList(t){return t.map((t=>`\n      <div class="stream-item flex items-center justify-between p-4 bg-gray-800 rounded-lg">\n        <div>\n          <h3 class="font-semibold text-white">${t.title}</h3>\n          <p class="text-gray-400 text-sm">${t.platform}</p>\n        </div>\n        <div class="flex items-center space-x-2">\n          <span class="status-badge ${"live"===t.status?"bg-green-500":"bg-gray-500"} text-white px-2 py-1 rounded text-xs">\n            ${t.status}\n          </span>\n        </div>\n      </div>\n    `)).join("")}renderStats(t){return`\n      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">\n        <div class="stat-card bg-gray-800 p-4 rounded-lg text-center">\n          <div class="text-2xl font-bold text-primary">${t.total_videos||0}</div>\n          <div class="text-gray-400 text-sm">Videos</div>\n        </div>\n        <div class="stat-card bg-gray-800 p-4 rounded-lg text-center">\n          <div class="text-2xl font-bold text-primary">${t.total_streams||0}</div>\n          <div class="text-gray-400 text-sm">Streams</div>\n        </div>\n        <div class="stat-card bg-gray-800 p-4 rounded-lg text-center">\n          <div class="text-2xl font-bold text-primary">${t.active_streams||0}</div>\n          <div class="text-gray-400 text-sm">Live</div>\n        </div>\n        <div class="stat-card bg-gray-800 p-4 rounded-lg text-center">\n          <div class="text-2xl font-bold text-primary">${this.formatFileSize(t.total_storage_bytes||0)}</div>\n          <div class="text-gray-400 text-sm">Storage</div>\n        </div>\n      </div>\n    `}observeNewImages(t){this.imageObserver&&t.querySelectorAll("img[data-src]").forEach((t=>{this.imageObserver.observe(t)}))}initVideoThumbnailLazyLoading(){if("IntersectionObserver"in window){const t=new IntersectionObserver((e=>{e.forEach((e=>{if(e.isIntersecting){const s=e.target;this.loadVideoThumbnail(s),t.unobserve(s)}}))}),{rootMargin:"100px 0px",threshold:.01});document.querySelectorAll(".video-thumbnail[data-src]").forEach((e=>{t.observe(e)}))}}loadVideoThumbnail(t){const e=t.dataset.src,s=e.replace(/\.(jpg|jpeg|png)$/i,".webp");this.supportsWebP()?(t.src=s,t.onerror=()=>{t.src=e}):t.src=e,t.classList.remove("lazy"),t.classList.add("loaded")}supportsWebP(){const t=document.createElement("canvas");return t.width=1,t.height=1,0===t.toDataURL("image/webp").indexOf("data:image/webp")}formatDuration(t){return t?`${Math.floor(t/60)}:${Math.floor(t%60).toString().padStart(2,"0")}`:"0:00"}formatFileSize(t){if(!t)return"0 B";const e=Math.floor(Math.log(t)/Math.log(1024));return`${(t/Math.pow(1024,e)).toFixed(1)} ${["B","KB","MB","GB"][e]}`}loadVisibleContent(){document.querySelectorAll("img[data-src]").forEach((t=>{const e=t.getBoundingClientRect();e.top<window.innerHeight&&e.bottom>0&&this.loadImage(t)}))}destroy(){this.imageObserver&&this.imageObserver.disconnect(),this.contentObserver&&this.contentObserver.disconnect()}}document.addEventListener("DOMContentLoaded",(()=>{window.lazyLoader=new t})),"undefined"!=typeof module&&module.exports&&(module.exports=t);