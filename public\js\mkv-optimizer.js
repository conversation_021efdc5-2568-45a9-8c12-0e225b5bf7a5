/**
 * MKV Optimizer Management
 * Frontend interface for managing MKV streaming optimization
 */

class MKVOptimizerManager {
  constructor() {
    this.status = null;
    this.refreshInterval = null;
    this.init();
  }

  init() {
    this.loadStatus();
    this.setupEventListeners();
    this.startAutoRefresh();
  }

  setupEventListeners() {
    // Refresh button
    const refreshBtn = document.getElementById('refresh-mkv-status');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => this.loadStatus());
    }

    // Configuration form
    const configForm = document.getElementById('mkv-config-form');
    if (configForm) {
      configForm.addEventListener('submit', (e) => this.updateConfig(e));
    }

    // Check MKV streaming capability
    const checkBtn = document.getElementById('check-mkv-capability');
    if (checkBtn) {
      checkBtn.addEventListener('click', () => this.checkStreamingCapability());
    }
  }

  async loadStatus() {
    try {
      const response = await fetch('/api/mkv-optimizer/status');
      if (!response.ok) throw new Error('Failed to load MKV optimizer status');
      
      this.status = await response.json();
      this.updateUI();
    } catch (error) {
      console.error('Error loading MKV optimizer status:', error);
      this.showError('Failed to load MKV optimizer status');
    }
  }

  updateUI() {
    if (!this.status) return;

    // Update status indicators
    this.updateStatusIndicators();
    this.updateActiveStreams();
    this.updateConfiguration();
    this.updateMetrics();
  }

  updateStatusIndicators() {
    const enabledStatus = document.getElementById('mkv-enabled-status');
    const activeStreamsCount = document.getElementById('mkv-active-streams');
    const maxStreamsLimit = document.getElementById('mkv-max-streams');

    if (enabledStatus) {
      enabledStatus.textContent = this.status.enabled ? 'Enabled' : 'Disabled';
      enabledStatus.className = `badge ${this.status.enabled ? 'badge-success' : 'badge-secondary'}`;
    }

    if (activeStreamsCount) {
      activeStreamsCount.textContent = this.status.activeMkvStreams;
      activeStreamsCount.className = this.status.activeMkvStreams > 0 ? 'text-warning' : 'text-muted';
    }

    if (maxStreamsLimit) {
      maxStreamsLimit.textContent = this.status.maxMkvStreams;
    }
  }

  updateActiveStreams() {
    const container = document.getElementById('mkv-active-streams-list');
    if (!container) return;

    if (this.status.activeStreams.length === 0) {
      container.innerHTML = '<p class="text-muted">No active MKV streams</p>';
      return;
    }

    const streamsHtml = this.status.activeStreams.map(stream => `
      <div class="card mb-2">
        <div class="card-body p-3">
          <div class="row">
            <div class="col-md-3">
              <strong>Stream ID:</strong> ${stream.streamId}
            </div>
            <div class="col-md-3">
              <strong>Quality:</strong> 
              <span class="badge badge-${this.getQualityBadgeClass(stream.qualityLevel)}">
                ${stream.qualityLevel}
              </span>
            </div>
            <div class="col-md-3">
              <strong>Duration:</strong> ${this.formatDuration(stream.duration)}
            </div>
            <div class="col-md-3">
              <strong>CPU at Start:</strong> ${stream.cpuAtStart}%
            </div>
          </div>
        </div>
      </div>
    `).join('');

    container.innerHTML = streamsHtml;
  }

  updateConfiguration() {
    const form = document.getElementById('mkv-config-form');
    if (!form) return;

    // Update form fields with current configuration
    const enabledCheckbox = form.querySelector('#mkv-enabled');
    const maxStreamsInput = form.querySelector('#mkv-max-streams');
    const criticalThreshold = form.querySelector('#mkv-cpu-critical');
    const highThreshold = form.querySelector('#mkv-cpu-high');
    const mediumThreshold = form.querySelector('#mkv-cpu-medium');
    const lowThreshold = form.querySelector('#mkv-cpu-low');

    if (enabledCheckbox) enabledCheckbox.checked = this.status.enabled;
    if (maxStreamsInput) maxStreamsInput.value = this.status.maxMkvStreams;
    if (criticalThreshold) criticalThreshold.value = this.status.cpuThresholds.CRITICAL;
    if (highThreshold) highThreshold.value = this.status.cpuThresholds.HIGH;
    if (mediumThreshold) mediumThreshold.value = this.status.cpuThresholds.MEDIUM;
    if (lowThreshold) lowThreshold.value = this.status.cpuThresholds.LOW;
  }

  updateMetrics() {
    const qualityPresets = document.getElementById('mkv-quality-presets');
    if (!qualityPresets) return;

    const presetsHtml = Object.entries(this.status.qualityPresets).map(([level, preset]) => `
      <div class="col-md-6 mb-3">
        <div class="card">
          <div class="card-header">
            <h6 class="mb-0">${level} Quality</h6>
          </div>
          <div class="card-body p-3">
            <small>
              <strong>Resolution:</strong> ${preset.resolution}<br>
              <strong>Bitrate:</strong> ${preset.bitrate}k<br>
              <strong>FPS:</strong> ${preset.fps}<br>
              <strong>Preset:</strong> ${preset.preset}<br>
              <strong>CRF:</strong> ${preset.crf}
            </small>
          </div>
        </div>
      </div>
    `).join('');

    qualityPresets.innerHTML = presetsHtml;
  }

  async updateConfig(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    
    const config = {
      enabled: formData.get('enabled') === 'on',
      maxMkvStreams: parseInt(formData.get('maxMkvStreams')),
      cpuThresholds: {
        CRITICAL: parseInt(formData.get('cpuCritical')),
        HIGH: parseInt(formData.get('cpuHigh')),
        MEDIUM: parseInt(formData.get('cpuMedium')),
        LOW: parseInt(formData.get('cpuLow'))
      }
    };

    try {
      const response = await fetch('/api/mkv-optimizer/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      });

      if (!response.ok) throw new Error('Failed to update configuration');
      
      const result = await response.json();
      this.showSuccess(result.message);
      this.loadStatus(); // Refresh status
    } catch (error) {
      console.error('Error updating MKV optimizer config:', error);
      this.showError('Failed to update configuration');
    }
  }

  async checkStreamingCapability() {
    try {
      const response = await fetch('/api/mkv-optimizer/can-stream');
      if (!response.ok) throw new Error('Failed to check streaming capability');
      
      const result = await response.json();
      this.showStreamingCapability(result);
    } catch (error) {
      console.error('Error checking streaming capability:', error);
      this.showError('Failed to check streaming capability');
    }
  }

  showStreamingCapability(result) {
    const modal = document.getElementById('mkv-capability-modal');
    const content = document.getElementById('mkv-capability-content');
    
    if (!modal || !content) return;

    let html = '';
    if (result.allowed) {
      html = `
        <div class="alert alert-success">
          <h5>✅ MKV Streaming Allowed</h5>
          <p><strong>Current CPU Usage:</strong> ${result.cpuUsage}%</p>
          <p><strong>Expected Quality:</strong> ${result.expectedQuality}</p>
        </div>
      `;
    } else {
      html = `
        <div class="alert alert-danger">
          <h5>❌ MKV Streaming Not Allowed</h5>
          <p><strong>Reason:</strong> ${result.reason}</p>
          <p><strong>Suggestion:</strong> ${result.suggestion}</p>
        </div>
      `;
    }

    content.innerHTML = html;
    $(modal).modal('show');
  }

  getQualityBadgeClass(quality) {
    const classes = {
      'ULTRA_LOW': 'danger',
      'LOW': 'warning',
      'MEDIUM': 'info',
      'NORMAL': 'success'
    };
    return classes[quality] || 'secondary';
  }

  formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  }

  startAutoRefresh() {
    this.refreshInterval = setInterval(() => {
      this.loadStatus();
    }, 30000); // Refresh every 30 seconds
  }

  stopAutoRefresh() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
    }
  }

  showSuccess(message) {
    this.showNotification(message, 'success');
  }

  showError(message) {
    this.showNotification(message, 'danger');
  }

  showNotification(message, type) {
    const alertHtml = `
      <div class="alert alert-${type} alert-dismissible fade show" role="alert">
        ${message}
        <button type="button" class="close" data-dismiss="alert">
          <span>&times;</span>
        </button>
      </div>
    `;
    
    const container = document.getElementById('mkv-notifications');
    if (container) {
      container.innerHTML = alertHtml;
      setTimeout(() => {
        container.innerHTML = '';
      }, 5000);
    }
  }

  destroy() {
    this.stopAutoRefresh();
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  if (document.getElementById('mkv-optimizer-container')) {
    window.mkvOptimizerManager = new MKVOptimizerManager();
  }
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  if (window.mkvOptimizerManager) {
    window.mkvOptimizerManager.destroy();
  }
});
