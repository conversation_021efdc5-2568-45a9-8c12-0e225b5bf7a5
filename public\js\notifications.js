/**
 * Modern Notification System for StreamOnPod
 * Replaces basic alert() and confirm() with beautiful, animated notifications
 */

class NotificationSystem {
  constructor() {
    this.toastContainer = null;
    this.activeModals = [];
    this.init();
  }

  init() {
    // Create toast container
    this.createToastContainer();
    
    // Add keyboard event listeners
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.activeModals.length > 0) {
        this.closeTopModal();
      }
    });
  }

  createToastContainer() {
    if (!this.toastContainer) {
      this.toastContainer = document.createElement('div');
      this.toastContainer.className = 'toast-container';
      document.body.appendChild(this.toastContainer);
    }
  }

  // Toast Notifications
  showToast(type, title, message, options = {}) {
    const {
      duration = 5000,
      closable = true,
      sound = true
    } = options;

    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    
    const icons = {
      success: 'ti-check',
      error: 'ti-x',
      warning: 'ti-alert-triangle',
      info: 'ti-info-circle'
    };

    toast.innerHTML = `
      <div class="toast-content">
        <div class="toast-icon">
          <i class="ti ${icons[type]}"></i>
        </div>
        <div class="toast-body">
          <div class="toast-title">${title}</div>
          ${message ? `<div class="toast-message">${message}</div>` : ''}
        </div>
      </div>
      ${closable ? '<button class="toast-close"><i class="ti ti-x"></i></button>' : ''}
      ${duration > 0 ? '<div class="toast-progress"></div>' : ''}
    `;

    // Add close functionality
    if (closable) {
      const closeBtn = toast.querySelector('.toast-close');
      closeBtn.addEventListener('click', () => this.removeToast(toast));
    }

    // Add to container
    this.toastContainer.appendChild(toast);

    // Trigger animation
    requestAnimationFrame(() => {
      toast.classList.add('show');
    });

    // Auto remove
    if (duration > 0) {
      setTimeout(() => {
        this.removeToast(toast);
      }, duration);
    }

    // Play sound
    if (sound) {
      this.playNotificationSound(type);
    }

    return toast;
  }

  removeToast(toast) {
    toast.classList.add('hide');
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 400);
  }

  // Modal Dialogs
  showModal(type, title, message, buttons = [], options = {}) {
    return new Promise((resolve) => {
      const {
        closable = true,
        backdrop = true
      } = options;

      const modal = document.createElement('div');
      modal.className = 'modal-overlay';
      
      const icons = {
        success: 'ti-check',
        error: 'ti-x',
        warning: 'ti-alert-triangle',
        info: 'ti-info-circle',
        question: 'ti-help'
      };

      const buttonsHtml = buttons.map(btn => {
        const btnClass = btn.primary ? 'modal-btn-primary' : 
                        btn.danger ? 'modal-btn-danger' : 
                        'modal-btn-secondary';
        return `<button class="modal-btn ${btnClass}" data-action="${btn.action}">${btn.text}</button>`;
      }).join('');

      modal.innerHTML = `
        <div class="modal-dialog modal-${type}">
          <div class="modal-header">
            <div class="modal-icon">
              <i class="ti ${icons[type]}"></i>
            </div>
            <div class="modal-title">${title}</div>
          </div>
          <div class="modal-body">
            <p class="modal-message">${message}</p>
          </div>
          <div class="modal-footer">
            ${buttonsHtml}
          </div>
        </div>
      `;

      // Add event listeners
      const dialog = modal.querySelector('.modal-dialog');
      const buttonElements = modal.querySelectorAll('.modal-btn');
      
      buttonElements.forEach(btn => {
        btn.addEventListener('click', () => {
          const action = btn.dataset.action;
          this.closeModal(modal);
          resolve(action);
        });
      });

      // Backdrop click
      if (backdrop && closable) {
        modal.addEventListener('click', (e) => {
          if (e.target === modal) {
            this.closeModal(modal);
            resolve('backdrop');
          }
        });
      }

      // Prevent dialog click from closing modal
      dialog.addEventListener('click', (e) => {
        e.stopPropagation();
      });

      // Add to DOM and show
      document.body.appendChild(modal);
      this.activeModals.push(modal);
      
      requestAnimationFrame(() => {
        modal.classList.add('show');
      });
    });
  }

  closeModal(modal) {
    modal.classList.remove('show');
    setTimeout(() => {
      if (modal.parentNode) {
        modal.parentNode.removeChild(modal);
      }
      const index = this.activeModals.indexOf(modal);
      if (index > -1) {
        this.activeModals.splice(index, 1);
      }
    }, 300);
  }

  closeTopModal() {
    if (this.activeModals.length > 0) {
      const topModal = this.activeModals[this.activeModals.length - 1];
      this.closeModal(topModal);
    }
  }

  // Convenience methods
  success(title, message, options = {}) {
    return this.showToast('success', title, message, options);
  }

  error(title, message, options = {}) {
    return this.showToast('error', title, message, options);
  }

  warning(title, message, options = {}) {
    return this.showToast('warning', title, message, options);
  }

  info(title, message, options = {}) {
    return this.showToast('info', title, message, options);
  }

  // Alert replacement
  alert(message, title = 'Notification', type = 'info') {
    return this.showModal(type, title, message, [
      { text: 'OK', action: 'ok', primary: true }
    ]);
  }

  // Confirm replacement
  confirm(message, title = 'Confirmation', options = {}) {
    const {
      confirmText = 'Yes',
      cancelText = 'Cancel',
      type = 'question'
    } = options;

    return this.showModal(type, title, message, [
      { text: cancelText, action: 'cancel' },
      { text: confirmText, action: 'confirm', primary: true }
    ]).then(action => action === 'confirm');
  }

  // Prompt replacement with input
  prompt(message, title = 'Input Required', options = {}) {
    const {
      defaultValue = '',
      placeholder = '',
      inputType = 'text',
      confirmText = 'OK',
      cancelText = 'Cancel',
      required = false
    } = options;

    return new Promise((resolve) => {
      const modal = document.createElement('div');
      modal.className = 'modal-overlay';

      modal.innerHTML = `
        <div class="modal-dialog modal-info">
          <div class="modal-header">
            <div class="modal-icon">
              <i class="ti ti-edit"></i>
            </div>
            <div class="modal-title">${title}</div>
          </div>
          <div class="modal-body">
            <p class="modal-message">${message}</p>
            <div class="modal-form-group">
              <input type="${inputType}" class="modal-input" id="promptInput"
                     value="${defaultValue}" placeholder="${placeholder}"
                     ${required ? 'required' : ''}>
            </div>
          </div>
          <div class="modal-footer">
            <button class="modal-btn modal-btn-secondary" data-action="cancel">${cancelText}</button>
            <button class="modal-btn modal-btn-primary" data-action="confirm">${confirmText}</button>
          </div>
        </div>
      `;

      const input = modal.querySelector('#promptInput');
      const buttons = modal.querySelectorAll('.modal-btn');

      buttons.forEach(btn => {
        btn.addEventListener('click', () => {
          const action = btn.dataset.action;
          this.closeModal(modal);

          if (action === 'confirm') {
            const value = input.value.trim();
            if (required && !value) {
              this.warning('Input Required', 'Please enter a value');
              return;
            }
            resolve(value);
          } else {
            resolve(null);
          }
        });
      });

      // Handle Enter key
      input.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          const value = input.value.trim();
          if (!required || value) {
            this.closeModal(modal);
            resolve(value);
          }
        }
      });

      document.body.appendChild(modal);
      this.activeModals.push(modal);

      requestAnimationFrame(() => {
        modal.classList.add('show');
        input.focus();
        input.select();
      });
    });
  }

  // Loading toast
  loading(title, message = '') {
    const toast = this.showToast('info', title, message, { 
      duration: 0, 
      closable: false,
      sound: false 
    });
    
    // Add spinner to icon
    const icon = toast.querySelector('.toast-icon i');
    icon.className = 'spinner';
    
    return {
      close: () => this.removeToast(toast),
      update: (newTitle, newMessage) => {
        toast.querySelector('.toast-title').textContent = newTitle;
        if (newMessage) {
          toast.querySelector('.toast-message').textContent = newMessage;
        }
      }
    };
  }

  // Sound effects
  playNotificationSound(type) {
    try {
      // Create audio context for subtle notification sounds
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      // Different frequencies for different types
      const frequencies = {
        success: 800,
        error: 400,
        warning: 600,
        info: 500
      };
      
      oscillator.frequency.setValueAtTime(frequencies[type] || 500, audioContext.currentTime);
      oscillator.type = 'sine';
      
      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
      
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.2);
    } catch (e) {
      // Silently fail if audio context is not available
    }
  }
}

// Create global instance
const notifications = new NotificationSystem();

// Global convenience functions for backward compatibility
window.showToast = (type, title, message, options) => {
  return notifications.showToast(type, title, message, options);
};

window.showNotification = {
  success: (title, message, options) => notifications.success(title, message, options),
  error: (title, message, options) => notifications.error(title, message, options),
  warning: (title, message, options) => notifications.warning(title, message, options),
  info: (title, message, options) => notifications.info(title, message, options),
  loading: (title, message) => notifications.loading(title, message)
};

// Override native alert, confirm, and prompt
window.alert = (message) => notifications.alert(message);
window.confirm = (message) => notifications.confirm(message);
window.prompt = (message, defaultValue) => notifications.prompt(message, 'Input', { defaultValue: defaultValue || '' });

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = NotificationSystem;
}
