/**
 * Prorated Upgrade Handler
 * Handles plan upgrade calculations and payment processing with prorated pricing
 */

class ProratedUpgradeHandler {
  constructor() {
    this.currentCalculation = null;
    this.isProcessing = false;
  }

  /**
   * Calculate prorated upgrade pricing
   * @param {string} planId - Target plan ID
   * @returns {Promise<Object>} Calculation result
   */
  async calculateUpgrade(planId) {
    try {
      const response = await fetch('/payment/calculate-upgrade', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planId })
      });

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to calculate upgrade');
      }

      this.currentCalculation = data.calculation;
      return data.calculation;
    } catch (error) {
      console.error('Error calculating upgrade:', error);
      throw error;
    }
  }

  /**
   * Display upgrade calculation details
   * @param {Object} calculation - Calculation result
   * @param {string} containerId - Container element ID
   */
  displayCalculation(calculation, containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;

    let html = '';

    if (calculation.isUpgrade) {
      html = `
        <div class="upgrade-calculation">
          <h4>📊 Perhitungan Upgrade Plan</h4>
          <div class="calculation-details">
            <div class="current-plan">
              <strong>Plan Saat Ini:</strong> ${calculation.currentPlan}
              <br><small>Sisa waktu: ${calculation.remainingDays} hari</small>
            </div>
            
            <div class="new-plan">
              <strong>Plan Baru:</strong> ${calculation.newPlan}
              <br><small>Harga normal: ${calculation.newPlanPriceFormatted}</small>
            </div>

            <div class="calculation-breakdown">
              <div class="savings">
                <span class="label">💰 Kredit dari plan lama:</span>
                <span class="value positive">-${calculation.savingsFormatted}</span>
              </div>
              
              <div class="upgrade-price">
                <span class="label">💳 Harga upgrade:</span>
                <span class="value">${calculation.upgradePriceFormatted}</span>
              </div>
              
              <div class="admin-fee">
                <span class="label">⚙️ Biaya admin (1%):</span>
                <span class="value">${calculation.adminFeeFormatted}</span>
              </div>
              
              <hr>
              
              <div class="total-payment">
                <span class="label"><strong>💸 Total Pembayaran:</strong></span>
                <span class="value total"><strong>${calculation.totalPaymentFormatted}</strong></span>
              </div>
            </div>

            <div class="upgrade-benefits">
              <p class="benefit-text">
                ✅ Anda menghemat <strong>${calculation.savingsFormatted}</strong> dari sisa plan lama!
                <br>✅ Masa aktif tetap sampai tanggal yang sama
                <br>✅ Upgrade langsung aktif setelah pembayaran
              </p>
            </div>
          </div>
        </div>
      `;
    } else {
      html = `
        <div class="new-subscription">
          <h4>📋 Langganan Baru</h4>
          <div class="subscription-details">
            <div class="plan-info">
              <strong>Plan:</strong> ${calculation.newPlan}
              <br><strong>Harga:</strong> ${calculation.newPlanPriceFormatted}
              <br><strong>Biaya admin (1%):</strong> ${calculation.adminFeeFormatted}
            </div>
            
            <hr>
            
            <div class="total-payment">
              <span class="label"><strong>💸 Total Pembayaran:</strong></span>
              <span class="value total"><strong>${calculation.totalPaymentFormatted}</strong></span>
            </div>
            
            <div class="subscription-benefits">
              <p class="benefit-text">
                ✅ Masa aktif 30 hari
                <br>✅ Akses penuh ke fitur plan
              </p>
            </div>
          </div>
        </div>
      `;
    }

    container.innerHTML = html;
  }

  /**
   * Process payment with prorated calculation
   * @param {string} planId - Target plan ID
   * @returns {Promise<Object>} Payment result
   */
  async processPayment(planId) {
    if (this.isProcessing) {
      throw new Error('Payment is already being processed');
    }

    this.isProcessing = true;

    try {
      const response = await fetch('/payment/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planId })
      });

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to create payment');
      }

      return data;
    } catch (error) {
      console.error('Error processing payment:', error);
      throw error;
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Show upgrade confirmation modal
   * @param {Object} calculation - Calculation result
   * @param {Function} onConfirm - Callback when user confirms
   */
  showUpgradeModal(calculation, onConfirm) {
    const modalHtml = `
      <div class="modal fade" id="upgradeModal" tabindex="-1">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">
                ${calculation.isUpgrade ? '🔄 Konfirmasi Upgrade Plan' : '📋 Konfirmasi Langganan'}
              </h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <div id="modalCalculation"></div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
              <button type="button" class="btn btn-primary" id="confirmUpgrade">
                ${calculation.isUpgrade ? 'Upgrade Sekarang' : 'Berlangganan Sekarang'}
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('upgradeModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Display calculation in modal
    this.displayCalculation(calculation, 'modalCalculation');

    // Setup confirm button
    document.getElementById('confirmUpgrade').addEventListener('click', () => {
      const modal = bootstrap.Modal.getInstance(document.getElementById('upgradeModal'));
      modal.hide();
      onConfirm();
    });

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('upgradeModal'));
    modal.show();
  }

  /**
   * Handle plan upgrade button click
   * @param {string} planId - Target plan ID
   * @param {string} planName - Target plan name
   */
  async handleUpgradeClick(planId, planName) {
    try {
      // Show loading state
      this.showLoadingState();

      // Calculate upgrade
      const calculation = await this.calculateUpgrade(planId);

      // Show confirmation modal
      this.showUpgradeModal(calculation, async () => {
        try {
          // Process payment
          const paymentData = await this.processPayment(planId);

          // Open Midtrans Snap
          if (paymentData.snap_token) {
            window.snap.pay(paymentData.snap_token, {
              onSuccess: (result) => {
                this.handlePaymentSuccess(result, planName);
              },
              onPending: (result) => {
                this.handlePaymentPending(result);
              },
              onError: (result) => {
                this.handlePaymentError(result);
              },
              onClose: () => {
                this.handlePaymentClose();
              }
            });
          }
        } catch (error) {
          this.showError('Gagal memproses pembayaran: ' + error.message);
        }
      });

    } catch (error) {
      this.showError('Gagal menghitung upgrade: ' + error.message);
    } finally {
      this.hideLoadingState();
    }
  }

  /**
   * Show loading state
   */
  showLoadingState() {
    // Implementation depends on your UI framework
    console.log('Loading...');
  }

  /**
   * Hide loading state
   */
  hideLoadingState() {
    // Implementation depends on your UI framework
    console.log('Loading complete');
  }

  /**
   * Show error message
   * @param {string} message - Error message
   */
  showError(message) {
    alert('Error: ' + message);
  }

  /**
   * Handle payment success
   */
  handlePaymentSuccess(result, planName) {
    alert(`Pembayaran berhasil! Plan ${planName} telah aktif.`);
    window.location.reload();
  }

  /**
   * Handle payment pending
   */
  handlePaymentPending(result) {
    alert('Pembayaran sedang diproses. Silakan tunggu konfirmasi.');
  }

  /**
   * Handle payment error
   */
  handlePaymentError(result) {
    alert('Pembayaran gagal. Silakan coba lagi.');
  }

  /**
   * Handle payment close
   */
  handlePaymentClose() {
    console.log('Payment popup closed');
  }
}

// Initialize global instance
window.proratedUpgradeHandler = new ProratedUpgradeHandler();
