// Stream Modal JavaScript
let selectedVideoData = null;
let modalCurrentOrientation = 'horizontal'; // Renamed to avoid conflicts
let isDropdownOpen = false;
let videoSelectorDropdown = null;
let desktopVideoPlayer = null;
let mobileVideoPlayer = null;
let streamKeyTimeout = null;
let isStreamKeyValid = true;
let currentPlatform = 'Custom';

// Modal functions
function openNewStreamModal() {
  // console.log('openNewStreamModal called from stream-modal.js'); // Removed for production
  const modal = document.getElementById('newStreamModal');
  if (!modal) {
    console.error('New stream modal not found!');
    return;
  }

  // console.log('Modal element found, opening...'); // Removed for production
  // console.log('Modal classes before:', modal.className); // Removed for production
  // BRUTE FORCE APPROACH - Force modal to show with inline styles
  modal.style.cssText = `
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 99999 !important;
    background: rgba(0, 0, 0, 0.7) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    opacity: 1 !important;
    visibility: visible !important;
  `;

  // Also force the modal container to be visible with better styling
  const modalContainer = modal.querySelector('.modal-container');
  if (modalContainer) {
    modalContainer.style.cssText = `
      background: linear-gradient(135deg, #252525 0%, #121212 100%) !important;
      border-radius: 20px !important;
      box-shadow: 0 30px 60px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(173, 102, 16, 0.2) !important;
      max-width: 95vw !important;
      max-height: 95vh !important;
      width: 100% !important;
      transform: scale(1) !important;
      opacity: 1 !important;
      visibility: visible !important;
      display: flex !important;
      flex-direction: column !important;
      overflow: hidden !important;
      backdrop-filter: blur(20px) !important;
      border: 1px solid rgba(173, 102, 16, 0.2) !important;
    `;

    // Apply modern styling
    applyModernModalStyling(modalContainer);

    // console.log('Modal container styled with modern design'); // Removed for production
  } else {
    console.error('Modal container not found!');
  }

  // Also remove hidden class and add show class
  modal.classList.remove('hidden');
  modal.classList.add('show');

  // Prevent body scroll
  document.body.style.overflow = 'hidden';

  // console.log('Modal classes after:', modal.className); // Removed for production
  // console.log('Modal inline styles:', modal.style.cssText); // Removed for production
  // console.log('New stream modal FORCED to be visible'); // Removed for production
  // Reset advanced settings
  const advancedSettingsContent = document.getElementById('advancedSettingsContent');
  const advancedSettingsToggle = document.getElementById('advancedSettingsToggle');
  if (advancedSettingsContent && advancedSettingsToggle) {
    advancedSettingsContent.classList.add('hidden');
    const icon = advancedSettingsToggle.querySelector('i');
    if (icon) icon.style.transform = '';
  }

  // Re-initialize platform selector after modal is shown
  setTimeout(() => {
    initializePlatformSelector();

    // Fix video preview if it exists
    fixVideoPreview();
  }, 100);
}

function closeNewStreamModal() {
  // console.log('closeNewStreamModal called from stream-modal.js'); // Removed for production
  const modal = document.getElementById('newStreamModal');
  if (!modal) {
    console.error('New stream modal not found!');
    return;
  }

  // console.log('Modal classes before close:', modal.className); // Removed for production
  // Remove show class and add hidden class
  modal.classList.remove('show');
  modal.classList.add('hidden');

  // Clear any inline styles that might interfere
  modal.removeAttribute('style');

  // Restore body scroll immediately
  document.body.style.overflow = 'auto';

  // console.log('Modal classes after close:', modal.className); // Removed for production
  // console.log('New stream modal closed'); // Removed for production
  // Reset form and cleanup
  resetModalForm();

  // Reset advanced settings
  const advancedSettingsContent = document.getElementById('advancedSettingsContent');
  const advancedSettingsToggle = document.getElementById('advancedSettingsToggle');
  if (advancedSettingsContent && advancedSettingsToggle) {
    advancedSettingsContent.classList.add('hidden');
    const icon = advancedSettingsToggle.querySelector('i');
    if (icon) icon.style.transform = '';
  }

  // Cleanup video players
  if (desktopVideoPlayer) {
    desktopVideoPlayer.pause();
    desktopVideoPlayer.dispose();
    desktopVideoPlayer = null;
  }
  if (mobileVideoPlayer) {
    mobileVideoPlayer.pause();
    mobileVideoPlayer.dispose();
    mobileVideoPlayer = null;
  }
}

function resetModalForm() {
  const form = document.getElementById('newStreamForm');
  form.reset();
  selectedVideoData = null;
  document.getElementById('selectedVideo').textContent = 'Choose a video...';
  const desktopPreview = document.getElementById('videoPreview');
  const desktopEmptyPreview = document.getElementById('emptyPreview');
  const mobilePreview = document.getElementById('videoPreviewMobile');
  const mobileEmptyPreview = document.getElementById('emptyPreviewMobile');
  desktopPreview.classList.add('hidden');
  mobilePreview.classList.add('hidden');
  desktopEmptyPreview.classList.remove('hidden');
  mobileEmptyPreview.classList.remove('hidden');
  desktopPreview.querySelector('video source').src = '';
  mobilePreview.querySelector('video source').src = '';
}

// Video orientation and resolution functions
function setVideoOrientation(orientation) {
  const resolutionSelect = document.getElementById('resolutionSelect');
  const currentValue = resolutionSelect.value;
  
  // Clear existing options
  resolutionSelect.innerHTML = '';
  
  let resolutions;
  if (orientation === 'horizontal') {
    resolutions = [
      { value: '1920x1080', label: '1080p (1920x1080)' },
      { value: '1280x720', label: '720p (1280x720)' },
      { value: '854x480', label: '480p (854x480)' },
      { value: '640x360', label: '360p (640x360)' }
    ];
  } else {
    resolutions = [
      { value: '1080x1920', label: '1080p Vertical (1080x1920)' },
      { value: '720x1280', label: '720p Vertical (720x1280)' },
      { value: '480x854', label: '480p Vertical (480x854)' },
      { value: '360x640', label: '360p Vertical (360x640)' }
    ];
  }
  
  resolutions.forEach(res => {
    const option = document.createElement('option');
    option.value = res.value;
    option.textContent = res.label;
    resolutionSelect.appendChild(option);
  });
  
  // Try to maintain the same resolution if possible
  if (currentValue && resolutionSelect.querySelector(`option[value="${currentValue}"]`)) {
    resolutionSelect.value = currentValue;
  } else {
    resolutionSelect.value = resolutions[0].value;
  }
  
  updateResolutionDisplay();
}

function updateResolutionDisplay() {
  const resolutionSelect = document.getElementById('resolutionSelect');
  const resolutionDisplay = document.getElementById('currentResolution');

  if (resolutionSelect && resolutionDisplay) {
    const selectedOption = resolutionSelect.options[resolutionSelect.selectedIndex];
    if (selectedOption) {
      // Extract resolution from the option value
      const resolutionValue = selectedOption.value;
      resolutionDisplay.textContent = resolutionValue;
    }
  }
}

// Stream key visibility toggle
function toggleStreamKeyVisibility() {
  const streamKeyInput = document.getElementById('streamKey');
  const streamKeyToggle = document.getElementById('streamKeyToggle');
  if (streamKeyInput.type === 'password') {
    streamKeyInput.type = 'text';
    streamKeyToggle.className = 'ti ti-eye-off';
  } else {
    streamKeyInput.type = 'password';
    streamKeyToggle.className = 'ti ti-eye';
  }
}

// Platform selector functions
let platformSelectorInitialized = false;

function initializePlatformSelector() {
  const platformSelector = document.getElementById('platformSelector');
  const platformDropdown = document.getElementById('platformDropdown');
  const rtmpInput = document.getElementById('rtmpUrl');

  if (!platformSelector || !platformDropdown || !rtmpInput) {
    return;
  }

  // Remove any existing event listeners by cloning the element
  const newPlatformSelector = platformSelector.cloneNode(true);
  platformSelector.parentNode.replaceChild(newPlatformSelector, platformSelector);

  // Add click event listener to platform selector
  newPlatformSelector.addEventListener('click', function (e) {
    e.preventDefault();
    e.stopPropagation();

    const dropdown = document.getElementById('platformDropdown');

    // Force show dropdown with darker theme colors to match app
    dropdown.style.display = 'block';
    dropdown.style.position = 'absolute';
    dropdown.style.top = '100%';
    dropdown.style.right = '0';
    dropdown.style.zIndex = '9999';
    dropdown.style.backgroundColor = '#252525'; // StreamOnPod dark background
    dropdown.style.border = '1px solid rgba(173, 102, 16, 0.3)'; // StreamOnPod brand border
    dropdown.style.borderRadius = '8px';
    dropdown.style.width = '192px';
    dropdown.style.marginTop = '4px';
    dropdown.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2)';
    dropdown.classList.remove('hidden');
  });

  // Add platform option event listeners
  const platformOptions = platformDropdown.querySelectorAll('.platform-option');

  platformOptions.forEach((option, index) => {
    option.addEventListener('click', function (e) {
      e.preventDefault();
      e.stopPropagation();
      const platformUrl = this.getAttribute('data-url');
      const platformName = this.querySelector('span').textContent;
      rtmpInput.value = platformUrl;
      platformDropdown.style.display = 'none';
      platformDropdown.classList.add('hidden');
      updatePlatformIcon(this.querySelector('i').className);
    });
  });

  // Close dropdown when clicking outside
  document.addEventListener('click', function(e) {
    if (!newPlatformSelector.contains(e.target) && !platformDropdown.contains(e.target)) {
      platformDropdown.style.display = 'none';
      platformDropdown.classList.add('hidden');
    }
  });
}



function updatePlatformIcon(iconClass) {
  const currentIcon = document.querySelector('#platformSelector i');
  if (!currentIcon) return;
  
  const iconParts = iconClass.split(' ');
  const brandIconPart = iconParts.filter(part => part.startsWith('ti-'))[0];
  
  if (!brandIconPart) return;
  
  currentIcon.className = `ti ${brandIconPart} text-center`;
  if (brandIconPart.includes('youtube')) {
    currentIcon.classList.add('text-red-500');
  } else if (brandIconPart.includes('twitch')) {
    currentIcon.classList.add('text-purple-500');
  } else if (brandIconPart.includes('facebook')) {
    currentIcon.classList.add('text-blue-500');
  } else if (brandIconPart.includes('instagram')) {
    currentIcon.classList.add('text-pink-500');
  } else if (brandIconPart.includes('tiktok')) {
    currentIcon.classList.add('text-white');
  } else if (brandIconPart.includes('shopee')) {
    currentIcon.classList.add('text-orange-500');
  } else if (brandIconPart.includes('live-photo')) {
    currentIcon.classList.add('text-teal-500');
  } else {
    currentIcon.classList.add('text-gray-400');
  }
}

// Stream key validation
function validateStreamKeyForPlatform(streamKey, platform) {
  if (!streamKey.trim()) {
    return;
  }
  fetch(`/api/streams/check-key?key=${encodeURIComponent(streamKey)}`)
    .then(response => response.json())
    .then(data => {
      const streamKeyInput = document.getElementById('streamKey');
      if (data.isInUse) {
        streamKeyInput.classList.add('border-red-500');
        streamKeyInput.classList.remove('border-gray-600', 'focus:border-primary');
        let errorMsg = document.getElementById('streamKeyError');
        if (!errorMsg) {
          errorMsg = document.createElement('div');
          errorMsg.id = 'streamKeyError';
          errorMsg.className = 'text-xs text-red-500 mt-1';
          streamKeyInput.parentNode.appendChild(errorMsg);
        }
        errorMsg.textContent = 'This stream key is already in use. Please use a different key.';
        isStreamKeyValid = false;
      } else {
        streamKeyInput.classList.remove('border-red-500');
        streamKeyInput.classList.add('border-gray-600', 'focus:border-primary');
        const errorMsg = document.getElementById('streamKeyError');
        if (errorMsg) {
          errorMsg.remove();
        }
        isStreamKeyValid = true;
      }
    })
    .catch(error => {
      // Error handling for production
    });
}

// Platform detection and stream key validation setup
function setupStreamKeyValidation() {
  const streamKeyInput = document.getElementById('streamKey');
  const rtmpInput = document.getElementById('rtmpUrl');
  
  if (!streamKeyInput || !rtmpInput) return;
  
  // Platform detection based on RTMP URL
  rtmpInput.addEventListener('input', function () {
    const url = this.value.toLowerCase();
    if (url.includes('youtube.com')) {
      currentPlatform = 'YouTube';
    } else if (url.includes('facebook.com')) {
      currentPlatform = 'Facebook';
    } else if (url.includes('twitch.tv')) {
      currentPlatform = 'Twitch';
    } else if (url.includes('tiktok.com')) {
      currentPlatform = 'TikTok';
    } else if (url.includes('instagram.com')) {
      currentPlatform = 'Instagram';
    } else if (url.includes('shopee.io')) {
      currentPlatform = 'Shopee Live';
    } else if (url.includes('restream.io')) {
      currentPlatform = 'Restream.io';
    } else {
      currentPlatform = 'Custom';
    }
    
    if (streamKeyInput.value) {
      validateStreamKeyForPlatform(streamKeyInput.value, currentPlatform);
    }
  });
  
  // Stream key validation with debounce
  streamKeyInput.addEventListener('input', function () {
    clearTimeout(streamKeyTimeout);
    const streamKey = this.value.trim();
    if (!streamKey) {
      return;
    }
    streamKeyTimeout = setTimeout(() => {
      validateStreamKeyForPlatform(streamKey, currentPlatform);
    }, 500);
  });
}

// Toast notification fallback
function ensureToastFunction() {
  if (typeof showToast !== 'function') {
    window.showToast = function (type, message) {
      // Fallback for production
    };
  }
}

// Video selector functions
function toggleVideoSelector() {
  // Disable console logging for production (always disabled for cleaner console)
  // console.log('🎬 Toggle video selector called'); // Disabled for production

  if (!videoSelectorDropdown) {
    // console.error('❌ Video selector dropdown not found'); // Disabled for production
    return;
  }

  const wasHidden = videoSelectorDropdown.classList.contains('hidden');
  // console.log('📋 Dropdown was hidden:', wasHidden); // Disabled for production

  if (wasHidden) {
    // Opening dropdown
    videoSelectorDropdown.classList.remove('hidden');
    isDropdownOpen = true;

    // Load videos when dropdown is opened for the first time
    if (!videoSelectorDropdown.dataset.loaded) {
      // console.log('🔄 Loading videos for first time...'); // Disabled for production
      loadGalleryVideos();
      videoSelectorDropdown.dataset.loaded = 'true';
    }

    // Focus search input when opened
    const searchInput = document.getElementById('videoSearchInput');
    if (searchInput) {
      setTimeout(() => searchInput.focus(), 100);
    }
  } else {
    // Closing dropdown
    videoSelectorDropdown.classList.add('hidden');
    isDropdownOpen = false;

    // Clear search when closed
    const searchInput = document.getElementById('videoSearchInput');
    if (searchInput) {
      searchInput.value = '';
    }
  }

  // console.log('📋 Dropdown is now open:', isDropdownOpen); // Disabled for production
}

function selectVideo(videoId, videoTitle, videoPath) {
  // console.log('🎯 Selecting video:', { videoId, videoTitle, videoPath }); // Disabled for production

  selectedVideoData = { id: videoId, title: videoTitle, path: videoPath };

  // Update hidden input and display text
  const hiddenInput = document.getElementById('selectedVideoId');
  if (hiddenInput) {
    hiddenInput.value = videoId;
    // console.log('✅ Updated hidden input value:', videoId); // Disabled for production
  }

  const selectedVideoText = document.getElementById('selectedVideo');
  if (selectedVideoText) {
    selectedVideoText.textContent = videoTitle;
    // console.log('✅ Updated display text:', videoTitle); // Disabled for production
  }

  // Show preview containers and create video preview
  const desktopPreview = document.getElementById('videoPreview');
  const desktopEmptyPreview = document.getElementById('emptyPreview');
  const mobilePreview = document.getElementById('videoPreviewMobile');
  const mobileEmptyPreview = document.getElementById('emptyPreviewMobile');

  if (desktopPreview && mobilePreview) {
    desktopPreview.classList.remove('hidden');
    mobilePreview.classList.remove('hidden');
    if (desktopEmptyPreview) desktopEmptyPreview.classList.add('hidden');
    if (mobileEmptyPreview) mobileEmptyPreview.classList.add('hidden');

    // Create video preview using Video.js
    createVideoPreview({ id: videoId, name: videoTitle, url: videoPath });

    // console.log('✅ Video preview updated successfully'); // Disabled for production
  } else {
    // console.error('❌ Preview containers not found'); // Disabled for production
  }

  // Close dropdown
  toggleVideoSelector();
}

// Create video preview using Video.js (similar to edit modal)
function createVideoPreview(video) {
  // console.log('🎬 Creating video preview for:', video); // Disabled for production

  // Dispose existing players if any to prevent "already initialised" warning
  if (window.desktopVideoPlayer) {
    try {
      window.desktopVideoPlayer.pause();
      window.desktopVideoPlayer.dispose();
    } catch (e) {
      // Ignore disposal errors
    }
    window.desktopVideoPlayer = null;
  }
  if (window.mobileVideoPlayer) {
    try {
      window.mobileVideoPlayer.pause();
      window.mobileVideoPlayer.dispose();
    } catch (e) {
      // Ignore disposal errors
    }
    window.mobileVideoPlayer = null;
  }

  // Get containers
  const desktopVideoContainer = document.getElementById('videoPreview');
  const mobileVideoContainer = document.getElementById('videoPreviewMobile');

  if (!desktopVideoContainer || !mobileVideoContainer) {
    // console.error('❌ Video containers not found'); // Disabled for production
    return;
  }

  // Create simple HTML5 video elements (reliable fallback)
  desktopVideoContainer.innerHTML = `
    <video id="simple-preview-desktop" controls preload="metadata" style="width: 100%; height: 100%; object-fit: contain; background: #000;">
      <source src="${video.url}" type="video/mp4">
      Your browser does not support the video tag.
    </video>
  `;

  mobileVideoContainer.innerHTML = `
    <video id="simple-preview-mobile" controls preload="metadata" style="width: 100%; height: 100%; object-fit: contain; background: #000;">
      <source src="${video.url}" type="video/mp4">
      Your browser does not support the video tag.
    </video>
  `;

  // Show video containers
  desktopVideoContainer.classList.remove('hidden');
  mobileVideoContainer.classList.remove('hidden');

  // Hide empty preview states
  const desktopEmptyPreview = document.getElementById('emptyPreview');
  const mobileEmptyPreview = document.getElementById('emptyPreviewMobile');
  if (desktopEmptyPreview) desktopEmptyPreview.classList.add('hidden');
  if (mobileEmptyPreview) mobileEmptyPreview.classList.add('hidden');
}

// Load gallery videos function
async function loadGalleryVideos() {
  // console.log('📁 Loading gallery videos...'); // Disabled for production

  try {
    const container = document.getElementById('videoListContainer');
    if (!container) {
      // console.error("❌ Video list container not found"); // Disabled for production
      return;
    }

    // Show loading state
    container.innerHTML = '<div class="text-center py-3 text-gray-300"><i class="ti ti-loader animate-spin mr-2"></i>Loading videos...</div>';

    // Fetch videos from API
    // console.log('🌐 Fetching videos from /api/stream/videos...'); // Disabled for production
    const response = await fetch('/api/stream/videos');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const videos = await response.json();
    // console.log('📹 Received videos:', videos); // Disabled for production
    window.allStreamVideos = videos;

    // Check if there are not ready videos to show info
    if (videos.metadata && videos.metadata.notReadyCount > 0) {
      showNotReadyVideosInfo(videos.metadata);
    }

    // Display videos
    displayFilteredVideos(videos);

    // Setup search functionality
    const searchInput = document.getElementById('videoSearchInput');
    if (searchInput) {
      searchInput.removeEventListener('input', handleVideoSearch);
      searchInput.addEventListener('input', handleVideoSearch);
      setTimeout(() => searchInput.focus(), 100);
    }

    // console.log('✅ Gallery videos loaded successfully'); // Disabled for production
  } catch (error) {
    // console.error('❌ Error loading gallery videos:', error); // Disabled for production
    const container = document.getElementById('videoListContainer');
    if (container) {
      container.innerHTML = '<div class="text-center py-5 text-red-400"><i class="ti ti-alert-circle text-2xl mb-2"></i><p>Failed to load videos</p><p class="text-xs mt-1">' + error.message + '</p></div>';
    }
  }
}

// Handle video search
function handleVideoSearch(e) {
  const searchTerm = e.target.value.toLowerCase();
  const filteredVideos = window.allStreamVideos.filter(video =>
    video.name.toLowerCase().includes(searchTerm)
  );
  displayFilteredVideos(filteredVideos);
}

// Show information about videos that are not ready for streaming
function showNotReadyVideosInfo(metadata) {
  const container = document.getElementById('videoListContainer');
  if (!container) return;

  const infoHtml = `
    <div class="bg-yellow-900/30 border border-yellow-600 rounded-lg p-3 mb-4">
      <div class="flex items-start gap-3">
        <i class="ti ti-info-circle text-yellow-400 text-lg mt-0.5"></i>
        <div class="flex-1">
          <h4 class="text-yellow-400 font-medium text-sm mb-1">Video Processing Information</h4>
          <p class="text-gray-300 text-xs mb-2">
            ${metadata.notReadyCount} video${metadata.notReadyCount > 1 ? 's are' : ' is'} still being processed and cannot be used for streaming yet.
          </p>
          <div class="space-y-1">
            ${metadata.notReadyVideos.map(video => `
              <div class="flex items-center gap-2 text-xs">
                <span class="text-gray-400">${video.name}</span>
                <span class="px-2 py-0.5 rounded text-xs ${getStatusBadgeClass(video.processing_status)}">
                  ${getStatusText(video.processing_status)}
                </span>
              </div>
            `).join('')}
          </div>
          <p class="text-gray-400 text-xs mt-2">
            Only videos that have completed processing are available for streaming.
          </p>
        </div>
      </div>
    </div>
  `;

  container.insertAdjacentHTML('afterbegin', infoHtml);
}

// Get status badge CSS class
function getStatusBadgeClass(status) {
  switch (status) {
    case 'pending': return 'bg-yellow-900/50 text-yellow-400';
    case 'processing': return 'bg-blue-900/50 text-blue-400';
    case 'failed': return 'bg-red-900/50 text-red-400';
    default: return 'bg-gray-900/50 text-gray-400';
  }
}

// Get status text
function getStatusText(status) {
  switch (status) {
    case 'pending': return 'Pending';
    case 'processing': return 'Processing';
    case 'failed': return 'Failed';
    default: return 'Unknown';
  }
}

// Display filtered videos
function displayFilteredVideos(videos) {
  // console.log('🎬 Displaying filtered videos:', videos); // Disabled for production

  const container = document.getElementById('videoListContainer');
  if (!container) {
    // console.error('❌ Video list container not found'); // Disabled for production
    return;
  }

  if (!videos || !videos.length) {
    container.innerHTML = '<div class="text-center py-5 text-gray-400"><p>No videos found</p></div>';
    return;
  }

  const videoItems = videos.map(video => {
    // Escape quotes in video name for onclick
    const escapedName = video.name.replace(/'/g, "\\'").replace(/"/g, '\\"');

    // console.log('📹 Processing video:', { // Disabled for production
    //   id: video.id,
    //   name: video.name,
    //   url: video.url,
    //   resolution: video.resolution,
    //   duration: video.duration
    // });

    return `
      <div class="video-item p-3 hover:bg-dark-600 rounded-lg cursor-pointer transition-colors border-b border-gray-700/50 last:border-b-0"
           onclick="selectVideo('${video.id}', '${escapedName}', '${video.url}')">
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <div class="w-16 h-12 bg-dark-800 rounded-lg flex items-center justify-center">
              <i class="ti ti-video text-gray-400 text-lg"></i>
            </div>
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-white truncate" title="${video.name}">${video.name}</p>
            <div class="flex items-center space-x-2 text-xs text-gray-400 mt-1">
              <span>${video.resolution || '1280x720'}</span>
              <span>•</span>
              <span>${video.duration || '0:00'}</span>
            </div>
          </div>
        </div>
      </div>
    `;
  }).join('');

  container.innerHTML = videoItems;
  // console.log('✅ Video items displayed successfully'); // Disabled for production
}

// Modal initialization
function initModal() {
  const modal = document.getElementById('newStreamModal');
  if (!modal) return;

  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      closeNewStreamModal();
    }
  });

  if (videoSelectorDropdown) {
    document.addEventListener('click', (e) => {
      const isClickInsideDropdown = videoSelectorDropdown.contains(e.target);
      const isClickOnButton = e.target.closest('[onclick="toggleVideoSelector()"]');
      if (!isClickInsideDropdown && !isClickOnButton && isDropdownOpen) {
        toggleVideoSelector();
      }
    });
  }

  // Add edit modal click outside handler
  const editModal = document.getElementById('editStreamModal');
  if (editModal) {
    editModal.addEventListener('click', (e) => {
      if (e.target === editModal) {
        closeEditStreamModal();
      }
    });
  }

  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      if (isDropdownOpen) {
        toggleVideoSelector();
      } else if (!modal.classList.contains('hidden')) {
        closeNewStreamModal();
      } else if (editModal && !editModal.classList.contains('hidden')) {
        closeEditStreamModal();
      }
    }
  });

  // Initialize edit platform selector
  const editPlatformSelector = document.getElementById('editPlatformSelector');
  const editPlatformDropdown = document.getElementById('editPlatformDropdown');

  if (editPlatformSelector && editPlatformDropdown) {
    editPlatformSelector.addEventListener('click', function (e) {
      e.preventDefault();
      e.stopPropagation();
      editPlatformDropdown.classList.toggle('hidden');
    });

    const editPlatformOptions = editPlatformDropdown.querySelectorAll('.platform-option');
    editPlatformOptions.forEach(option => {
      option.addEventListener('click', function (e) {
        e.preventDefault();
        e.stopPropagation();
        const platformUrl = this.getAttribute('data-url');
        const editRtmpInput = document.getElementById('editRtmpUrl');
        if (editRtmpInput) {
          editRtmpInput.value = platformUrl;
        }
        editPlatformDropdown.classList.add('hidden');
        updateEditPlatformIcon(this.querySelector('i').className);
      });
    });

    // Close edit dropdown when clicking outside
    document.addEventListener('click', function (e) {
      if (editPlatformDropdown && !editPlatformDropdown.contains(e.target) &&
        !editPlatformSelector.contains(e.target)) {
        editPlatformDropdown.classList.add('hidden');
      }
    });
  }
}

function updateEditPlatformIcon(iconClass) {
  const currentIcon = document.querySelector('#editPlatformSelector i');
  if (!currentIcon) return;

  const iconParts = iconClass.split(' ');
  const brandIconPart = iconParts.filter(part => part.startsWith('ti-'))[0];

  if (!brandIconPart) return;

  currentIcon.className = `ti ${brandIconPart} text-center`;
  if (brandIconPart.includes('youtube')) {
    currentIcon.classList.add('text-red-500');
  } else if (brandIconPart.includes('twitch')) {
    currentIcon.classList.add('text-purple-500');
  } else if (brandIconPart.includes('facebook')) {
    currentIcon.classList.add('text-blue-500');
  } else if (brandIconPart.includes('instagram')) {
    currentIcon.classList.add('text-pink-500');
  } else if (brandIconPart.includes('tiktok')) {
    currentIcon.classList.add('text-white');
  } else if (brandIconPart.includes('shopee')) {
    currentIcon.classList.add('text-orange-500');
  } else if (brandIconPart.includes('live-photo')) {
    currentIcon.classList.add('text-teal-500');
  } else {
    currentIcon.classList.add('text-gray-400');
  }
}

// Safety function to restore body scroll
function restoreBodyScroll() {
  document.body.style.overflow = 'auto';
  document.body.style.removeProperty('overflow');

  // Also remove any backdrop filters that might be stuck
  const body = document.body;
  body.style.removeProperty('backdrop-filter');
  body.style.removeProperty('filter');

  // console.log('Body scroll restored and filters cleared'); // Removed for production
}

// Add safety mechanism to restore scroll on page errors
window.addEventListener('error', function(e) {
  console.warn('Page error detected, restoring body scroll as safety measure');
  restoreBodyScroll();
});

// Main initialization
document.addEventListener('DOMContentLoaded', function () {
  // Initialize video selector dropdown
  videoSelectorDropdown = document.getElementById('videoSelectorDropdown');

  // Initialize resolution select
  const resolutionSelect = document.getElementById('resolutionSelect');
  if (resolutionSelect) {
    resolutionSelect.addEventListener('change', updateResolutionDisplay);
    setVideoOrientation('horizontal');
  }

  // Initialize platform selector
  initializePlatformSelector();

  // Setup stream key validation
  setupStreamKeyValidation();

  // Ensure toast function exists
  ensureToastFunction();

  // Initialize modal
  initModal();

  // Ensure body scroll is restored on page load
  restoreBodyScroll();

  // console.log('Stream modal initialized successfully'); // Removed for production
});

// Clean up video players when modal is closed
function cleanupVideoPlayers() {
  // console.log('🧹 Cleaning up video players...'); // Disabled for production

  if (window.desktopVideoPlayer) {
    try {
      window.desktopVideoPlayer.pause();
      window.desktopVideoPlayer.dispose();
      window.desktopVideoPlayer = null;
      // console.log('✅ Desktop video player disposed'); // Disabled for production
    } catch (e) {
      // console.error('❌ Error disposing desktop player:', e); // Disabled for production
    }
  }

  if (window.mobileVideoPlayer) {
    try {
      window.mobileVideoPlayer.pause();
      window.mobileVideoPlayer.dispose();
      window.mobileVideoPlayer = null;
      // console.log('✅ Mobile video player disposed'); // Disabled for production
    } catch (e) {
      // console.error('❌ Error disposing mobile player:', e); // Disabled for production
    }
  }
}

// Duplicate function removed - using the one defined earlier

// Edit Stream Modal Functions
function openEditStreamModal(stream) {
  // console.log('openEditStreamModal called from stream-modal.js with stream:', stream); // Removed for production
  const modal = document.getElementById('editStreamModal');
  if (!modal) {
    console.error('Edit stream modal not found!');
    return;
  }

  try {
    // console.log('Edit modal element found, opening...'); // Removed for production
    // console.log('Edit modal classes before:', modal.className); // Removed for production
    // BRUTE FORCE APPROACH - Force modal to show with inline styles
    modal.style.cssText = `
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      z-index: 99999 !important;
      background: rgba(0, 0, 0, 0.7) !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      opacity: 1 !important;
      visibility: visible !important;
    `;

    // Also force the modal container to be visible with modern styling
    const modalContainer = modal.querySelector('.modal-container');
    if (modalContainer) {
      modalContainer.style.cssText = `
        background: linear-gradient(135deg, #252525 0%, #121212 100%) !important;
        border-radius: 20px !important;
        box-shadow: 0 30px 60px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(173, 102, 16, 0.2) !important;
        max-width: 95vw !important;
        max-height: 95vh !important;
        width: 100% !important;
        transform: scale(1) !important;
        opacity: 1 !important;
        visibility: visible !important;
        display: flex !important;
        flex-direction: column !important;
        overflow: hidden !important;
        backdrop-filter: blur(20px) !important;
        border: 1px solid rgba(173, 102, 16, 0.2) !important;
      `;

      // Apply the same styling as new modal
      applyModernModalStyling(modalContainer);

      // console.log('Edit modal container styled with modern design'); // Removed for production
    } else {
      console.error('Edit modal container not found!');
    }

    // Also remove hidden class and add show class
    modal.classList.remove('hidden');
    modal.classList.add('show');

    // Prevent body scroll
    document.body.style.overflow = 'hidden';

    // console.log('Edit modal classes after:', modal.className); // Removed for production
    // console.log('Edit modal inline styles:', modal.style.cssText); // Removed for production
    // console.log('Edit modal FORCED to be visible'); // Removed for production
    // Fix video preview after modal is shown
    setTimeout(() => {
      fixVideoPreview();
      // Also try to reinitialize video if needed
      initializeEditVideoPreview();
    }, 500);
  } catch (error) {
    console.error('Error in openEditStreamModal:', error);
    alert('Failed to open edit modal: ' + error.message);
  }
}

function closeEditStreamModal() {
  // console.log('closeEditStreamModal called from stream-modal.js'); // Removed for production
  const modal = document.getElementById('editStreamModal');
  if (!modal) {
    console.error('Edit stream modal not found!');
    return;
  }

  // console.log('Edit modal classes before close:', modal.className); // Removed for production
  // Remove show class and add hidden class
  modal.classList.remove('show');
  modal.classList.add('hidden');

  // Clear any inline styles that might interfere
  modal.removeAttribute('style');

  // Restore body scroll immediately
  document.body.style.overflow = 'auto';

  // console.log('Edit modal classes after close:', modal.className); // Removed for production
  // console.log('Edit stream modal closed'); // Removed for production
}

// Function to apply modern styling to modal container
function applyModernModalStyling(modalContainer) {
  // Improve header styling
  const header = modalContainer.querySelector('.flex.items-center.justify-between');
  if (header) {
    header.style.cssText = `
      background: rgba(18, 18, 18, 0.8) !important;
      border-bottom: 1px solid rgba(173, 102, 16, 0.2) !important;
      padding: 28px 28px 20px !important;
      backdrop-filter: blur(15px) !important;
    `;

    const title = header.querySelector('h3');
    if (title) {
      title.style.cssText = `
        color: #ffffff !important;
        font-size: 20px !important;
        font-weight: 600 !important;
        margin: 0 !important;
      `;
    }
  }

  // Improve form content styling
  const formContent = modalContainer.querySelector('.p-4.sm\\:px-6');
  if (formContent) {
    formContent.style.cssText = `
      padding: 28px !important;
      background: rgba(37, 37, 37, 0.3) !important;
    `;
  }

  // Style input fields
  const inputs = modalContainer.querySelectorAll('input[type="text"], input[type="password"], select, textarea');
  inputs.forEach(input => {
    // Check if input has icons (by checking if it's in a relative container with icons)
    const hasLeftIcon = input.parentElement.querySelector('i.ti-link, i.ti-key');
    const hasRightIcon = input.parentElement.querySelector('button');

    // Different styling for select elements vs input elements
    if (input.tagName.toLowerCase() === 'select') {
      // Select elements need special handling for dropdown arrow
      input.style.cssText = `
        background: rgba(18, 18, 18, 0.8) !important;
        border: 2px solid rgba(61, 61, 61, 1) !important;
        border-radius: 10px !important;
        color: #ffffff !important;
        padding: 14px 40px 14px 18px !important;
        font-size: 14px !important;
        line-height: 1.4 !important;
        height: auto !important;
        min-height: 48px !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        backdrop-filter: blur(5px) !important;
        box-sizing: border-box !important;
        vertical-align: middle !important;
        appearance: none !important;
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
        background-repeat: no-repeat !important;
        background-position: right 12px center !important;
        background-size: 16px !important;
        cursor: pointer !important;
      `;
    } else {
      // Regular input elements with icon detection
      let paddingLeft = hasLeftIcon ? '44px' : '18px';  // Space for left icon
      let paddingRight = hasRightIcon ? '48px' : '18px'; // Space for right icon/button

      input.style.cssText = `
        background: rgba(18, 18, 18, 0.8) !important;
        border: 2px solid rgba(61, 61, 61, 1) !important;
        border-radius: 10px !important;
        color: #ffffff !important;
        padding: 14px ${paddingRight} 14px ${paddingLeft} !important;
        font-size: 14px !important;
        line-height: 1.4 !important;
        height: auto !important;
        min-height: 48px !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        backdrop-filter: blur(5px) !important;
        box-sizing: border-box !important;
        vertical-align: middle !important;
      `;
    }

    // Add focus styles
    input.addEventListener('focus', function() {
      this.style.borderColor = '#ad6610';
      this.style.boxShadow = '0 0 0 4px rgba(173, 102, 16, 0.1)';
      this.style.background = 'rgba(18, 18, 18, 0.9)';
      this.style.transform = 'translateY(-1px)';
    });

    input.addEventListener('blur', function() {
      this.style.borderColor = 'rgba(61, 61, 61, 1)';
      this.style.boxShadow = 'none';
      this.style.background = 'rgba(18, 18, 18, 0.8)';
      this.style.transform = 'translateY(0)';
    });
  });

  // Style labels
  const labels = modalContainer.querySelectorAll('label');
  labels.forEach(label => {
    label.style.cssText = `
      color: #e5e5e5 !important;
      font-weight: 600 !important;
      font-size: 14px !important;
      margin-bottom: 8px !important;
      letter-spacing: 0.025em !important;
    `;
  });

  // Style buttons
  const buttons = modalContainer.querySelectorAll('button');
  buttons.forEach(button => {
    if (button.textContent.includes('Save') || button.textContent.includes('Create')) {
      button.style.cssText = `
        background: linear-gradient(135deg, #ad6610 0%, #d17f14 100%) !important;
        border: none !important;
        border-radius: 10px !important;
        color: #ffffff !important;
        padding: 14px 28px !important;
        font-weight: 600 !important;
        font-size: 14px !important;
        text-transform: uppercase !important;
        letter-spacing: 0.025em !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 4px 15px rgba(173, 102, 16, 0.3) !important;
        position: relative !important;
        overflow: hidden !important;
      `;

      // Add hover effects for primary button
      button.addEventListener('mouseenter', function() {
        this.style.background = 'linear-gradient(135deg, #d17f14 0%, #ad6610 100%)';
        this.style.transform = 'translateY(-2px) scale(1.05)';
        this.style.boxShadow = '0 8px 25px rgba(173, 102, 16, 0.4)';
      });

      button.addEventListener('mouseleave', function() {
        this.style.background = 'linear-gradient(135deg, #ad6610 0%, #d17f14 100%)';
        this.style.transform = 'translateY(0) scale(1)';
        this.style.boxShadow = '0 4px 15px rgba(173, 102, 16, 0.3)';
      });
    } else if (button.textContent.includes('Cancel')) {
      button.style.cssText = `
        background: rgba(45, 45, 45, 0.8) !important;
        border: 2px solid rgba(173, 102, 16, 0.3) !important;
        border-radius: 10px !important;
        color: #e5e5e5 !important;
        padding: 14px 28px !important;
        font-weight: 600 !important;
        font-size: 14px !important;
        text-transform: uppercase !important;
        letter-spacing: 0.025em !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        backdrop-filter: blur(10px) !important;
      `;

      // Add hover effects for Cancel button
      button.addEventListener('mouseenter', function() {
        this.style.background = 'rgba(173, 102, 16, 0.1)';
        this.style.borderColor = 'rgba(173, 102, 16, 0.6)';
        this.style.color = '#ffffff';
        this.style.transform = 'translateY(-2px)';
        this.style.boxShadow = '0 8px 20px rgba(173, 102, 16, 0.2)';
      });

      button.addEventListener('mouseleave', function() {
        this.style.background = 'rgba(45, 45, 45, 0.8)';
        this.style.borderColor = 'rgba(173, 102, 16, 0.3)';
        this.style.color = '#e5e5e5';
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = 'none';
      });
    }
  });
}

// Function to fix video preview issues
function fixVideoPreview() {
  // console.log('Fixing video preview...'); // Removed for production
  // Fix edit modal video preview if it exists
  const editVideoPreview = document.getElementById('editVideoPreview');
  const editVideoPreviewMobile = document.getElementById('editVideoPreviewMobile');

  if (editVideoPreview && !editVideoPreview.classList.contains('hidden')) {
    const videoElement = editVideoPreview.querySelector('video');
    if (videoElement) {
      // console.log('Found edit video element, fixing...'); // Removed for production
      // Force reload the video
      videoElement.load();

      // Add error handling
      videoElement.addEventListener('error', function(e) {
        console.error('Video error:', e);
        // console.log('Trying to fix video source...'); // Removed for production
        // Try to reload with different approach
        setTimeout(() => {
          videoElement.load();
        }, 1000);
      });

      // Add loaded event
      videoElement.addEventListener('loadeddata', function() {
        // console.log('Video loaded successfully'); // Removed for production
      });

      // Force video to be visible with enhanced styling
      videoElement.style.cssText = `
        width: 100% !important;
        height: 100% !important;
        object-fit: contain !important;
        background: #000 !important;
        display: block !important;
        border-radius: 8px !important;
      `;

      // Also ensure the container is properly styled
      editVideoPreview.style.cssText = `
        width: 100% !important;
        height: 100% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        background: #000 !important;
        border-radius: 8px !important;
        overflow: hidden !important;
      `;
    } else {
      // console.log('No video element found in edit preview, trying to recreate...'); // Removed for production
      // Try to trigger video recreation if no video element exists
      if (window.editSelectedVideoData) {
        setTimeout(() => {
          window.createEditVideoPreview(window.editSelectedVideoData);
        }, 100);
      }
    }
  }

  // Fix mobile video preview
  if (editVideoPreviewMobile && !editVideoPreviewMobile.classList.contains('hidden')) {
    const videoElementMobile = editVideoPreviewMobile.querySelector('video');
    if (videoElementMobile) {
      // console.log('Found edit mobile video element, fixing...'); // Removed for production
      videoElementMobile.load();
      videoElementMobile.style.cssText = `
        width: 100% !important;
        height: 100% !important;
        object-fit: contain !important;
        background: #000 !important;
        display: block !important;
        border-radius: 8px !important;
      `;

      // Also ensure the mobile container is properly styled
      editVideoPreviewMobile.style.cssText = `
        width: 100% !important;
        height: 100% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        background: #000 !important;
        border-radius: 8px !important;
        overflow: hidden !important;
      `;
    } else {
      // console.log('No mobile video element found, trying to recreate...'); // Removed for production
      if (window.editSelectedVideoData) {
        setTimeout(() => {
          window.createEditVideoPreview(window.editSelectedVideoData);
        }, 100);
      }
    }
  }
}

// Function to initialize edit video preview
function initializeEditVideoPreview() {
  // console.log('Initializing edit video preview...'); // Removed for production
  // Check if we have selected video data
  if (window.editSelectedVideoData) {
    // console.log('Found selected video data, recreating preview...'); // Removed for production
    // Ensure preview containers are visible
    const editVideoPreview = document.getElementById('editVideoPreview');
    const editVideoPreviewMobile = document.getElementById('editVideoPreviewMobile');
    const editEmptyPreview = document.getElementById('editEmptyPreview');
    const editEmptyPreviewMobile = document.getElementById('editEmptyPreviewMobile');

    if (editVideoPreview && editVideoPreviewMobile) {
      editVideoPreview.classList.remove('hidden');
      editVideoPreviewMobile.classList.remove('hidden');
      if (editEmptyPreview) editEmptyPreview.classList.add('hidden');
      if (editEmptyPreviewMobile) editEmptyPreviewMobile.classList.add('hidden');

      // Recreate the video preview
      if (window.createEditVideoPreview) {
        window.createEditVideoPreview(window.editSelectedVideoData);
      }
    }
  } else {
    // console.log('No selected video data found'); // Removed for production
  }
}

// Make functions globally available
window.openNewStreamModal = openNewStreamModal;
window.closeNewStreamModal = closeNewStreamModal;
window.openEditStreamModal = openEditStreamModal;
window.closeEditStreamModal = closeEditStreamModal;
window.restoreBodyScroll = restoreBodyScroll;
window.fixVideoPreview = fixVideoPreview;

// Debug functions to test modals
window.testModals = function() {
  // console.log('=== MODAL TEST ==='); // Removed for production
  console.log('New stream modal element:', !!document.getElementById('newStreamModal'));
  console.log('Edit stream modal element:', !!document.getElementById('editStreamModal'));
  // console.log('openNewStreamModal function:', typeof window.openNewStreamModal); // Removed for production
  // console.log('closeNewStreamModal function:', typeof window.closeNewStreamModal); // Removed for production
  // console.log('openEditStreamModal function:', typeof window.openEditStreamModal); // Removed for production
  // console.log('closeEditStreamModal function:', typeof window.closeEditStreamModal); // Removed for production
  // Test new stream modal
  // console.log('Testing new stream modal...'); // Removed for production
  try {
    window.openNewStreamModal();
    // console.log('✅ New stream modal opened successfully'); // Removed for production
    setTimeout(() => {
      window.closeNewStreamModal();
      // console.log('✅ New stream modal closed successfully'); // Removed for production
    }, 3000);
  } catch (error) {
    console.error('❌ Error testing new stream modal:', error);
  }
};

// Simple test functions for individual modals
window.testNewModal = function() {
  // console.log('=== TESTING NEW STREAM MODAL ==='); // Removed for production
  const modal = document.getElementById('newStreamModal');
  // console.log('Modal element exists:', !!modal); // Removed for production
  if (modal) {
    // console.log('Modal current classes:', modal.className); // Removed for production
    // console.log('Modal current styles:', modal.style.cssText); // Removed for production
    console.log('Modal computed display:', window.getComputedStyle(modal).display);
    console.log('Modal computed visibility:', window.getComputedStyle(modal).visibility);
    console.log('Modal computed opacity:', window.getComputedStyle(modal).opacity);
  }
  window.openNewStreamModal();

  // Check again after opening
  setTimeout(() => {
    if (modal) {
      // console.log('=== AFTER OPENING ==='); // Removed for production
      // console.log('Modal classes after open:', modal.className); // Removed for production
      // console.log('Modal styles after open:', modal.style.cssText); // Removed for production
      console.log('Modal computed display after:', window.getComputedStyle(modal).display);
      console.log('Modal computed visibility after:', window.getComputedStyle(modal).visibility);
      console.log('Modal computed opacity after:', window.getComputedStyle(modal).opacity);

      // Check modal container
      const container = modal.querySelector('.modal-container');
      if (container) {
        // console.log('=== MODAL CONTAINER ==='); // Removed for production
        // console.log('Container classes:', container.className); // Removed for production
        // console.log('Container styles:', container.style.cssText); // Removed for production
        console.log('Container computed display:', window.getComputedStyle(container).display);
        console.log('Container computed visibility:', window.getComputedStyle(container).visibility);
        console.log('Container computed opacity:', window.getComputedStyle(container).opacity);
      } else {
        console.error('Modal container not found!');
      }
    }
  }, 500);
};

window.testEditModal = function() {
  // console.log('=== TESTING EDIT STREAM MODAL ==='); // Removed for production
  const modal = document.getElementById('editStreamModal');
  // console.log('Modal element exists:', !!modal); // Removed for production
  if (modal) {
    // console.log('Modal current classes:', modal.className); // Removed for production
  }
  window.openEditStreamModal({ id: 'test', title: 'Test Stream', stream_key: 'test123', rtmp_url: 'rtmp://test' });
};

window.closeAllModals = function() {
  // console.log('Closing all modals...'); // Removed for production
  window.closeNewStreamModal();
  window.closeEditStreamModal();
  window.restoreBodyScroll();
};

// Emergency modal functions - force show with brute force CSS
window.forceShowNewModal = function() {
  // console.log('FORCE SHOWING NEW STREAM MODAL'); // Removed for production
  const modal = document.getElementById('newStreamModal');
  if (modal) {
    // Remove all classes
    modal.className = '';
    // Force show with inline styles
    modal.style.cssText = `
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      z-index: 99999 !important;
      background: rgba(0, 0, 0, 0.8) !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      opacity: 1 !important;
      visibility: visible !important;
    `;
    document.body.style.overflow = 'hidden';
    // console.log('NEW STREAM MODAL FORCED TO SHOW'); // Removed for production
  } else {
    console.error('NEW STREAM MODAL NOT FOUND');
  }
};

window.forceShowEditModal = function() {
  // console.log('FORCE SHOWING EDIT STREAM MODAL'); // Removed for production
  const modal = document.getElementById('editStreamModal');
  if (modal) {
    // Remove all classes
    modal.className = '';
    // Force show with inline styles
    modal.style.cssText = `
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      z-index: 99999 !important;
      background: rgba(0, 0, 0, 0.8) !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      opacity: 1 !important;
      visibility: visible !important;
    `;
    document.body.style.overflow = 'hidden';
    // console.log('EDIT STREAM MODAL FORCED TO SHOW'); // Removed for production
  } else {
    console.error('EDIT STREAM MODAL NOT FOUND');
  }
};

window.forceHideAllModals = function() {
  // console.log('FORCE HIDING ALL MODALS'); // Removed for production
  const newModal = document.getElementById('newStreamModal');
  const editModal = document.getElementById('editStreamModal');

  if (newModal) {
    newModal.removeAttribute('style');
    newModal.classList.remove('show');
    newModal.classList.add('hidden');
  }
  if (editModal) {
    editModal.removeAttribute('style');
    editModal.classList.remove('show');
    editModal.classList.add('hidden');
  }

  document.body.style.overflow = 'auto';
  // console.log('ALL MODALS FORCED TO HIDE'); // Removed for production
};
