<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Notification System Demo - StreamOnPod</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@2.30.0/tabler-icons.min.css">
  <link rel="stylesheet" href="/css/notifications.css">
  <style>
    body {
      font-family: 'Inter', sans-serif;
      background: #121212;
      color: #ffffff;
      margin: 0;
      padding: 20px;
      line-height: 1.6;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    
    .demo-section {
      background: #1f2937;
      border-radius: 12px;
      padding: 24px;
      margin-bottom: 24px;
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .demo-title {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #ad6610;
    }
    
    .demo-subtitle {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 16px;
      color: #ffffff;
    }
    
    .demo-description {
      color: #d1d5db;
      margin-bottom: 20px;
    }
    
    .demo-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
    }
    
    .demo-btn {
      padding: 10px 16px;
      border: none;
      border-radius: 8px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      font-size: 14px;
    }
    
    .demo-btn-success {
      background: #10b981;
      color: white;
    }
    
    .demo-btn-error {
      background: #ef4444;
      color: white;
    }
    
    .demo-btn-warning {
      background: #f59e0b;
      color: white;
    }
    
    .demo-btn-info {
      background: #3b82f6;
      color: white;
    }
    
    .demo-btn-primary {
      background: #ad6610;
      color: white;
    }
    
    .demo-btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
    
    .code-block {
      background: #0f172a;
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 16px;
      margin-top: 16px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 13px;
      color: #e2e8f0;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="demo-section">
      <h1 class="demo-title">🎉 Modern Notification System</h1>
      <p class="demo-description">
        Sistem notifikasi modern untuk StreamOnPod yang menggantikan alert() dan confirm() biasa dengan 
        tampilan yang lebih menarik, animasi yang halus, dan pengalaman pengguna yang lebih baik.
      </p>
    </div>

    <div class="demo-section">
      <h2 class="demo-subtitle">Toast Notifications</h2>
      <p class="demo-description">Notifikasi toast yang muncul di pojok kanan atas dengan animasi yang halus.</p>
      <div class="demo-buttons">
        <button class="demo-btn demo-btn-success" onclick="showSuccessToast()">Success Toast</button>
        <button class="demo-btn demo-btn-error" onclick="showErrorToast()">Error Toast</button>
        <button class="demo-btn demo-btn-warning" onclick="showWarningToast()">Warning Toast</button>
        <button class="demo-btn demo-btn-info" onclick="showInfoToast()">Info Toast</button>
        <button class="demo-btn demo-btn-primary" onclick="showLoadingToast()">Loading Toast</button>
      </div>
      <div class="code-block">
notifications.success('Success!', 'Your action was completed successfully');
notifications.error('Error!', 'Something went wrong');
notifications.warning('Warning!', 'Please check your input');
notifications.info('Info', 'Here is some information');
      </div>
    </div>

    <div class="demo-section">
      <h2 class="demo-subtitle">Modal Dialogs</h2>
      <p class="demo-description">Modal dialog yang menggantikan alert() dan confirm() dengan tampilan yang lebih modern.</p>
      <div class="demo-buttons">
        <button class="demo-btn demo-btn-info" onclick="showAlert()">Alert Dialog</button>
        <button class="demo-btn demo-btn-warning" onclick="showConfirm()">Confirm Dialog</button>
        <button class="demo-btn demo-btn-primary" onclick="showPrompt()">Prompt Dialog</button>
        <button class="demo-btn demo-btn-error" onclick="showDangerConfirm()">Danger Confirm</button>
      </div>
      <div class="code-block">
// Alert replacement
await notifications.alert('This is an alert message');

// Confirm replacement
const confirmed = await notifications.confirm('Are you sure?');
if (confirmed) {
  console.log('User confirmed');
}

// Prompt replacement
const input = await notifications.prompt('Enter your name:');
if (input) {
  console.log('User entered:', input);
}
      </div>
    </div>

    <div class="demo-section">
      <h2 class="demo-subtitle">Advanced Features</h2>
      <p class="demo-description">Fitur-fitur lanjutan seperti loading state, custom buttons, dan sound effects.</p>
      <div class="demo-buttons">
        <button class="demo-btn demo-btn-primary" onclick="showCustomModal()">Custom Modal</button>
        <button class="demo-btn demo-btn-info" onclick="showPersistentToast()">Persistent Toast</button>
        <button class="demo-btn demo-btn-warning" onclick="showMultipleToasts()">Multiple Toasts</button>
        <button class="demo-btn demo-btn-success" onclick="testSoundEffects()">Sound Effects</button>
      </div>
    </div>

    <div class="demo-section">
      <h2 class="demo-subtitle">Backward Compatibility</h2>
      <p class="demo-description">Sistem ini secara otomatis menggantikan alert(), confirm(), dan prompt() native browser.</p>
      <div class="demo-buttons">
        <button class="demo-btn demo-btn-info" onclick="testNativeAlert()">Native alert()</button>
        <button class="demo-btn demo-btn-warning" onclick="testNativeConfirm()">Native confirm()</button>
        <button class="demo-btn demo-btn-primary" onclick="testNativePrompt()">Native prompt()</button>
      </div>
      <div class="code-block">
// These will automatically use the new notification system
alert('This is now a beautiful modal!');
const result = confirm('Do you want to continue?');
const name = prompt('What is your name?');
      </div>
    </div>
  </div>

  <script src="/js/notifications.js"></script>
  <script>
    // Toast examples
    function showSuccessToast() {
      notifications.success('Operation Successful!', 'Your stream has been created successfully');
    }

    function showErrorToast() {
      notifications.error('Upload Failed', 'Failed to upload video. Please check your connection.');
    }

    function showWarningToast() {
      notifications.warning('Storage Almost Full', 'You have used 90% of your storage quota');
    }

    function showInfoToast() {
      notifications.info('New Feature Available', 'Check out the new stream scheduling feature');
    }

    function showLoadingToast() {
      const loading = notifications.loading('Processing...', 'Please wait while we process your request');
      setTimeout(() => {
        loading.close();
        notifications.success('Complete!', 'Processing finished successfully');
      }, 3000);
    }

    // Modal examples
    async function showAlert() {
      await notifications.alert('This is a modern alert dialog that replaces the old browser alert!');
    }

    async function showConfirm() {
      const confirmed = await notifications.confirm(
        'Are you sure you want to delete this stream?',
        'Confirm Deletion',
        { confirmText: 'Delete', type: 'warning' }
      );
      
      if (confirmed) {
        notifications.success('Deleted', 'Stream has been deleted successfully');
      } else {
        notifications.info('Cancelled', 'Deletion was cancelled');
      }
    }

    async function showPrompt() {
      const name = await notifications.prompt(
        'Please enter your stream title:',
        'Stream Title',
        { placeholder: 'Enter title here...', required: true }
      );
      
      if (name) {
        notifications.success('Title Set', `Stream title set to: "${name}"`);
      }
    }

    async function showDangerConfirm() {
      const confirmed = await notifications.confirm(
        'This action will permanently delete all your data. This cannot be undone!',
        'Danger Zone',
        { confirmText: 'Delete Everything', type: 'error' }
      );
      
      if (confirmed) {
        notifications.error('Cancelled', 'Just kidding! This is just a demo 😄');
      }
    }

    // Advanced examples
    async function showCustomModal() {
      const result = await notifications.showModal('question', 'Choose Action', 'What would you like to do?', [
        { text: 'Cancel', action: 'cancel' },
        { text: 'Save Draft', action: 'draft' },
        { text: 'Publish', action: 'publish', primary: true }
      ]);
      
      notifications.info('Action Selected', `You chose: ${result}`);
    }

    function showPersistentToast() {
      notifications.showToast('warning', 'Persistent Warning', 'This toast will not auto-dismiss', { duration: 0 });
    }

    function showMultipleToasts() {
      notifications.info('First', 'This is the first notification');
      setTimeout(() => notifications.success('Second', 'This is the second notification'), 500);
      setTimeout(() => notifications.warning('Third', 'This is the third notification'), 1000);
      setTimeout(() => notifications.error('Fourth', 'This is the fourth notification'), 1500);
    }

    function testSoundEffects() {
      notifications.success('Sound Test', 'Listen for the subtle notification sound', { sound: true });
    }

    // Native compatibility tests
    async function testNativeAlert() {
      alert('This looks like a native alert, but it\'s actually our beautiful modal!');
    }

    async function testNativeConfirm() {
      const result = confirm('This is a native confirm() call, but with modern styling!');
      notifications.info('Result', `You clicked: ${result ? 'OK' : 'Cancel'}`);
    }

    async function testNativePrompt() {
      const result = prompt('This is a native prompt() call with modern UI:', 'Default value');
      if (result !== null) {
        notifications.success('Input Received', `You entered: "${result}"`);
      }
    }
  </script>
</body>
</html>
