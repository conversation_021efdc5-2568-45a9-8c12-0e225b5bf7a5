// StreamOnPod Service Worker - CACHING DISABLED
const CACHE_NAME = 'streamonpod-disabled';
const STATIC_CACHE = 'streamonpod-disabled';

// Cache disabled - no files to cache
const STATIC_FILES = [];

// Install event - skip caching
self.addEventListener('install', (event) => {
  // Service Worker installed - caching disabled (logging removed for production)
  self.skipWaiting();
});

// Activate event - clear all caches (caching disabled)
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => caches.delete(cacheName))
        );
      })
      .then(() => {
        // All caches cleared - caching disabled (logging removed for production)
        return self.clients.claim();
      })
  );
});

// Fetch event - always fetch from network (no caching)
self.addEventListener('fetch', (event) => {
  // Cache disabled - always fetch from network
  event.respondWith(
    fetch(event.request)
      .catch(() => {
        // Return basic error response if network fails
        return new Response('Network error - caching disabled', {
          status: 503,
          statusText: 'Service Unavailable'
        });
      })
  );
});

// Background sync disabled
self.addEventListener('sync', (event) => {
  // Background sync disabled (logging removed for production)
});

// No retry function needed (caching disabled)
async function retryFailedRequests() {
  // Retry disabled - caching disabled (logging removed for production)
}