<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RTMP Validation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .container {
            background-color: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        input, button {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #555;
            border-radius: 5px;
            background-color: #333;
            color: #fff;
        }
        button {
            background-color: #ad6610;
            cursor: pointer;
        }
        button:hover {
            background-color: #d17f14;
        }
        .result {
            background-color: #333;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            white-space: pre-wrap;
        }
        .error {
            color: #ff6b6b;
        }
        .success {
            color: #51cf66;
        }
        .warning {
            color: #ffd43b;
        }
    </style>
</head>
<body>
    <h1>🧪 RTMP Validation Test</h1>
    
    <div class="container">
        <h2>Test RTMP URL Validation</h2>
        <p>This tool helps debug RTMP validation issues between local and tunnel access.</p>
        
        <input type="text" id="rtmpUrl" placeholder="Enter RTMP URL (e.g., rtmp://a.rtmp.youtube.com/live2)" 
               value="rtmp://a.rtmp.youtube.com/live2">
        
        <input type="text" id="streamKey" placeholder="Enter Stream Key" 
               value="test-stream-key-123">
        
        <button onclick="testValidation()">🔍 Test Validation</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>📋 Test Cases</h2>
        <button onclick="testYouTube()">Test YouTube RTMP</button>
        <button onclick="testFacebook()">Test Facebook RTMP</button>
        <button onclick="testInvalid()">Test Invalid URL</button>
        <button onclick="testEmpty()">Test Empty Fields</button>
    </div>

    <div class="container">
        <h2>ℹ️ Environment Info</h2>
        <div id="envInfo">
            <p><strong>Current URL:</strong> <span id="currentUrl"></span></p>
            <p><strong>User Agent:</strong> <span id="userAgent"></span></p>
            <p><strong>Protocol:</strong> <span id="protocol"></span></p>
        </div>
    </div>

    <script>
        // Display environment info
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('userAgent').textContent = navigator.userAgent;
        document.getElementById('protocol').textContent = window.location.protocol;

        async function testValidation() {
            const rtmpUrl = document.getElementById('rtmpUrl').value;
            const streamKey = document.getElementById('streamKey').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '⏳ Testing validation...';
            
            try {
                const response = await fetch('/api/validate-rtmp', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        rtmpUrl: rtmpUrl,
                        streamKey: streamKey
                    })
                });
                
                const data = await response.json();
                
                let resultText = `Status: ${response.status}\n`;
                resultText += `Response: ${JSON.stringify(data, null, 2)}\n\n`;
                
                if (data.success && data.validation) {
                    const validation = data.validation;
                    resultText += `✅ Validation Result:\n`;
                    resultText += `   Valid: ${validation.isValid}\n`;
                    resultText += `   Platform: ${validation.platform}\n`;
                    
                    if (validation.errors && validation.errors.length > 0) {
                        resultText += `   Errors:\n`;
                        validation.errors.forEach(error => {
                            resultText += `     - ${error}\n`;
                        });
                    }
                    
                    if (validation.warnings && validation.warnings.length > 0) {
                        resultText += `   Warnings:\n`;
                        validation.warnings.forEach(warning => {
                            resultText += `     - ${warning}\n`;
                        });
                    }
                    
                    resultDiv.className = validation.isValid ? 'result success' : 'result error';
                } else {
                    resultDiv.className = 'result error';
                }
                
                resultDiv.textContent = resultText;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Error: ${error.message}`;
            }
        }

        function testYouTube() {
            document.getElementById('rtmpUrl').value = 'rtmp://a.rtmp.youtube.com/live2';
            document.getElementById('streamKey').value = 'abcd-1234-efgh-5678-ijkl-9012';
            testValidation();
        }

        function testFacebook() {
            document.getElementById('rtmpUrl').value = 'rtmps://live-api-s.facebook.com:443/rtmp';
            document.getElementById('streamKey').value = 'FB-1234567890-ABCDEFGH';
            testValidation();
        }

        function testInvalid() {
            document.getElementById('rtmpUrl').value = 'http://invalid-url.com/live';
            document.getElementById('streamKey').value = 'test-key';
            testValidation();
        }

        function testEmpty() {
            document.getElementById('rtmpUrl').value = '';
            document.getElementById('streamKey').value = '';
            testValidation();
        }
    </script>
</body>
</html>
