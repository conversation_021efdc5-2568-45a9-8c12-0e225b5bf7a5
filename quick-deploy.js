#!/usr/bin/env node

/**
 * Quick Deployment for StreamOnPod
 * Simple and fast deployment without complex checks
 */

const fs = require('fs');
const { execSync } = require('child_process');

console.log('🚀 StreamOnPod Quick Deployment\n');

try {
  // Load environment
  require('dotenv').config();
  
  console.log('✅ Environment loaded');
  
  // Check essential files
  const essentialFiles = ['app.js', 'package.json'];
  essentialFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file} found`);
    } else {
      console.log(`❌ ${file} missing`);
    }
  });
  
  // Install dependencies
  console.log('\n📦 Installing dependencies...');
  execSync('npm install', { stdio: 'inherit' });
  console.log('✅ Dependencies installed');
  
  // Success
  console.log('\n🎉 DEPLOYMENT SUCCESSFUL!');
  console.log('\n📋 Next Steps:');
  console.log('1. Start application: npm run production');
  console.log('2. Or with PM2: pm2 start scripts/production-start.js --name streamonpod');
  console.log('3. Monitor: npm run monitor:performance');
  console.log('4. Access: https://streamonpod.imthe.one');
  
} catch (error) {
  console.error('❌ Deployment failed:', error.message);
  process.exit(1);
}
