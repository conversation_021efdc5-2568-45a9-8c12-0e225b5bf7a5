const express = require('express');
const router = express.Router();
const Notification = require('../models/Notification');
const Permission = require('../models/Permission');
const notificationService = require('../services/notificationService');
const { db } = require('../db/database');



// Middleware to check if user is admin
const requireAdmin = Permission.requireRole('admin');

// Middleware to check if user is authenticated
const requireAuth = (req, res, next) => {
  if (!req.session.userId) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
  }
  next();
};

// Get notifications for regular users
router.get('/user', requireAuth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      type,
      category,
      priority,
      is_read
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    const options = {
      target_user_id: req.session.userId, // User-specific notifications
      limit: parseInt(limit),
      offset,
      ...(type && { type }),
      ...(category && { category }),
      ...(priority && { priority }),
      ...(is_read !== undefined && { is_read: is_read === 'true' })
    };

    const notifications = await Notification.findAll(options);
    const unreadCount = await Notification.getUnreadCount(req.session.userId);

    res.json({
      success: true,
      notifications,
      unreadCount,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        hasMore: notifications.length === parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching user notifications:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch notifications'
    });
  }
});

// Get unread count for regular users
router.get('/user/unread-count', requireAuth, async (req, res) => {
  try {
    const count = await Notification.getUnreadCount(req.session.userId);
    res.json({ success: true, count });
  } catch (error) {
    console.error('Error fetching user unread count:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch unread count'
    });
  }
});

// Mark user notification as read
router.post('/user/:id/mark-read', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;

    // Verify the notification belongs to the user
    const notification = await Notification.findById(id);
    if (!notification || notification.target_user_id !== req.session.userId) {
      return res.status(404).json({
        success: false,
        error: 'Notification not found'
      });
    }

    const success = await Notification.markAsRead(id);

    if (success) {
      // Emit real-time update to the user
      notificationService.emitToUser(req.session.userId, 'notification:updated', {
        id,
        isRead: true
      });

      res.json({ success: true });
    } else {
      res.status(404).json({
        success: false,
        error: 'Notification not found'
      });
    }
  } catch (error) {
    console.error('Error marking user notification as read:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to mark notification as read'
    });
  }
});

// Mark all user notifications as read
router.post('/user/mark-all-read', requireAuth, async (req, res) => {
  try {
    const count = await Notification.markAllAsRead(req.session.userId);

    // Emit real-time update to the user
    notificationService.emitToUser(req.session.userId, 'notification:allMarkedRead');

    res.json({
      success: true,
      markedCount: count
    });
  } catch (error) {
    console.error('Error marking all user notifications as read:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to mark all notifications as read'
    });
  }
});



// Admin notification management routes removed - replaced with stream monitoring

// Create test user notification (for development/testing)
router.post('/user/test', requireAuth, async (req, res) => {
  try {
    const {
      title = 'Test User Notification',
      message = 'This is a test notification for you',
      type = 'info',
      priority = 'normal'
    } = req.body;

    const notification = await notificationService.notifyUserSpecific(
      req.session.userId,
      title,
      message,
      type,
      priority
    );

    res.json({
      success: true,
      notification
    });
  } catch (error) {
    console.error('Error creating test user notification:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create test notification'
    });
  }
});

// Admin notification stats and cleanup routes removed - replaced with stream monitoring



module.exports = router;
