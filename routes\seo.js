const express = require('express');
const router = express.Router();
const User = require('../models/User');
const Video = require('../models/Video');
const Subscription = require('../models/Subscription');

// robots.txt
router.get('/robots.txt', (req, res) => {
  const baseUrl = process.env.BASE_URL || 'https://streamonpod.com';
  
  const robotsTxt = `User-agent: *
Allow: /
Allow: /login
Allow: /register
Allow: /subscription
Allow: /privacy-policy
Allow: /tos

# Disallow private areas
Disallow: /admin/
Disallow: /dashboard/
Disallow: /api/
Disallow: /uploads/videos/
Disallow: /uploads/avatars/
Disallow: /test-*
Disallow: /health

# Allow public images
Allow: /images/
Allow: /uploads/thumbnails/

# Crawl delay
Crawl-delay: 1

# Sitemap location
Sitemap: ${baseUrl}/sitemap.xml`;

  res.set('Content-Type', 'text/plain');
  res.send(robotsTxt);
});

// sitemap.xml - Dynamic sitemap generation
router.get('/sitemap.xml', async (req, res) => {
  try {
    const baseUrl = process.env.BASE_URL || 'https://streamonpod.com';
    const currentDate = new Date().toISOString().split('T')[0];
    
    // Static pages
    const staticPages = [
      { url: '/', priority: '1.0', changefreq: 'daily' },
      { url: '/login', priority: '0.8', changefreq: 'monthly' },
      { url: '/register', priority: '0.8', changefreq: 'monthly' },
      { url: '/subscription', priority: '0.9', changefreq: 'weekly' },
      { url: '/privacy-policy', priority: '0.5', changefreq: 'yearly' },
      { url: '/tos', priority: '0.5', changefreq: 'yearly' }
    ];

    // Get subscription plans for dynamic content
    let plans = [];
    try {
      plans = await Subscription.getAllPlans();
    } catch (error) {
      console.error('Error fetching plans for sitemap:', error);
    }

    // Build sitemap XML
    let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

    // Add static pages
    staticPages.forEach(page => {
      sitemap += `
  <url>
    <loc>${baseUrl}${page.url}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`;
    });

    // Add subscription plan pages if available
    if (plans && plans.length > 0) {
      plans.forEach(plan => {
        if (plan.name && plan.name !== 'Preview') {
          sitemap += `
  <url>
    <loc>${baseUrl}/subscription?plan=${encodeURIComponent(plan.name.toLowerCase())}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>`;
        }
      });
    }

    sitemap += `
</urlset>`;

    res.set('Content-Type', 'application/xml');
    res.send(sitemap);
    
  } catch (error) {
    console.error('Error generating sitemap:', error);
    res.status(500).send('Error generating sitemap');
  }
});

// SEO health check endpoint
router.get('/seo-health', (req, res) => {
  const baseUrl = process.env.BASE_URL || 'https://streamonpod.com';
  
  res.json({
    status: 'OK',
    seo: {
      robots: `${baseUrl}/robots.txt`,
      sitemap: `${baseUrl}/sitemap.xml`,
      structured_data: 'JSON-LD implemented',
      open_graph: 'Implemented',
      twitter_cards: 'Implemented',
      canonical_urls: 'Implemented'
    },
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
