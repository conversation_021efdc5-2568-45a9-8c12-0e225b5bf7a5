#!/usr/bin/env node

/**
 * Cleanup FFmpeg Processes
 * Force cleanup any orphaned FFmpeg processes that might still be running
 */

const { exec } = require('child_process');
const os = require('os');

console.log('🧹 FFmpeg Process Cleanup Tool\n');

function cleanupFFmpegProcesses() {
  return new Promise((resolve) => {
    if (os.platform() === 'win32') {
      // Windows
      console.log('🖥️  Windows detected - checking for FFmpeg processes...');
      
      exec('tasklist /FI "IMAGENAME eq ffmpeg.exe" /FO CSV', (error, stdout) => {
        if (error) {
          console.log('❌ Error checking processes:', error.message);
          resolve();
          return;
        }
        
        const lines = stdout.split('\n');
        const ffmpegProcesses = lines.filter(line => line.includes('ffmpeg.exe'));
        
        console.log(`📊 Found ${Math.max(0, ffmpegProcesses.length - 1)} FFmpeg processes`);
        
        if (ffmpegProcesses.length > 1) { // More than header line
          console.log('🔧 Killing FFmpeg processes...');
          
          exec('taskkill /F /IM ffmpeg.exe', (killError, killStdout) => {
            if (killError) {
              console.log('❌ Error killing processes:', killError.message);
            } else {
              console.log('✅ FFmpeg processes killed');
              console.log(killStdout);
            }
            resolve();
          });
        } else {
          console.log('✅ No FFmpeg processes to kill');
          resolve();
        }
      });
      
    } else {
      // Linux/Mac
      console.log('🐧 Unix-like system detected - checking for FFmpeg processes...');
      
      exec('pgrep -f ffmpeg', (error, stdout) => {
        if (error) {
          console.log('✅ No FFmpeg processes found');
          resolve();
          return;
        }
        
        const pids = stdout.trim().split('\n').filter(pid => pid);
        console.log(`📊 Found ${pids.length} FFmpeg processes: ${pids.join(', ')}`);
        
        if (pids.length > 0) {
          console.log('🔧 Killing FFmpeg processes...');
          
          exec(`kill -9 ${pids.join(' ')}`, (killError) => {
            if (killError) {
              console.log('❌ Error killing processes:', killError.message);
            } else {
              console.log('✅ FFmpeg processes killed');
            }
            resolve();
          });
        } else {
          resolve();
        }
      });
    }
  });
}

// Also check for any StreamOnPod related processes
function checkStreamOnPodProcesses() {
  return new Promise((resolve) => {
    if (os.platform() === 'win32') {
      exec('tasklist /FI "WINDOWTITLE eq StreamOnPod*" /FO CSV', (error, stdout) => {
        if (!error && stdout) {
          const lines = stdout.split('\n');
          const streamProcesses = lines.filter(line => line.includes('StreamOnPod'));
          if (streamProcesses.length > 1) {
            console.log(`📊 Found ${streamProcesses.length - 1} StreamOnPod related processes`);
          }
        }
        resolve();
      });
    } else {
      exec('pgrep -f "streamonpod\\|StreamOnPod"', (error, stdout) => {
        if (!error && stdout) {
          const pids = stdout.trim().split('\n').filter(pid => pid);
          if (pids.length > 0) {
            console.log(`📊 Found ${pids.length} StreamOnPod related processes: ${pids.join(', ')}`);
          }
        }
        resolve();
      });
    }
  });
}

async function main() {
  try {
    console.log('🔍 Checking for orphaned processes...\n');
    
    await checkStreamOnPodProcesses();
    await cleanupFFmpegProcesses();
    
    console.log('\n✅ Cleanup completed!');
    console.log('💡 You can now safely restart StreamOnPod');
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { cleanupFFmpegProcesses, checkStreamOnPodProcesses };
