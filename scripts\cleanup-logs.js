#!/usr/bin/env node

/**
 * Log Cleanup Script for StreamOnPod
 * Removes old log files and manages disk space
 */

const fs = require('fs');
const path = require('path');

const logDir = path.join(__dirname, '..', 'logs');
const retentionDays = parseInt(process.env.LOG_RETENTION_DAYS) || 30;
const maxLogSize = parseInt(process.env.LOG_ROTATION_SIZE) || 10 * 1024 * 1024; // 10MB

async function cleanupLogs() {
  console.log('🧹 Starting log cleanup...');
  
  if (!fs.existsSync(logDir)) {
    console.log('📁 No logs directory found, nothing to clean');
    return;
  }

  const files = fs.readdirSync(logDir);
  const now = Date.now();
  const retentionMs = retentionDays * 24 * 60 * 60 * 1000;
  
  let deletedFiles = 0;
  let totalSizeFreed = 0;

  for (const file of files) {
    const filePath = path.join(logDir, file);
    
    try {
      const stats = fs.statSync(filePath);
      const fileAge = now - stats.mtime.getTime();
      
      // Delete files older than retention period
      if (fileAge > retentionMs) {
        totalSizeFreed += stats.size;
        fs.unlinkSync(filePath);
        deletedFiles++;
        console.log(`🗑️  Deleted old log file: ${file} (${(stats.size / 1024 / 1024).toFixed(2)}MB)`);
      }
      // Rotate large files
      else if (stats.size > maxLogSize && !file.includes('.')) {
        const rotatedName = `${file}.${Date.now()}`;
        fs.renameSync(filePath, path.join(logDir, rotatedName));
        console.log(`🔄 Rotated large log file: ${file} -> ${rotatedName}`);
      }
    } catch (error) {
      console.error(`❌ Error processing ${file}:`, error.message);
    }
  }

  console.log(`✅ Cleanup complete: ${deletedFiles} files deleted, ${(totalSizeFreed / 1024 / 1024).toFixed(2)}MB freed`);
}

// Run cleanup
cleanupLogs().catch(error => {
  console.error('❌ Log cleanup failed:', error);
  process.exit(1);
});
