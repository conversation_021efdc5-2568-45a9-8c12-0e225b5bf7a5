#!/usr/bin/env node

/**
 * Orphaned Files Cleanup Script for StreamOnPod
 * Identifies and removes video/thumbnail files that exist on disk but not in database
 */

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

// Configuration
const uploadsDir = path.join(__dirname, '..', 'public', 'uploads');
const videosDir = path.join(uploadsDir, 'videos');
const thumbnailsDir = path.join(uploadsDir, 'thumbnails');
const dbPath = path.join(__dirname, '..', 'db', 'streamonpod.db');

// Dry run mode - set to false to actually delete files
const DRY_RUN = process.argv.includes('--execute') ? false : true;

async function getVideosFromDatabase() {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        console.error('❌ Error opening database:', err.message);
        return reject(err);
      }
    });

    db.all('SELECT filepath, thumbnail_path FROM videos', [], (err, rows) => {
      if (err) {
        console.error('❌ Error querying database:', err.message);
        db.close();
        return reject(err);
      }

      const videos = {
        filepaths: new Set(),
        thumbnailPaths: new Set()
      };

      rows.forEach(row => {
        if (row.filepath) {
          // Remove leading slash and convert to relative path
          const relativePath = row.filepath.replace(/^\//, '');
          videos.filepaths.add(relativePath);
        }
        if (row.thumbnail_path) {
          // Remove leading slash and convert to relative path
          const relativePath = row.thumbnail_path.replace(/^\//, '');
          videos.thumbnailPaths.add(relativePath);
        }
      });

      db.close((err) => {
        if (err) {
          console.error('❌ Error closing database:', err.message);
        }
      });

      resolve(videos);
    });
  });
}

function getFilesInDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.warn(`⚠️ Directory does not exist: ${dirPath}`);
    return [];
  }

  try {
    return fs.readdirSync(dirPath).filter(file => {
      const filePath = path.join(dirPath, file);
      return fs.statSync(filePath).isFile();
    });
  } catch (error) {
    console.error(`❌ Error reading directory ${dirPath}:`, error.message);
    return [];
  }
}

function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

async function findOrphanedFiles() {
  console.log('🔍 Scanning for orphaned files...\n');

  try {
    // Get video records from database
    const dbVideos = await getVideosFromDatabase();
    console.log(`📊 Found ${dbVideos.filepaths.size} video records and ${dbVideos.thumbnailPaths.size} thumbnail records in database`);

    // Get actual files on disk
    const videoFiles = getFilesInDirectory(videosDir);
    const thumbnailFiles = getFilesInDirectory(thumbnailsDir);
    console.log(`📁 Found ${videoFiles.length} video files and ${thumbnailFiles.length} thumbnail files on disk\n`);

    // Find orphaned video files
    const orphanedVideos = [];
    let totalVideoSize = 0;

    videoFiles.forEach(file => {
      const relativePath = `uploads/videos/${file}`;
      if (!dbVideos.filepaths.has(relativePath)) {
        const fullPath = path.join(videosDir, file);
        const stats = fs.statSync(fullPath);
        orphanedVideos.push({
          file,
          path: fullPath,
          size: stats.size,
          modified: stats.mtime
        });
        totalVideoSize += stats.size;
      }
    });

    // Find orphaned thumbnail files
    const orphanedThumbnails = [];
    let totalThumbnailSize = 0;

    thumbnailFiles.forEach(file => {
      const relativePath = `uploads/thumbnails/${file}`;
      let isOrphaned = !dbVideos.thumbnailPaths.has(relativePath);

      // For WebP files, also check if corresponding JPG exists in database
      if (isOrphaned && file.endsWith('.webp')) {
        const jpgPath = relativePath.replace(/\.webp$/i, '.jpg');
        const jpegPath = relativePath.replace(/\.webp$/i, '.jpeg');
        isOrphaned = !dbVideos.thumbnailPaths.has(jpgPath) && !dbVideos.thumbnailPaths.has(jpegPath);
      }

      if (isOrphaned) {
        const fullPath = path.join(thumbnailsDir, file);
        const stats = fs.statSync(fullPath);
        orphanedThumbnails.push({
          file,
          path: fullPath,
          size: stats.size,
          modified: stats.mtime
        });
        totalThumbnailSize += stats.size;
      }
    });

    // Report findings
    console.log('📋 ORPHANED FILES REPORT');
    console.log('========================\n');

    if (orphanedVideos.length > 0) {
      console.log(`🎥 Orphaned Video Files (${orphanedVideos.length}):`);
      orphanedVideos.forEach(video => {
        console.log(`   • ${video.file} (${formatFileSize(video.size)}) - Modified: ${video.modified.toISOString()}`);
      });
      console.log(`   Total size: ${formatFileSize(totalVideoSize)}\n`);
    } else {
      console.log('✅ No orphaned video files found\n');
    }

    if (orphanedThumbnails.length > 0) {
      console.log(`🖼️ Orphaned Thumbnail Files (${orphanedThumbnails.length}):`);
      orphanedThumbnails.forEach(thumb => {
        console.log(`   • ${thumb.file} (${formatFileSize(thumb.size)}) - Modified: ${thumb.modified.toISOString()}`);
      });
      console.log(`   Total size: ${formatFileSize(totalThumbnailSize)}\n`);
    } else {
      console.log('✅ No orphaned thumbnail files found\n');
    }

    const totalOrphaned = orphanedVideos.length + orphanedThumbnails.length;
    const totalSize = totalVideoSize + totalThumbnailSize;

    if (totalOrphaned > 0) {
      console.log(`💾 Total orphaned files: ${totalOrphaned} (${formatFileSize(totalSize)})`);
      
      if (DRY_RUN) {
        console.log('\n🔒 DRY RUN MODE - No files will be deleted');
        console.log('   Run with --execute flag to actually delete orphaned files');
        console.log('   Example: node scripts/cleanup-orphaned-files.js --execute');
      } else {
        console.log('\n🗑️ DELETING ORPHANED FILES...');
        
        let deletedCount = 0;
        let deletedSize = 0;
        const errors = [];

        // Delete orphaned videos
        orphanedVideos.forEach(video => {
          try {
            fs.unlinkSync(video.path);
            console.log(`   ✅ Deleted video: ${video.file}`);
            deletedCount++;
            deletedSize += video.size;
          } catch (error) {
            console.error(`   ❌ Failed to delete video ${video.file}:`, error.message);
            errors.push(`Video ${video.file}: ${error.message}`);
          }
        });

        // Delete orphaned thumbnails
        orphanedThumbnails.forEach(thumb => {
          try {
            fs.unlinkSync(thumb.path);
            console.log(`   ✅ Deleted thumbnail: ${thumb.file}`);
            deletedCount++;
            deletedSize += thumb.size;
          } catch (error) {
            console.error(`   ❌ Failed to delete thumbnail ${thumb.file}:`, error.message);
            errors.push(`Thumbnail ${thumb.file}: ${error.message}`);
          }
        });

        console.log(`\n📊 CLEANUP SUMMARY:`);
        console.log(`   Files deleted: ${deletedCount}/${totalOrphaned}`);
        console.log(`   Space freed: ${formatFileSize(deletedSize)}`);
        
        if (errors.length > 0) {
          console.log(`   Errors: ${errors.length}`);
          errors.forEach(error => console.log(`     • ${error}`));
        }
      }
    } else {
      console.log('🎉 No orphaned files found - your storage is clean!');
    }

  } catch (error) {
    console.error('❌ Error during cleanup:', error.message);
    process.exit(1);
  }
}

// Run the cleanup
console.log('🧹 StreamOnPod Orphaned Files Cleanup');
console.log('=====================================\n');

findOrphanedFiles().then(() => {
  console.log('\n✅ Cleanup scan completed');
}).catch(error => {
  console.error('❌ Cleanup failed:', error);
  process.exit(1);
});
