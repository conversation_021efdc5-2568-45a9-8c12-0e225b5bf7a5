#!/usr/bin/env node

/**
 * Comprehensive Test Suite for StreamOnPod
 * 
 * Runs all tests in organized manner:
 * 1. Critical security fixes
 * 2. High priority performance fixes
 * 3. Medium priority improvements
 * 4. Low priority error handling
 * 5. Application functionality tests
 * 6. Performance monitoring tests
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');

console.log('🧪 StreamOnPod Comprehensive Test Suite\n');

// Configuration
const CONFIG = {
  LOG_FILE: './logs/comprehensive-tests.log',
  RESULTS_FILE: './logs/test-results.json',
  TIMEOUT: 30000, // 30 seconds per test
  PARALLEL_TESTS: false // Set to true for faster execution
};

// Test suites configuration
const TEST_SUITES = [
  {
    name: 'Streaming Service Tests',
    priority: 'HIGH',
    script: 'scripts/testStreamingFixes.js',
    required: true,
    description: 'Tests for streaming functionality and fixes'
  },
  {
    name: 'Stream Management Tests',
    priority: 'HIGH',
    script: 'scripts/testStreamFixes.js',
    required: true,
    description: 'Tests for stream status and management'
  },
  {
    name: 'Upload System Tests',
    priority: 'MEDIUM',
    script: 'scripts/test-upload.js',
    required: false,
    description: 'Tests for file upload functionality'
  },
  {
    name: 'Production Configuration Tests',
    priority: 'HIGH',
    script: 'scripts/validateProductionConfig.js',
    required: true,
    description: 'Tests for production environment configuration'
  },
  {
    name: 'Midtrans Payment Tests',
    priority: 'MEDIUM',
    script: 'scripts/testMidtrans.js',
    required: false,
    description: 'Tests for payment system functionality'
  }
];

// Utility functions
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] [${level}] ${message}`;
  
  console.log(logEntry);
  
  // Ensure log directory exists
  const logDir = path.dirname(CONFIG.LOG_FILE);
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
  
  // Write to log file
  fs.appendFileSync(CONFIG.LOG_FILE, logEntry + '\n');
}

function saveResults(results) {
  const resultsDir = path.dirname(CONFIG.RESULTS_FILE);
  if (!fs.existsSync(resultsDir)) {
    fs.mkdirSync(resultsDir, { recursive: true });
  }
  
  fs.writeFileSync(CONFIG.RESULTS_FILE, JSON.stringify(results, null, 2));
}

async function runTest(testSuite) {
  log(`Running: ${testSuite.name}`, 'INFO');
  
  const startTime = Date.now();
  
  try {
    // Check if test script exists
    if (!fs.existsSync(testSuite.script)) {
      return {
        name: testSuite.name,
        priority: testSuite.priority,
        status: 'SKIPPED',
        message: 'Test script not found',
        duration: 0,
        required: testSuite.required
      };
    }
    
    // Run the test
    const output = execSync(`node ${testSuite.script}`, {
      timeout: CONFIG.TIMEOUT,
      encoding: 'utf8'
    });
    
    const duration = Date.now() - startTime;
    
    log(`✅ ${testSuite.name} passed (${duration}ms)`, 'SUCCESS');
    
    return {
      name: testSuite.name,
      priority: testSuite.priority,
      status: 'PASSED',
      message: 'Test completed successfully',
      duration,
      output: output.substring(0, 500), // Limit output size
      required: testSuite.required
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    
    log(`❌ ${testSuite.name} failed: ${error.message}`, 'ERROR');
    
    return {
      name: testSuite.name,
      priority: testSuite.priority,
      status: 'FAILED',
      message: error.message,
      duration,
      output: error.stdout || error.stderr || '',
      required: testSuite.required
    };
  }
}

async function runTestsSequentially() {
  const results = [];
  
  for (const testSuite of TEST_SUITES) {
    const result = await runTest(testSuite);
    results.push(result);
    
    // Stop on critical failures if required
    if (result.status === 'FAILED' && result.required && result.priority === 'CRITICAL') {
      log(`Critical test failed: ${result.name}. Stopping test suite.`, 'ERROR');
      break;
    }
  }
  
  return results;
}

async function runTestsParallel() {
  log('Running tests in parallel...', 'INFO');
  
  const promises = TEST_SUITES.map(testSuite => runTest(testSuite));
  const results = await Promise.all(promises);
  
  return results;
}

function generateReport(results) {
  log('\n📊 TEST RESULTS SUMMARY', 'INFO');
  log('=' .repeat(50), 'INFO');
  
  const stats = {
    total: results.length,
    passed: 0,
    failed: 0,
    skipped: 0,
    critical_failures: 0,
    required_failures: 0
  };
  
  // Calculate statistics
  results.forEach(result => {
    switch (result.status) {
      case 'PASSED':
        stats.passed++;
        break;
      case 'FAILED':
        stats.failed++;
        if (result.priority === 'CRITICAL') {
          stats.critical_failures++;
        }
        if (result.required) {
          stats.required_failures++;
        }
        break;
      case 'SKIPPED':
        stats.skipped++;
        break;
    }
  });
  
  // Display summary
  log(`Total Tests: ${stats.total}`, 'INFO');
  log(`✅ Passed: ${stats.passed}`, 'SUCCESS');
  log(`❌ Failed: ${stats.failed}`, stats.failed > 0 ? 'ERROR' : 'INFO');
  log(`⏭️  Skipped: ${stats.skipped}`, 'INFO');
  
  if (stats.critical_failures > 0) {
    log(`🚨 Critical Failures: ${stats.critical_failures}`, 'ERROR');
  }
  
  if (stats.required_failures > 0) {
    log(`⚠️  Required Test Failures: ${stats.required_failures}`, 'ERROR');
  }
  
  // Display detailed results
  log('\n📋 DETAILED RESULTS', 'INFO');
  log('-' .repeat(50), 'INFO');
  
  results.forEach(result => {
    const status = result.status === 'PASSED' ? '✅' : 
                   result.status === 'FAILED' ? '❌' : '⏭️';
    const priority = `[${result.priority}]`;
    const required = result.required ? '[REQUIRED]' : '[OPTIONAL]';
    
    log(`${status} ${priority} ${required} ${result.name}`, 'INFO');
    log(`   Duration: ${result.duration}ms`, 'INFO');
    log(`   Message: ${result.message}`, 'INFO');
    
    if (result.status === 'FAILED' && result.output) {
      log(`   Output: ${result.output.substring(0, 200)}...`, 'INFO');
    }
    log('', 'INFO');
  });
  
  // Overall assessment
  const overallSuccess = stats.critical_failures === 0 && stats.required_failures === 0;
  
  log('\n🎯 OVERALL ASSESSMENT', 'INFO');
  log('=' .repeat(50), 'INFO');
  
  if (overallSuccess) {
    log('🎉 ALL CRITICAL AND REQUIRED TESTS PASSED!', 'SUCCESS');
    log('✅ System is ready for production deployment', 'SUCCESS');
  } else {
    log('❌ CRITICAL OR REQUIRED TESTS FAILED!', 'ERROR');
    log('⚠️  System is NOT ready for production deployment', 'ERROR');
    log('🔧 Please fix failing tests before deploying', 'ERROR');
  }
  
  return {
    stats,
    results,
    overallSuccess,
    timestamp: new Date().toISOString()
  };
}

// Main test function
async function main() {
  log('Starting comprehensive test suite...', 'INFO');
  log(`Parallel execution: ${CONFIG.PARALLEL_TESTS}`, 'INFO');
  log(`Test timeout: ${CONFIG.TIMEOUT}ms`, 'INFO');
  
  const startTime = Date.now();
  
  try {
    // Run tests
    const results = CONFIG.PARALLEL_TESTS ? 
      await runTestsParallel() : 
      await runTestsSequentially();
    
    // Generate report
    const report = generateReport(results);
    
    // Save results
    saveResults(report);
    
    const totalDuration = Date.now() - startTime;
    log(`\n⏱️  Total execution time: ${totalDuration}ms`, 'INFO');
    log(`📄 Results saved to: ${CONFIG.RESULTS_FILE}`, 'INFO');
    log(`📄 Logs saved to: ${CONFIG.LOG_FILE}`, 'INFO');
    
    // Exit with appropriate code
    process.exit(report.overallSuccess ? 0 : 1);
    
  } catch (error) {
    log(`Test suite execution failed: ${error.message}`, 'ERROR');
    process.exit(1);
  }
}

// Run test suite if executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Test suite error:', error);
    process.exit(1);
  });
}

module.exports = { main, runTest, generateReport };
