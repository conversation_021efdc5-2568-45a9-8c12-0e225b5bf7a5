#!/usr/bin/env node

/**
 * Debug Stream Start Process
 * 
 * This script helps debug what happens when a stream starts
 * by monitoring the process in real-time
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

const Stream = require('../models/Stream');
const streamingService = require('../services/streamingService');

async function monitorStreamStart(streamId) {
  console.log(`🔍 Monitoring stream ${streamId} start process...`);
  console.log('='.repeat(60));

  let monitoringInterval;
  let startTime = Date.now();
  let maxMonitorTime = 30000; // 30 seconds

  const monitor = async () => {
    try {
      const elapsed = Date.now() - startTime;
      
      if (elapsed > maxMonitorTime) {
        console.log(`⏰ Monitoring timeout reached (${maxMonitorTime/1000}s)`);
        clearInterval(monitoringInterval);
        return;
      }

      // Get current state
      const stream = await Stream.findById(streamId);
      const isInMemory = streamingService.isStreamActive(streamId);
      const activeStreams = streamingService.getActiveStreams();
      
      console.log(`\n⏱️  ${Math.floor(elapsed/1000)}s - Stream Status Check:`);
      console.log(`   📊 Database Status: ${stream ? stream.status : 'NOT_FOUND'}`);
      console.log(`   💾 In Memory: ${isInMemory}`);
      console.log(`   🔢 Total Active Streams: ${activeStreams.length}`);
      
      if (isInMemory) {
        // Get process details
        const activeStreamsList = streamingService.getActiveStreams();
        const processExists = activeStreamsList.includes(streamId);
        
        console.log(`   🔍 Process Details:`);
        console.log(`     - Found in active list: ${processExists}`);
        
        // Try to validate
        try {
          const isValid = streamingService.validateStreamProcess(streamId);
          console.log(`     - Validation result: ${isValid}`);
        } catch (error) {
          console.log(`     - Validation error: ${error.message}`);
        }
      }

      // Check for inconsistencies
      if (stream && stream.status === 'live' && !isInMemory) {
        console.log(`   ⚠️  INCONSISTENCY: Marked live but not in memory`);
      } else if (stream && stream.status !== 'live' && isInMemory) {
        console.log(`   ⚠️  INCONSISTENCY: In memory but not marked live (status: ${stream.status})`);
      } else if (stream && stream.status === 'live' && isInMemory) {
        console.log(`   ✅ CONSISTENT: Live in DB and in memory`);
      } else if (stream && stream.status === 'offline' && !isInMemory) {
        console.log(`   ✅ CONSISTENT: Offline in DB and not in memory`);
      }

      // Show recent logs
      const logs = streamingService.getStreamLogs(streamId);
      if (logs && logs.length > 0) {
        const recentLogs = logs.slice(-3); // Last 3 logs
        console.log(`   📝 Recent Logs:`);
        recentLogs.forEach(log => {
          console.log(`     - ${log}`);
        });
      }

    } catch (error) {
      console.error(`❌ Error during monitoring:`, error.message);
    }
  };

  // Start monitoring
  console.log(`🚀 Starting monitoring for stream ${streamId}...`);
  await monitor(); // Initial check
  
  monitoringInterval = setInterval(monitor, 2000); // Check every 2 seconds

  // Stop monitoring after timeout
  setTimeout(() => {
    if (monitoringInterval) {
      clearInterval(monitoringInterval);
      console.log(`\n🏁 Monitoring completed for stream ${streamId}`);
      console.log('='.repeat(60));
    }
  }, maxMonitorTime);
}

async function listStreams() {
  try {
    const streams = await Stream.findAll();
    console.log('\n📋 Available Streams:');
    console.log('='.repeat(40));
    
    if (streams.length === 0) {
      console.log('No streams found');
      return;
    }

    streams.forEach(stream => {
      const isInMemory = streamingService.isStreamActive(stream.id);
      const statusIcon = stream.status === 'live' ? '🟢' : 
                        stream.status === 'offline' ? '🔴' : 
                        stream.status === 'error' ? '❌' : '⚪';
      const memoryIcon = isInMemory ? '💾' : '❌';
      
      console.log(`${statusIcon} ${stream.id} - ${stream.title}`);
      console.log(`   Status: ${stream.status} | In Memory: ${memoryIcon}`);
      console.log(`   Platform: ${stream.platform} | Updated: ${stream.status_updated_at || 'Never'}`);
      console.log('');
    });
  } catch (error) {
    console.error('Error listing streams:', error);
  }
}

async function main() {
  console.log('🔧 Stream Start Debug Tool');
  console.log('==========================\n');

  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('Usage:');
    console.log('  node debug-stream-start.js <stream-id>  - Monitor specific stream');
    console.log('  node debug-stream-start.js list         - List all streams');
    console.log('');
    await listStreams();
    return;
  }

  const command = args[0];

  if (command === 'list') {
    await listStreams();
    return;
  }

  // Monitor specific stream
  const streamId = command;
  
  try {
    const stream = await Stream.findById(streamId);
    if (!stream) {
      console.log(`❌ Stream ${streamId} not found`);
      await listStreams();
      return;
    }

    console.log(`📊 Stream Info:`);
    console.log(`   ID: ${stream.id}`);
    console.log(`   Title: ${stream.title}`);
    console.log(`   Platform: ${stream.platform}`);
    console.log(`   Current Status: ${stream.status}`);
    console.log(`   Last Updated: ${stream.status_updated_at || 'Never'}`);
    console.log('');

    await monitorStreamStart(streamId);

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Handle Ctrl+C
process.on('SIGINT', () => {
  console.log('\n👋 Monitoring stopped by user');
  process.exit(0);
});

if (require.main === module) {
  main().catch(error => {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { monitorStreamStart };
