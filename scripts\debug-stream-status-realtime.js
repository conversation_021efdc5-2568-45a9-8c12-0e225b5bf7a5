#!/usr/bin/env node

/**
 * Real-time Stream Status Debugging Script
 * 
 * This script monitors stream status changes in real-time and helps debug
 * the timing issues between FFmpeg process start and status updates.
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

const Stream = require('../models/Stream');
const streamingService = require('../services/streamingService');

let monitoring = false;
let monitoringInterval;

async function getCurrentStatus() {
  try {
    const activeStreamsInMemory = streamingService.getActiveStreams();
    const liveStreamsInDB = await Stream.findAll(null, 'live');
    const allStreams = await Stream.findAll();

    return {
      timestamp: new Date().toISOString(),
      activeInMemory: activeStreamsInMemory.length,
      liveInDB: liveStreamsInDB.length,
      totalStreams: allStreams.length,
      activeIds: activeStreamsInMemory,
      liveIds: liveStreamsInDB.map(s => s.id),
      streams: allStreams.map(s => ({
        id: s.id,
        title: s.title,
        status: s.status,
        lastUpdated: s.status_updated_at,
        inMemory: activeStreamsInMemory.includes(s.id)
      }))
    };
  } catch (error) {
    console.error('Error getting current status:', error);
    return null;
  }
}

function displayStatus(status) {
  if (!status) return;

  console.clear();
  console.log('🔍 Real-time Stream Status Monitor');
  console.log('==================================');
  console.log(`⏰ Time: ${new Date(status.timestamp).toLocaleTimeString()}`);
  console.log(`📊 Active in Memory: ${status.activeInMemory}`);
  console.log(`📊 Live in Database: ${status.liveInDB}`);
  console.log(`📊 Total Streams: ${status.totalStreams}`);
  console.log('');

  if (status.streams.length > 0) {
    console.log('📋 Stream Details:');
    console.log('==================');
    
    status.streams.forEach(stream => {
      const statusIcon = stream.status === 'live' ? '🟢' : 
                        stream.status === 'offline' ? '🔴' : 
                        stream.status === 'error' ? '❌' : '⚪';
      
      const memoryIcon = stream.inMemory ? '💾' : '❌';
      
      console.log(`${statusIcon} ${stream.id} - ${stream.title}`);
      console.log(`   Status: ${stream.status} | In Memory: ${memoryIcon} | Updated: ${stream.lastUpdated || 'Never'}`);
      
      // Check for inconsistencies
      if (stream.status === 'live' && !stream.inMemory) {
        console.log(`   ⚠️  INCONSISTENT: Marked live but not in memory`);
      } else if (stream.status !== 'live' && stream.inMemory) {
        console.log(`   ⚠️  INCONSISTENT: In memory but not marked live`);
      }
      console.log('');
    });
  } else {
    console.log('📋 No streams found');
  }

  console.log('Commands: [q]uit | [s]ync | [t]est | [c]lear');
  console.log('Press Ctrl+C to exit');
}

async function startMonitoring() {
  if (monitoring) {
    console.log('Already monitoring...');
    return;
  }

  monitoring = true;
  console.log('🚀 Starting real-time monitoring...');
  
  // Initial display
  const initialStatus = await getCurrentStatus();
  displayStatus(initialStatus);

  // Monitor every 2 seconds
  monitoringInterval = setInterval(async () => {
    const status = await getCurrentStatus();
    displayStatus(status);
  }, 2000);
}

function stopMonitoring() {
  if (!monitoring) return;
  
  monitoring = false;
  if (monitoringInterval) {
    clearInterval(monitoringInterval);
    monitoringInterval = null;
  }
  console.log('⏹️  Monitoring stopped');
}

async function manualSync() {
  console.log('🔄 Running manual sync...');
  try {
    await streamingService.syncStreamStatuses();
    console.log('✅ Manual sync completed');
  } catch (error) {
    console.error('❌ Manual sync failed:', error);
  }
}

async function runTest() {
  console.log('🧪 Running stream status test...');
  try {
    const { testStreamStatus } = require('./test-stream-status');
    await testStreamStatus();
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Handle keyboard input
process.stdin.setRawMode(true);
process.stdin.resume();
process.stdin.setEncoding('utf8');

process.stdin.on('data', async (key) => {
  switch (key.toLowerCase()) {
    case 'q':
    case '\u0003': // Ctrl+C
      stopMonitoring();
      console.log('\n👋 Goodbye!');
      process.exit(0);
      break;
    
    case 's':
      await manualSync();
      break;
    
    case 't':
      stopMonitoring();
      await runTest();
      setTimeout(() => startMonitoring(), 2000);
      break;
    
    case 'c':
      console.clear();
      break;
  }
});

// Handle process termination
process.on('SIGINT', () => {
  stopMonitoring();
  console.log('\n👋 Goodbye!');
  process.exit(0);
});

process.on('SIGTERM', () => {
  stopMonitoring();
  process.exit(0);
});

// Start monitoring
console.log('🔍 Stream Status Real-time Monitor');
console.log('===================================');
console.log('This tool monitors stream status changes in real-time.');
console.log('It helps debug timing issues between FFmpeg and status updates.');
console.log('');

startMonitoring().catch(error => {
  console.error('❌ Failed to start monitoring:', error);
  process.exit(1);
});
