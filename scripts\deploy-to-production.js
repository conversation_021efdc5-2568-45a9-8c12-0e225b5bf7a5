#!/usr/bin/env node

/**
 * StreamOnPod Production Deployment Script
 * Final deployment with all security and performance optimizations
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 StreamOnPod Production Deployment\n');

// Configuration
const BACKUP_DIR = './backups/production-deployment';
const TIMESTAMP = new Date().toISOString().replace(/[:.]/g, '-');

// Helper functions
function log(message, type = 'INFO') {
  const timestamp = new Date().toISOString();
  const prefix = type === 'ERROR' ? '❌' : type === 'WARN' ? '⚠️' : '✅';
  console.log(`${prefix} [${type}] ${message}`);
}

function createBackup() {
  log('Creating backup before deployment...');
  
  if (!fs.existsSync(BACKUP_DIR)) {
    fs.mkdirSync(BACKUP_DIR, { recursive: true });
  }
  
  const backupPath = path.join(BACKUP_DIR, `backup-${TIMESTAMP}`);
  fs.mkdirSync(backupPath, { recursive: true });
  
  // Backup critical files
  const filesToBackup = [
    '.env',
    'app.js',
    'package.json',
    'db/streamonpod.db'
  ];
  
  filesToBackup.forEach(file => {
    if (fs.existsSync(file)) {
      const destDir = path.join(backupPath, path.dirname(file));
      if (!fs.existsSync(destDir)) {
        fs.mkdirSync(destDir, { recursive: true });
      }
      fs.copyFileSync(file, path.join(backupPath, file));
      log(`Backed up: ${file}`);
    }
  });
  
  log(`Backup created at: ${backupPath}`);
  return backupPath;
}

function runProductionReadinessCheck() {
  log('Running production readiness check...');
  
  try {
    execSync('node scripts/production-readiness-check.js', { stdio: 'inherit' });
    log('Production readiness check passed!');
    return true;
  } catch (error) {
    log('Production readiness check failed!', 'ERROR');
    return false;
  }
}

function optimizeForProduction() {
  log('Applying production optimizations...');
  
  // Ensure production environment
  const envContent = fs.readFileSync('.env', 'utf8');
  if (!envContent.includes('NODE_ENV=production')) {
    log('Setting NODE_ENV to production', 'WARN');
    const updatedEnv = envContent.replace(/NODE_ENV=.*/, 'NODE_ENV=production');
    fs.writeFileSync('.env', updatedEnv);
  }
  
  // Clear development logs
  const logsDir = './logs';
  if (fs.existsSync(logsDir)) {
    const logFiles = fs.readdirSync(logsDir);
    logFiles.forEach(file => {
      if (file.endsWith('.log')) {
        fs.writeFileSync(path.join(logsDir, file), '');
        log(`Cleared log file: ${file}`);
      }
    });
  }
  
  // Clear temporary files
  const tempDirs = ['./temp', './tmp'];
  tempDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true, force: true });
      log(`Cleared temp directory: ${dir}`);
    }
  });
  
  log('Production optimizations applied');
}

function validateDependencies() {
  log('Validating dependencies...');
  
  try {
    execSync('npm audit --audit-level=high', { stdio: 'pipe' });
    log('No high-severity vulnerabilities found');
  } catch (error) {
    log('Security vulnerabilities detected!', 'WARN');
    log('Run "npm audit fix" to resolve issues', 'WARN');
  }
  
  // Check for missing dependencies
  try {
    execSync('npm ls --depth=0', { stdio: 'pipe' });
    log('All dependencies are installed');
  } catch (error) {
    log('Missing dependencies detected!', 'ERROR');
    log('Run "npm install" to install missing packages', 'ERROR');
    return false;
  }
  
  return true;
}

function startProductionServer() {
  log('Starting production server...');
  
  // Kill any existing processes
  try {
    execSync('pkill -f "node app.js"', { stdio: 'pipe' });
    log('Stopped existing server processes');
  } catch (error) {
    // No existing processes, continue
  }
  
  // Start server in background
  log('Starting StreamOnPod in production mode...');
  log('Server will be available at: https://streamonpod.imthe.one');
  log('Local access: http://localhost:7575');
  
  // Note: In a real production environment, you would use PM2 or similar
  // For now, we'll just show the command
  log('To start the server, run: node app.js');
  log('For production with PM2: pm2 start app.js --name streamonpod');
}

function displayPostDeploymentInfo() {
  console.log('\n🎉 Production Deployment Complete!\n');
  
  console.log('📋 Post-Deployment Checklist:');
  console.log('  ✅ Production readiness check passed');
  console.log('  ✅ Security configurations enabled');
  console.log('  ✅ Performance optimizations applied');
  console.log('  ✅ Backup created');
  console.log('  ✅ Dependencies validated');
  
  console.log('\n🔗 Access URLs:');
  console.log('  🌐 Production: https://streamonpod.imthe.one');
  console.log('  🏠 Local: http://localhost:7575');
  
  console.log('\n📊 Monitoring:');
  console.log('  📝 Logs: ./logs/app.log');
  console.log('  📈 Performance: Built-in monitoring enabled');
  console.log('  🚨 Alerts: Configured for CPU/Memory thresholds');
  
  console.log('\n🛠️ Management Commands:');
  console.log('  🔍 Health Check: node scripts/production-readiness-check.js');
  console.log('  📊 Performance: Check dashboard admin panel');
  console.log('  🔄 Restart: pm2 restart streamonpod (if using PM2)');
  
  console.log('\n⚠️  Important Notes:');
  console.log('  🔒 CSRF protection is ENABLED');
  console.log('  🔐 HTTPS is ENFORCED');
  console.log('  💳 Midtrans is in PRODUCTION mode');
  console.log('  📝 Logging is optimized for production');
  
  console.log('\n🎯 Next Steps:');
  console.log('  1. Start the server: node app.js');
  console.log('  2. Test login functionality');
  console.log('  3. Verify payment integration');
  console.log('  4. Monitor system performance');
  console.log('  5. Set up automated backups');
}

// Main deployment process
async function deploy() {
  try {
    log('Starting production deployment process...');
    
    // Step 1: Create backup
    const backupPath = createBackup();
    
    // Step 2: Run production readiness check
    if (!runProductionReadinessCheck()) {
      log('Deployment aborted due to failed readiness check', 'ERROR');
      process.exit(1);
    }
    
    // Step 3: Validate dependencies
    if (!validateDependencies()) {
      log('Deployment aborted due to dependency issues', 'ERROR');
      process.exit(1);
    }
    
    // Step 4: Apply production optimizations
    optimizeForProduction();
    
    // Step 5: Final readiness check
    if (!runProductionReadinessCheck()) {
      log('Final readiness check failed!', 'ERROR');
      process.exit(1);
    }
    
    // Step 6: Display deployment info
    displayPostDeploymentInfo();
    
    log('Production deployment completed successfully!');
    
  } catch (error) {
    log(`Deployment failed: ${error.message}`, 'ERROR');
    process.exit(1);
  }
}

// Run deployment
deploy();
