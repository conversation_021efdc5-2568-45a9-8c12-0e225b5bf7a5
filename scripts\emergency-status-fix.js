#!/usr/bin/env node

/**
 * Emergency Status Fix Script
 * 
 * This script directly fixes stream status issues without loading the full application
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

const Stream = require('../models/Stream');

async function emergencyStatusFix() {
  console.log('🚨 Emergency Stream Status Fix');
  console.log('==============================\n');

  try {
    // 1. Get all streams
    console.log('1. Getting all streams...');
    const allStreams = await Stream.findAll();
    const liveStreams = allStreams.filter(s => s.status === 'live');
    const offlineStreams = allStreams.filter(s => s.status === 'offline');
    const errorStreams = allStreams.filter(s => s.status === 'error');

    console.log(`   📊 Total streams: ${allStreams.length}`);
    console.log(`   🟢 Live streams: ${liveStreams.length}`);
    console.log(`   🔴 Offline streams: ${offlineStreams.length}`);
    console.log(`   ❌ Error streams: ${errorStreams.length}`);

    if (liveStreams.length > 0) {
      console.log(`   🔗 Live stream IDs: ${liveStreams.map(s => s.id).join(', ')}`);
    }

    console.log('');

    // 2. Check for running FFmpeg processes
    console.log('2. Checking for running FFmpeg processes...');
    
    const { exec } = require('child_process');
    const os = require('os');

    return new Promise((resolve) => {
      if (os.platform() === 'win32') {
        exec('tasklist /FI "IMAGENAME eq ffmpeg.exe" /FO CSV', async (error, stdout) => {
          let ffmpegCount = 0;
          
          if (!error && stdout) {
            const lines = stdout.split('\n');
            const ffmpegProcesses = lines.filter(line => line.includes('ffmpeg.exe'));
            ffmpegCount = Math.max(0, ffmpegProcesses.length - 1); // Subtract header line
            
            if (ffmpegCount > 0) {
              console.log(`   🔍 Found ${ffmpegCount} FFmpeg processes running`);
              ffmpegProcesses.slice(1).forEach((line, index) => {
                console.log(`   ${index + 1}. ${line.trim()}`);
              });
            } else {
              console.log(`   ❌ No FFmpeg processes found`);
            }
          } else {
            console.log(`   ⚠️  Could not check FFmpeg processes`);
          }

          console.log('');

          // 3. Analyze inconsistencies
          console.log('3. Analyzing inconsistencies...');
          
          if (liveStreams.length > 0 && ffmpegCount === 0) {
            console.log(`   ⚠️  MAJOR INCONSISTENCY: ${liveStreams.length} streams marked as live but no FFmpeg processes running`);
            console.log('   🔧 Recommendation: Set all live streams to offline');
            
            // Ask for confirmation
            console.log('\n4. Fixing inconsistencies...');
            
            for (const stream of liveStreams) {
              try {
                await Stream.updateStatus(stream.id, 'offline', stream.user_id);
                console.log(`   ✅ Fixed: Updated stream ${stream.id} (${stream.title}) to offline`);
              } catch (error) {
                console.log(`   ❌ Error: Could not update stream ${stream.id}: ${error.message}`);
              }
            }
            
          } else if (liveStreams.length === 0 && ffmpegCount > 0) {
            console.log(`   ⚠️  INCONSISTENCY: ${ffmpegCount} FFmpeg processes running but no streams marked as live`);
            console.log('   🔧 Recommendation: Kill orphaned FFmpeg processes');
            
            console.log('\n4. Cleaning up orphaned processes...');
            exec('taskkill /F /IM ffmpeg.exe', (killError) => {
              if (killError) {
                console.log(`   ❌ Error killing FFmpeg processes: ${killError.message}`);
              } else {
                console.log(`   ✅ Killed ${ffmpegCount} orphaned FFmpeg processes`);
              }
            });
            
          } else if (liveStreams.length === ffmpegCount) {
            console.log(`   ✅ CONSISTENT: ${liveStreams.length} live streams match ${ffmpegCount} FFmpeg processes`);
          } else {
            console.log(`   ⚠️  PARTIAL INCONSISTENCY: ${liveStreams.length} live streams vs ${ffmpegCount} FFmpeg processes`);
            console.log('   🔧 Manual review recommended');
          }

          console.log('');

          // 5. Final status
          console.log('5. Final Status Check...');
          
          try {
            const updatedStreams = await Stream.findAll();
            const updatedLive = updatedStreams.filter(s => s.status === 'live');
            const updatedOffline = updatedStreams.filter(s => s.status === 'offline');
            const updatedError = updatedStreams.filter(s => s.status === 'error');

            console.log(`   📊 Updated counts:`);
            console.log(`   🟢 Live: ${updatedLive.length}`);
            console.log(`   🔴 Offline: ${updatedOffline.length}`);
            console.log(`   ❌ Error: ${updatedError.length}`);

            if (updatedLive.length > 0) {
              console.log(`   🔗 Remaining live streams: ${updatedLive.map(s => s.id).join(', ')}`);
            }

          } catch (error) {
            console.log(`   ❌ Error getting updated status: ${error.message}`);
          }

          console.log('');
          console.log('✅ Emergency status fix completed');
          console.log('');
          console.log('📝 Next Steps:');
          console.log('   1. Start the application: npm start');
          console.log('   2. Test starting a stream');
          console.log('   3. Monitor status consistency');
          console.log('   4. Use manual-sync-safe.js for ongoing monitoring');

          resolve();
        });
      } else {
        // Linux/Mac version
        exec('pgrep -f ffmpeg', async (error, stdout) => {
          let ffmpegCount = 0;
          
          if (!error && stdout) {
            const pids = stdout.trim().split('\n').filter(pid => pid);
            ffmpegCount = pids.length;
            
            if (ffmpegCount > 0) {
              console.log(`   🔍 Found ${ffmpegCount} FFmpeg processes running`);
              pids.forEach((pid, index) => {
                console.log(`   ${index + 1}. PID: ${pid}`);
              });
            } else {
              console.log(`   ❌ No FFmpeg processes found`);
            }
          } else {
            console.log(`   ❌ No FFmpeg processes found`);
          }

          // Similar logic for Linux/Mac...
          // (Implementation similar to Windows version above)
          
          resolve();
        });
      }
    });

  } catch (error) {
    console.error('❌ Error during emergency fix:', error);
  }
}

if (require.main === module) {
  emergencyStatusFix().then(() => {
    console.log('\n👋 Emergency fix completed');
    process.exit(0);
  }).catch(error => {
    console.error('\n❌ Emergency fix failed:', error);
    process.exit(1);
  });
}

module.exports = { emergencyStatusFix };
