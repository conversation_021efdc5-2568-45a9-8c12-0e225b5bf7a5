#!/usr/bin/env node

/**
 * Fix Production Login Issues
 * Creates a proper production configuration that works for login
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

console.log('🔧 Production Login Fixer\n');

// Read current .env file
const envPath = path.join(__dirname, '..', '.env');
const envProductionPath = path.join(__dirname, '..', '.env.production');

if (!fs.existsSync(envPath)) {
  console.error('❌ .env file not found');
  process.exit(1);
}

let envContent = fs.readFileSync(envPath, 'utf8');

console.log('🔍 Current .env configuration:');
console.log(`   NODE_ENV: ${envContent.match(/NODE_ENV=(.+)/)?.[1] || 'not set'}`);
console.log(`   SESSION_SECRET: ${envContent.includes('SESSION_SECRET=') ? 'set' : 'not set'}`);
console.log(`   ENABLE_CONSOLE_LOGGING: ${envContent.match(/ENABLE_CONSOLE_LOGGING=(.+)/)?.[1] || 'not set'}`);

// Create production-ready configuration
console.log('\n🔧 Creating production-ready configuration...');

// Generate secure secrets if needed
let sessionSecret = envContent.match(/SESSION_SECRET=(.+)/)?.[1];
let csrfSecret = envContent.match(/CSRF_SECRET=(.+)/)?.[1];

if (!sessionSecret || sessionSecret.includes('your-secure') || sessionSecret.length < 32) {
  sessionSecret = crypto.randomBytes(32).toString('hex');
  console.log('✅ Generated new SESSION_SECRET');
}

if (!csrfSecret || csrfSecret.includes('your-secure') || csrfSecret.length < 32) {
  csrfSecret = crypto.randomBytes(32).toString('hex');
  console.log('✅ Generated new CSRF_SECRET');
}

// Create optimized production configuration
const productionConfig = `# StreamOnPod Production Environment Configuration
# Optimized for production with working login

# Application Environment
NODE_ENV=production
PORT=7575

# Logging Configuration (Optimized for production but with essential logging)
LOG_LEVEL=warn
ENABLE_FILE_LOGGING=true
ENABLE_CONSOLE_LOGGING=true
ENABLE_VERBOSE_FFMPEG_LOGS=false

# Performance Optimizations
ENABLE_COMPRESSION=true
ENABLE_CACHING=true
CACHE_TTL=3600

# Security Settings (Production-ready)
SESSION_SECRET=${sessionSecret}
CSRF_SECRET=${csrfSecret}

# Database Configuration
DATABASE_PATH=./db/streamonpod.db
ENABLE_DATABASE_LOGGING=false

# FFmpeg Configuration
FFMPEG_TIMEOUT=300000
FFMPEG_MAX_RETRIES=3
ENABLE_FFMPEG_HARDWARE_ACCELERATION=true

# Load Balancer Configuration
LOAD_BALANCER_ENABLED=true
LOAD_BALANCER_CHECK_INTERVAL=30000
CPU_THRESHOLD_HIGH=85
CPU_THRESHOLD_MEDIUM=75
CPU_THRESHOLD_LOW=60

# File Upload Limits
MAX_FILE_SIZE=2147483648
MAX_STORAGE_GB=50

# Notification Settings
NOTIFICATION_CLEANUP_INTERVAL=86400000
MAX_NOTIFICATIONS_PER_USER=100

# System Monitoring
ENABLE_PERFORMANCE_MONITORING=true
SYSTEM_STATS_INTERVAL=60000
MEMORY_THRESHOLD=90
CPU_THRESHOLD=85

# Cleanup and Maintenance
LOG_ROTATION_SIZE=10485760
LOG_RETENTION_DAYS=30
TEMP_FILE_CLEANUP_INTERVAL=3600000

# Midtrans Payment Gateway Configuration
MIDTRANS_SERVER_KEY=SB-Mid-server-L-oAVvIRyXTVVY65L4qCRRLr
MIDTRANS_CLIENT_KEY=SB-Mid-client-Gea0ZBqekgqxBYSf
MIDTRANS_IS_PRODUCTION=false
MIDTRANS_MERCHANT_ID=G463893303

# Base URL for callbacks and webhooks
BASE_URL=https://streamonpod.imthe.one

# Additional Midtrans Settings
MIDTRANS_WEBHOOK_TIMEOUT=30000
MIDTRANS_ENABLE_SIGNATURE_VERIFICATION=true
MIDTRANS_ENABLE_TRANSACTION_LOGGING=true

# Production Session Configuration
# Note: For local production testing, we need to handle HTTPS properly
FORCE_HTTPS=false
TRUST_PROXY=false
`;

// Backup current .env
const backupPath = envPath + '.backup.production.' + Date.now();
fs.copyFileSync(envPath, backupPath);
console.log(`📁 Backup created: ${path.basename(backupPath)}`);

// Write new configuration
fs.writeFileSync(envPath, productionConfig);
console.log('✅ Production configuration applied');

// Update .env.production as well
fs.writeFileSync(envProductionPath, productionConfig);
console.log('✅ .env.production updated');

console.log('\n🔧 Key Changes Made:');
console.log('   - Generated secure SESSION_SECRET and CSRF_SECRET');
console.log('   - Set LOG_LEVEL to "warn" (less verbose than dev, more than error)');
console.log('   - Enabled CONSOLE_LOGGING for debugging');
console.log('   - Kept NODE_ENV=production but optimized for login');
console.log('   - Added session configuration flags');

console.log('\n🚀 Now try:');
console.log('   npm run production');
console.log('   Then go to: http://localhost:7575');

console.log('\n📝 Login credentials:');
console.log('   Username: aufanirsad');
console.log('   Password: [try your usual password]');

console.log('\n🔍 If still having issues:');
console.log('   1. Check browser console for errors');
console.log('   2. Check Network tab for failed requests');
console.log('   3. Look for HTTPS/HTTP cookie issues');
console.log('   4. Run: npm run logs:tail');

console.log('\n✅ Production login configuration complete!');
