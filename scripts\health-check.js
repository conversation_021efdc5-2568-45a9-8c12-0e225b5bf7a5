#!/usr/bin/env node

/**
 * StreamOnPod Health Check Script
 * 
 * Comprehensive health check that validates:
 * 1. Application responsiveness
 * 2. Database connectivity
 * 3. File system access
 * 4. Service dependencies
 * 5. Performance metrics
 * 6. Security configurations
 */

const fs = require('fs');
const path = require('path');
const http = require('http');
const https = require('https');

// Load environment variables
require('dotenv').config();

const { db } = require('../db/database');

console.log('🏥 StreamOnPod Health Check\n');

// Configuration
const CONFIG = {
  BASE_URL: process.env.BASE_URL || 'http://localhost:7575',
  TIMEOUT: 10000,
  LOG_FILE: './logs/health-check.log',
  RESULTS_FILE: './logs/health-check-results.json'
};

// Health check results
const results = {
  timestamp: new Date().toISOString(),
  overall: 'UNKNOWN',
  checks: [],
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    warnings: 0
  }
};

// Utility functions
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] [${level}] ${message}`;
  
  console.log(logEntry);
  
  // Ensure log directory exists
  const logDir = path.dirname(CONFIG.LOG_FILE);
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
  
  // Write to log file
  fs.appendFileSync(CONFIG.LOG_FILE, logEntry + '\n');
}

function addCheck(name, status, message, details = {}) {
  const check = {
    name,
    status, // PASS, FAIL, WARNING
    message,
    details,
    timestamp: new Date().toISOString()
  };
  
  results.checks.push(check);
  results.summary.total++;
  
  switch (status) {
    case 'PASS':
      results.summary.passed++;
      log(`✅ ${name}: ${message}`, 'INFO');
      break;
    case 'FAIL':
      results.summary.failed++;
      log(`❌ ${name}: ${message}`, 'ERROR');
      break;
    case 'WARNING':
      results.summary.warnings++;
      log(`⚠️  ${name}: ${message}`, 'WARNING');
      break;
  }
}

// Health check functions
async function checkApplicationResponsiveness() {
  log('Checking application responsiveness...', 'INFO');

  // Check if this is a pre-deployment check (app not running)
  const isPreDeployment = process.argv.includes('--pre-deployment');

  if (isPreDeployment) {
    addCheck(
      'Application Responsiveness',
      'PASS',
      'Pre-deployment check - application not expected to be running',
      { preDeployment: true }
    );
    return Promise.resolve();
  }

  return new Promise((resolve) => {
    const startTime = Date.now();
    const protocol = CONFIG.BASE_URL.startsWith('https') ? https : http;
    const url = new URL(CONFIG.BASE_URL);

    const req = protocol.request({
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 80),
      path: '/',
      method: 'GET',
      timeout: CONFIG.TIMEOUT
    }, (res) => {
      const responseTime = Date.now() - startTime;

      if (res.statusCode === 200) {
        addCheck(
          'Application Responsiveness',
          'PASS',
          `Application responding normally (${responseTime}ms)`,
          { responseTime, statusCode: res.statusCode }
        );
      } else {
        addCheck(
          'Application Responsiveness',
          'WARNING',
          `Application responding with status ${res.statusCode} (${responseTime}ms)`,
          { responseTime, statusCode: res.statusCode }
        );
      }

      resolve();
    });

    req.on('error', (error) => {
      // For pre-deployment, connection errors are expected
      if (error.code === 'ECONNREFUSED') {
        addCheck(
          'Application Responsiveness',
          'WARNING',
          'Application not running (expected for pre-deployment)',
          { error: error.message, expected: true }
        );
      } else {
        addCheck(
          'Application Responsiveness',
          'FAIL',
          `Application not responding: ${error.message}`,
          { error: error.message }
        );
      }
      resolve();
    });

    req.on('timeout', () => {
      req.destroy();
      addCheck(
        'Application Responsiveness',
        'FAIL',
        `Application timeout after ${CONFIG.TIMEOUT}ms`,
        { timeout: CONFIG.TIMEOUT }
      );
      resolve();
    });

    req.end();
  });
}

async function checkDatabaseConnectivity() {
  log('Checking database connectivity...', 'INFO');
  
  return new Promise((resolve) => {
    try {
      db.get('SELECT 1 as test', (err, row) => {
        if (err) {
          addCheck(
            'Database Connectivity',
            'FAIL',
            `Database connection failed: ${err.message}`,
            { error: err.message }
          );
        } else {
          addCheck(
            'Database Connectivity',
            'PASS',
            'Database connection successful',
            { testQuery: 'SELECT 1', result: row }
          );
        }
        resolve();
      });
    } catch (error) {
      addCheck(
        'Database Connectivity',
        'FAIL',
        `Database connection error: ${error.message}`,
        { error: error.message }
      );
      resolve();
    }
  });
}

function checkFileSystemAccess() {
  log('Checking file system access...', 'INFO');
  
  const criticalPaths = [
    { path: './db', description: 'Database directory' },
    { path: './logs', description: 'Logs directory' },
    { path: './uploads', description: 'Uploads directory' },
    { path: './public', description: 'Public assets directory' }
  ];
  
  let allAccessible = true;
  const details = {};
  
  criticalPaths.forEach(({ path: checkPath, description }) => {
    try {
      // Check if path exists
      const exists = fs.existsSync(checkPath);
      
      if (exists) {
        // Check read/write permissions
        fs.accessSync(checkPath, fs.constants.R_OK | fs.constants.W_OK);
        details[checkPath] = { accessible: true, description };
      } else {
        // Try to create directory
        fs.mkdirSync(checkPath, { recursive: true });
        details[checkPath] = { accessible: true, description, created: true };
      }
    } catch (error) {
      allAccessible = false;
      details[checkPath] = { 
        accessible: false, 
        description, 
        error: error.message 
      };
    }
  });
  
  if (allAccessible) {
    addCheck(
      'File System Access',
      'PASS',
      'All critical directories accessible',
      details
    );
  } else {
    addCheck(
      'File System Access',
      'FAIL',
      'Some critical directories not accessible',
      details
    );
  }
}

function checkEnvironmentConfiguration() {
  log('Checking environment configuration...', 'INFO');
  
  const requiredEnvVars = [
    'PORT',
    'SESSION_SECRET',
    'BASE_URL'
  ];
  
  const optionalEnvVars = [
    'NODE_ENV',
    'LOG_LEVEL',
    'MIDTRANS_SERVER_KEY',
    'MIDTRANS_CLIENT_KEY'
  ];
  
  const missing = [];
  const present = [];
  const optional = [];
  
  requiredEnvVars.forEach(varName => {
    if (process.env[varName]) {
      present.push(varName);
    } else {
      missing.push(varName);
    }
  });
  
  optionalEnvVars.forEach(varName => {
    if (process.env[varName]) {
      optional.push(varName);
    }
  });
  
  if (missing.length === 0) {
    addCheck(
      'Environment Configuration',
      'PASS',
      `All required environment variables present (${present.length} required, ${optional.length} optional)`,
      { required: present, optional, missing }
    );
  } else {
    addCheck(
      'Environment Configuration',
      'FAIL',
      `Missing required environment variables: ${missing.join(', ')}`,
      { required: present, optional, missing }
    );
  }
}

function checkServiceDependencies() {
  log('Checking service dependencies...', 'INFO');
  
  const services = [
    { name: 'FFmpeg', check: () => {
      try {
        const { execSync } = require('child_process');
        execSync('ffmpeg -version', { stdio: 'pipe' });
        return { available: true };
      } catch (error) {
        return { available: false, error: error.message };
      }
    }},
    { name: 'Node.js Version', check: () => {
      const version = process.version;
      const majorVersion = parseInt(version.slice(1).split('.')[0]);
      return {
        available: majorVersion >= 14,
        version,
        supported: majorVersion >= 14
      };
    }}
  ];
  
  const serviceResults = {};
  let allAvailable = true;
  
  services.forEach(service => {
    try {
      const result = service.check();
      serviceResults[service.name] = result;
      
      if (!result.available) {
        allAvailable = false;
      }
    } catch (error) {
      serviceResults[service.name] = {
        available: false,
        error: error.message
      };
      allAvailable = false;
    }
  });
  
  if (allAvailable) {
    addCheck(
      'Service Dependencies',
      'PASS',
      'All required services available',
      serviceResults
    );
  } else {
    addCheck(
      'Service Dependencies',
      'WARNING',
      'Some services may not be available',
      serviceResults
    );
  }
}

function checkPerformanceMetrics() {
  log('Checking performance metrics...', 'INFO');
  
  const os = require('os');
  
  const totalMem = os.totalmem();
  const freeMem = os.freemem();
  const usedMem = totalMem - freeMem;
  const memoryUsage = (usedMem / totalMem) * 100;
  
  const cpus = os.cpus();
  const loadAvg = os.loadavg();
  
  const metrics = {
    memory: {
      total: Math.round(totalMem / 1024 / 1024 / 1024 * 100) / 100,
      used: Math.round(usedMem / 1024 / 1024 / 1024 * 100) / 100,
      percentage: Math.round(memoryUsage * 100) / 100
    },
    cpu: {
      cores: cpus.length,
      model: cpus[0].model,
      loadAverage: loadAvg.map(l => Math.round(l * 100) / 100)
    },
    uptime: Math.round(os.uptime())
  };
  
  let status = 'PASS';
  let message = 'System performance within normal ranges';
  
  if (memoryUsage > 90) {
    status = 'FAIL';
    message = `Critical memory usage: ${metrics.memory.percentage}%`;
  } else if (memoryUsage > 80) {
    status = 'WARNING';
    message = `High memory usage: ${metrics.memory.percentage}%`;
  }
  
  addCheck(
    'Performance Metrics',
    status,
    message,
    metrics
  );
}

function generateSummary() {
  const { total, passed, failed, warnings } = results.summary;
  
  if (failed > 0) {
    results.overall = 'UNHEALTHY';
  } else if (warnings > 0) {
    results.overall = 'WARNING';
  } else {
    results.overall = 'HEALTHY';
  }
  
  log('\n📊 HEALTH CHECK SUMMARY', 'INFO');
  log('=' .repeat(50), 'INFO');
  log(`Overall Status: ${results.overall}`, 'INFO');
  log(`Total Checks: ${total}`, 'INFO');
  log(`✅ Passed: ${passed}`, 'INFO');
  log(`❌ Failed: ${failed}`, 'INFO');
  log(`⚠️  Warnings: ${warnings}`, 'INFO');
  
  if (failed > 0) {
    log('\n🚨 CRITICAL ISSUES:', 'ERROR');
    results.checks
      .filter(check => check.status === 'FAIL')
      .forEach(check => {
        log(`   ❌ ${check.name}: ${check.message}`, 'ERROR');
      });
  }
  
  if (warnings > 0) {
    log('\n⚠️  WARNINGS:', 'WARNING');
    results.checks
      .filter(check => check.status === 'WARNING')
      .forEach(check => {
        log(`   ⚠️  ${check.name}: ${check.message}`, 'WARNING');
      });
  }
  
  // Save results
  const resultsDir = path.dirname(CONFIG.RESULTS_FILE);
  if (!fs.existsSync(resultsDir)) {
    fs.mkdirSync(resultsDir, { recursive: true });
  }
  
  fs.writeFileSync(CONFIG.RESULTS_FILE, JSON.stringify(results, null, 2));
  log(`\n📄 Results saved to: ${CONFIG.RESULTS_FILE}`, 'INFO');
}

// Main health check function
async function main() {
  log('Starting comprehensive health check...', 'INFO');
  
  try {
    // Run all health checks
    await checkApplicationResponsiveness();
    await checkDatabaseConnectivity();
    checkFileSystemAccess();
    checkEnvironmentConfiguration();
    checkServiceDependencies();
    checkPerformanceMetrics();
    
    // Generate summary
    generateSummary();
    
    // Exit with appropriate code
    const exitCode = results.overall === 'UNHEALTHY' ? 1 : 0;
    process.exit(exitCode);
    
  } catch (error) {
    log(`Health check failed: ${error.message}`, 'ERROR');
    process.exit(1);
  }
}

// Run health check if executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Health check error:', error);
    process.exit(1);
  });
}

module.exports = { main, checkApplicationResponsiveness, checkDatabaseConnectivity };
