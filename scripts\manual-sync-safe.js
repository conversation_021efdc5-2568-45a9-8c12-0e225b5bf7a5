#!/usr/bin/env node

/**
 * Safe Manual Stream Status Sync
 * 
 * This script provides safe manual sync without aggressive automatic cleanup
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

const Stream = require('../models/Stream');
const streamingService = require('../services/streamingService');

async function safeSync() {
  console.log('🔧 Safe Manual Stream Status Sync');
  console.log('==================================\n');

  try {
    // 1. Get current state
    console.log('1. Getting current state...');
    const activeStreamsInMemory = streamingService.getActiveStreams();
    const liveStreamsInDB = await Stream.findAll(null, 'live');
    const allStreams = await Stream.findAll();

    console.log(`   📊 Active streams in memory: ${activeStreamsInMemory.length}`);
    console.log(`   📊 Live streams in database: ${liveStreamsInDB.length}`);
    console.log(`   📊 Total streams in database: ${allStreams.length}`);

    if (activeStreamsInMemory.length > 0) {
      console.log(`   🔗 Active stream IDs: ${activeStreamsInMemory.join(', ')}`);
    }

    if (liveStreamsInDB.length > 0) {
      console.log(`   🔗 Live stream IDs: ${liveStreamsInDB.map(s => s.id).join(', ')}`);
    }

    console.log('');

    // 2. Check for obvious inconsistencies (but don't auto-fix)
    console.log('2. Checking for inconsistencies...');
    const inconsistencies = [];

    // Check streams marked live in DB but not in memory
    for (const stream of liveStreamsInDB) {
      const isInMemory = activeStreamsInMemory.includes(stream.id);
      if (!isInMemory) {
        inconsistencies.push({
          streamId: stream.id,
          issue: 'marked_live_but_not_in_memory',
          title: stream.title,
          recommendation: 'Consider updating to offline if FFmpeg is not running'
        });
        console.log(`   ⚠️  Stream ${stream.id} (${stream.title}): marked live in DB but not in memory`);
      }
    }

    // Check streams in memory but not marked live in DB
    for (const streamId of activeStreamsInMemory) {
      const stream = liveStreamsInDB.find(s => s.id === streamId);
      if (!stream) {
        const dbStream = allStreams.find(s => s.id === streamId);
        inconsistencies.push({
          streamId: streamId,
          issue: 'in_memory_but_not_live_in_db',
          title: dbStream ? dbStream.title : 'Unknown',
          dbStatus: dbStream ? dbStream.status : 'not_found',
          recommendation: 'Consider updating to live if FFmpeg is running'
        });
        console.log(`   ⚠️  Stream ${streamId}: in memory but not marked live in DB (status: ${dbStream ? dbStream.status : 'not found'})`);
      }
    }

    if (inconsistencies.length === 0) {
      console.log('   ✅ No inconsistencies found!');
    } else {
      console.log(`   ❌ Found ${inconsistencies.length} inconsistencies`);
    }

    console.log('');

    // 3. Show recommendations
    if (inconsistencies.length > 0) {
      console.log('3. Recommendations:');
      console.log('==================');
      
      inconsistencies.forEach((inc, index) => {
        console.log(`${index + 1}. Stream ${inc.streamId} (${inc.title})`);
        console.log(`   Issue: ${inc.issue}`);
        console.log(`   Recommendation: ${inc.recommendation}`);
        console.log('');
      });

      console.log('Manual Actions:');
      console.log('- Check if FFmpeg processes are actually running: tasklist | findstr ffmpeg');
      console.log('- Use admin panel to manually update status if needed');
      console.log('- Restart streams that should be live but are not in memory');
      console.log('');
    }

    // 4. Show current FFmpeg processes
    console.log('4. Current FFmpeg processes:');
    console.log('============================');
    
    const { exec } = require('child_process');
    const os = require('os');

    return new Promise((resolve) => {
      if (os.platform() === 'win32') {
        exec('tasklist /FI "IMAGENAME eq ffmpeg.exe" /FO CSV', (error, stdout) => {
          if (!error && stdout) {
            const lines = stdout.split('\n');
            const ffmpegProcesses = lines.filter(line => line.includes('ffmpeg.exe'));
            
            if (ffmpegProcesses.length > 1) {
              console.log(`   Found ${ffmpegProcesses.length - 1} FFmpeg processes:`);
              ffmpegProcesses.slice(1).forEach((line, index) => {
                console.log(`   ${index + 1}. ${line.trim()}`);
              });
            } else {
              console.log('   No FFmpeg processes found');
            }
          } else {
            console.log('   Could not check FFmpeg processes');
          }
          
          console.log('');
          console.log('✅ Safe sync completed - no automatic changes made');
          console.log('   Use the recommendations above to manually fix any issues');
          resolve();
        });
      } else {
        exec('pgrep -f ffmpeg', (error, stdout) => {
          if (!error && stdout) {
            const pids = stdout.trim().split('\n').filter(pid => pid);
            if (pids.length > 0) {
              console.log(`   Found ${pids.length} FFmpeg processes:`);
              pids.forEach((pid, index) => {
                console.log(`   ${index + 1}. PID: ${pid}`);
              });
            } else {
              console.log('   No FFmpeg processes found');
            }
          } else {
            console.log('   Could not check FFmpeg processes');
          }
          
          console.log('');
          console.log('✅ Safe sync completed - no automatic changes made');
          console.log('   Use the recommendations above to manually fix any issues');
          resolve();
        });
      }
    });

  } catch (error) {
    console.error('❌ Error during safe sync:', error);
  }
}

async function fixSpecificStream(streamId, action) {
  console.log(`🔧 Fixing stream ${streamId} with action: ${action}`);
  
  try {
    const stream = await Stream.findById(streamId);
    if (!stream) {
      console.log(`❌ Stream ${streamId} not found`);
      return;
    }

    const isInMemory = streamingService.isStreamActive(streamId);
    
    console.log(`📊 Current state:`);
    console.log(`   Database status: ${stream.status}`);
    console.log(`   In memory: ${isInMemory}`);
    
    switch (action) {
      case 'set-live':
        if (isInMemory) {
          await Stream.updateStatus(streamId, 'live', stream.user_id);
          console.log(`✅ Updated stream ${streamId} to live`);
        } else {
          console.log(`⚠️  Cannot set to live - stream not in memory`);
        }
        break;
        
      case 'set-offline':
        await Stream.updateStatus(streamId, 'offline', stream.user_id);
        console.log(`✅ Updated stream ${streamId} to offline`);
        break;
        
      case 'cleanup':
        if (isInMemory) {
          streamingService.cleanupStreamData(streamId);
          console.log(`✅ Cleaned up stream ${streamId} from memory`);
        }
        await Stream.updateStatus(streamId, 'offline', stream.user_id);
        console.log(`✅ Updated stream ${streamId} to offline`);
        break;
        
      default:
        console.log(`❌ Unknown action: ${action}`);
        console.log(`Available actions: set-live, set-offline, cleanup`);
    }
    
  } catch (error) {
    console.error(`❌ Error fixing stream ${streamId}:`, error);
  }
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    await safeSync();
  } else if (args.length === 2) {
    const [streamId, action] = args;
    await fixSpecificStream(streamId, action);
  } else {
    console.log('Usage:');
    console.log('  node manual-sync-safe.js                    - Run safe sync check');
    console.log('  node manual-sync-safe.js <stream-id> <action> - Fix specific stream');
    console.log('');
    console.log('Actions:');
    console.log('  set-live    - Update status to live (only if in memory)');
    console.log('  set-offline - Update status to offline');
    console.log('  cleanup     - Remove from memory and set offline');
  }
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { safeSync, fixSpecificStream };
