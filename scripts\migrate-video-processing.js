#!/usr/bin/env node

/**
 * Migration script for video processing features
 * This script adds the necessary database columns and initializes existing videos
 */

const { db } = require('../db/database');
const Video = require('../models/Video');

async function runMigration() {
  console.log('🚀 Starting video processing migration...');

  try {
    // Add processing status column
    await new Promise((resolve, reject) => {
      db.run(`ALTER TABLE videos ADD COLUMN processing_status TEXT DEFAULT 'pending'`, (err) => {
        if (err && !err.message.includes('duplicate column name')) {
          console.error('Error adding processing_status column:', err.message);
          return reject(err);
        }
        console.log('✅ Added processing_status column');
        resolve();
      });
    });

    // Add streaming ready path column
    await new Promise((resolve, reject) => {
      db.run(`ALTER TABLE videos ADD COLUMN streaming_ready_path TEXT`, (err) => {
        if (err && !err.message.includes('duplicate column name')) {
          console.error('Error adding streaming_ready_path column:', err.message);
          return reject(err);
        }
        console.log('✅ Added streaming_ready_path column');
        resolve();
      });
    });

    // Add original filepath column
    await new Promise((resolve, reject) => {
      db.run(`ALTER TABLE videos ADD COLUMN original_filepath TEXT`, (err) => {
        if (err && !err.message.includes('duplicate column name')) {
          console.error('Error adding original_filepath column:', err.message);
          return reject(err);
        }
        console.log('✅ Added original_filepath column');
        resolve();
      });
    });

    // Create indexes for better performance
    await new Promise((resolve, reject) => {
      db.run(`CREATE INDEX IF NOT EXISTS idx_videos_processing_status ON videos(processing_status)`, (err) => {
        if (err) {
          console.error('Error creating processing_status index:', err.message);
          return reject(err);
        }
        console.log('✅ Created processing_status index');
        resolve();
      });
    });

    await new Promise((resolve, reject) => {
      db.run(`CREATE INDEX IF NOT EXISTS idx_videos_created_at ON videos(created_at)`, (err) => {
        if (err) {
          console.error('Error creating created_at index:', err.message);
          return reject(err);
        }
        console.log('✅ Created created_at index');
        resolve();
      });
    });

    // Update existing videos based on their codec compatibility
    console.log('📊 Analyzing existing videos...');

    // Get all existing videos
    const videos = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM videos', [], (err, rows) => {
        if (err) {
          return reject(err);
        }
        resolve(rows || []);
      });
    });

    console.log(`Found ${videos.length} existing videos`);

    let completedCount = 0;
    let pendingCount = 0;

    for (const video of videos) {
      let status = 'pending';
      
      // Check if video is already streaming-ready
      if (video.codec) {
        const codecLower = video.codec.toLowerCase();
        
        // H.264 videos are already streaming-ready
        if (codecLower.includes('h264') || codecLower.includes('avc')) {
          // Check if it's not MKV and has reasonable parameters
          if (video.format !== 'mkv' && 
              (!video.bitrate || video.bitrate <= 5000) &&
              (!video.resolution || !video.resolution.includes('x') || 
               (parseInt(video.resolution.split('x')[0]) <= 1920 && 
                parseInt(video.resolution.split('x')[1]) <= 1080))) {
            status = 'completed';
            completedCount++;
          } else {
            pendingCount++;
          }
        } else {
          // Non-H.264 codecs need processing
          pendingCount++;
        }
      } else {
        // Unknown codec, assume needs processing
        pendingCount++;
      }

      // Update video status
      await new Promise((resolve, reject) => {
        db.run(
          'UPDATE videos SET processing_status = ? WHERE id = ?',
          [status, video.id],
          (err) => {
            if (err) {
              console.error(`Error updating video ${video.id}:`, err.message);
              return reject(err);
            }
            resolve();
          }
        );
      });
    }

    console.log(`✅ Migration completed successfully!`);
    console.log(`📈 Statistics:`);
    console.log(`   - ${completedCount} videos marked as streaming-ready`);
    console.log(`   - ${pendingCount} videos marked for processing`);
    console.log(`   - Total videos: ${videos.length}`);

    if (pendingCount > 0) {
      console.log('');
      console.log('🔄 Next steps:');
      console.log('   1. Start your application');
      console.log('   2. Videos marked as "pending" will be automatically processed');
      console.log('   3. Monitor processing status in the gallery');
      console.log('   4. Check /api/video-processing/status for queue status');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  runMigration()
    .then(() => {
      console.log('🎉 Migration completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { runMigration };
