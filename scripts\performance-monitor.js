#!/usr/bin/env node

/**
 * Enhanced Performance Monitor for StreamOnPod
 * 
 * Features:
 * 1. Real-time system metrics monitoring
 * 2. Application health checks
 * 3. Automated alerting system
 * 4. Performance trend analysis
 * 5. Resource usage optimization suggestions
 */

const os = require('os');
const fs = require('fs');
const path = require('path');
const http = require('http');
const https = require('https');

console.log('📊 StreamOnPod Enhanced Performance Monitor\n');

// Configuration
const CONFIG = {
  MONITOR_INTERVAL: 15000,     // 15 seconds
  ALERT_INTERVAL: 60000,       // 1 minute
  METRICS_RETENTION: 1440,     // 24 hours (in minutes)
  LOG_FILE: './logs/performance.log',
  METRICS_FILE: './logs/performance-metrics.json',
  ALERTS_FILE: './logs/performance-alerts.json',
  BASE_URL: process.env.BASE_URL || 'http://localhost:7575',
  
  // Alert thresholds
  THRESHOLDS: {
    CPU_WARNING: 70,
    CPU_CRITICAL: 85,
    MEMORY_WARNING: 75,
    MEMORY_CRITICAL: 90,
    RESPONSE_TIME_WARNING: 3000,
    RESPONSE_TIME_CRITICAL: 5000,
    ERROR_RATE_WARNING: 3,
    ERROR_RATE_CRITICAL: 5,
    DISK_WARNING: 80,
    DISK_CRITICAL: 95
  }
};

// Monitoring state
const state = {
  startTime: Date.now(),
  metrics: {
    cpu: [],
    memory: [],
    disk: [],
    network: [],
    application: [],
    errors: []
  },
  alerts: [],
  lastAlert: {},
  isRunning: true
};

// Utility functions
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] [${level}] ${message}`;
  
  console.log(logEntry);
  
  // Ensure log directory exists
  const logDir = path.dirname(CONFIG.LOG_FILE);
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
  
  // Write to log file
  fs.appendFileSync(CONFIG.LOG_FILE, logEntry + '\n');
}

function getSystemMetrics() {
  const cpus = os.cpus();
  const totalMem = os.totalmem();
  const freeMem = os.freemem();
  const usedMem = totalMem - freeMem;
  
  // Calculate CPU usage
  let totalIdle = 0;
  let totalTick = 0;
  
  cpus.forEach(cpu => {
    for (const type in cpu.times) {
      totalTick += cpu.times[type];
    }
    totalIdle += cpu.times.idle;
  });
  
  const idle = totalIdle / cpus.length;
  const total = totalTick / cpus.length;
  const cpuUsage = 100 - ~~(100 * idle / total);
  
  // Memory usage
  const memoryUsage = (usedMem / totalMem) * 100;
  
  // Disk usage (simplified)
  let diskUsage = 0;
  try {
    const stats = fs.statSync('./');
    diskUsage = 50; // Placeholder - would need platform-specific implementation
  } catch (error) {
    diskUsage = 0;
  }
  
  return {
    timestamp: Date.now(),
    cpu: {
      usage: cpuUsage,
      cores: cpus.length,
      model: cpus[0].model
    },
    memory: {
      total: totalMem,
      used: usedMem,
      free: freeMem,
      percentage: memoryUsage
    },
    disk: {
      usage: diskUsage
    },
    uptime: os.uptime(),
    loadAverage: os.loadavg()
  };
}

async function checkApplicationHealth() {
  const startTime = Date.now();
  
  return new Promise((resolve) => {
    const protocol = CONFIG.BASE_URL.startsWith('https') ? https : http;
    const url = new URL(CONFIG.BASE_URL);
    
    const req = protocol.request({
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 80),
      path: '/health',
      method: 'GET',
      timeout: 5000
    }, (res) => {
      const responseTime = Date.now() - startTime;
      
      resolve({
        healthy: res.statusCode === 200,
        statusCode: res.statusCode,
        responseTime,
        timestamp: Date.now()
      });
    });
    
    req.on('error', (error) => {
      const responseTime = Date.now() - startTime;
      
      resolve({
        healthy: false,
        error: error.message,
        responseTime,
        timestamp: Date.now()
      });
    });
    
    req.on('timeout', () => {
      req.destroy();
      const responseTime = Date.now() - startTime;
      
      resolve({
        healthy: false,
        error: 'Request timeout',
        responseTime,
        timestamp: Date.now()
      });
    });
    
    req.end();
  });
}

function generateAlerts(systemMetrics, healthCheck) {
  const alerts = [];
  const now = Date.now();
  
  // CPU alerts
  if (systemMetrics.cpu.usage > CONFIG.THRESHOLDS.CPU_CRITICAL) {
    alerts.push({
      type: 'CPU_CRITICAL',
      severity: 'CRITICAL',
      message: `Critical CPU usage: ${systemMetrics.cpu.usage.toFixed(1)}%`,
      value: systemMetrics.cpu.usage,
      threshold: CONFIG.THRESHOLDS.CPU_CRITICAL,
      timestamp: now
    });
  } else if (systemMetrics.cpu.usage > CONFIG.THRESHOLDS.CPU_WARNING) {
    alerts.push({
      type: 'CPU_WARNING',
      severity: 'WARNING',
      message: `High CPU usage: ${systemMetrics.cpu.usage.toFixed(1)}%`,
      value: systemMetrics.cpu.usage,
      threshold: CONFIG.THRESHOLDS.CPU_WARNING,
      timestamp: now
    });
  }
  
  // Memory alerts
  if (systemMetrics.memory.percentage > CONFIG.THRESHOLDS.MEMORY_CRITICAL) {
    alerts.push({
      type: 'MEMORY_CRITICAL',
      severity: 'CRITICAL',
      message: `Critical memory usage: ${systemMetrics.memory.percentage.toFixed(1)}%`,
      value: systemMetrics.memory.percentage,
      threshold: CONFIG.THRESHOLDS.MEMORY_CRITICAL,
      timestamp: now
    });
  } else if (systemMetrics.memory.percentage > CONFIG.THRESHOLDS.MEMORY_WARNING) {
    alerts.push({
      type: 'MEMORY_WARNING',
      severity: 'WARNING',
      message: `High memory usage: ${systemMetrics.memory.percentage.toFixed(1)}%`,
      value: systemMetrics.memory.percentage,
      threshold: CONFIG.THRESHOLDS.MEMORY_WARNING,
      timestamp: now
    });
  }
  
  // Application health alerts
  if (!healthCheck.healthy) {
    alerts.push({
      type: 'APP_UNHEALTHY',
      severity: 'CRITICAL',
      message: `Application health check failed: ${healthCheck.error || 'Status ' + healthCheck.statusCode}`,
      responseTime: healthCheck.responseTime,
      timestamp: now
    });
  } else if (healthCheck.responseTime > CONFIG.THRESHOLDS.RESPONSE_TIME_CRITICAL) {
    alerts.push({
      type: 'RESPONSE_TIME_CRITICAL',
      severity: 'CRITICAL',
      message: `Critical response time: ${healthCheck.responseTime}ms`,
      value: healthCheck.responseTime,
      threshold: CONFIG.THRESHOLDS.RESPONSE_TIME_CRITICAL,
      timestamp: now
    });
  } else if (healthCheck.responseTime > CONFIG.THRESHOLDS.RESPONSE_TIME_WARNING) {
    alerts.push({
      type: 'RESPONSE_TIME_WARNING',
      severity: 'WARNING',
      message: `Slow response time: ${healthCheck.responseTime}ms`,
      value: healthCheck.responseTime,
      threshold: CONFIG.THRESHOLDS.RESPONSE_TIME_WARNING,
      timestamp: now
    });
  }
  
  return alerts;
}

function saveMetrics(systemMetrics, healthCheck, alerts) {
  // Add to state
  state.metrics.cpu.push({
    timestamp: systemMetrics.timestamp,
    usage: systemMetrics.cpu.usage
  });
  
  state.metrics.memory.push({
    timestamp: systemMetrics.timestamp,
    percentage: systemMetrics.memory.percentage,
    used: systemMetrics.memory.used
  });
  
  state.metrics.application.push({
    timestamp: healthCheck.timestamp,
    healthy: healthCheck.healthy,
    responseTime: healthCheck.responseTime
  });
  
  // Trim old data
  const cutoff = Date.now() - (CONFIG.METRICS_RETENTION * 60 * 1000);
  
  Object.keys(state.metrics).forEach(key => {
    state.metrics[key] = state.metrics[key].filter(metric => 
      metric.timestamp > cutoff
    );
  });
  
  // Save to file
  const metricsData = {
    timestamp: Date.now(),
    systemMetrics,
    healthCheck,
    alerts,
    historical: state.metrics
  };
  
  const metricsDir = path.dirname(CONFIG.METRICS_FILE);
  if (!fs.existsSync(metricsDir)) {
    fs.mkdirSync(metricsDir, { recursive: true });
  }
  
  fs.writeFileSync(CONFIG.METRICS_FILE, JSON.stringify(metricsData, null, 2));
}

function displayDashboard(systemMetrics, healthCheck, alerts) {
  console.clear();
  console.log('📊 StreamOnPod Performance Monitor Dashboard\n');
  
  // Header
  const uptime = Math.floor((Date.now() - state.startTime) / 1000);
  const uptimeStr = `${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m ${uptime % 60}s`;
  
  console.log(`🕐 Monitor Uptime: ${uptimeStr}`);
  console.log(`🌐 Application URL: ${CONFIG.BASE_URL}`);
  console.log(`📊 Metrics File: ${CONFIG.METRICS_FILE}\n`);
  
  // System metrics
  console.log('💻 SYSTEM METRICS');
  console.log('─'.repeat(40));
  console.log(`CPU Usage: ${systemMetrics.cpu.usage.toFixed(1)}% (${systemMetrics.cpu.cores} cores)`);
  console.log(`Memory: ${systemMetrics.memory.percentage.toFixed(1)}% (${(systemMetrics.memory.used / 1024 / 1024 / 1024).toFixed(2)}GB used)`);
  console.log(`Load Average: ${systemMetrics.loadAverage.map(l => l.toFixed(2)).join(', ')}`);
  console.log(`System Uptime: ${Math.floor(systemMetrics.uptime / 3600)}h ${Math.floor((systemMetrics.uptime % 3600) / 60)}m\n`);
  
  // Application health
  console.log('🏥 APPLICATION HEALTH');
  console.log('─'.repeat(40));
  console.log(`Status: ${healthCheck.healthy ? '✅ Healthy' : '❌ Unhealthy'}`);
  console.log(`Response Time: ${healthCheck.responseTime}ms`);
  if (healthCheck.error) {
    console.log(`Error: ${healthCheck.error}`);
  }
  console.log('');
  
  // Alerts
  if (alerts.length > 0) {
    console.log('🚨 ACTIVE ALERTS');
    console.log('─'.repeat(40));
    alerts.forEach(alert => {
      const icon = alert.severity === 'CRITICAL' ? '🔴' : '🟡';
      console.log(`${icon} [${alert.severity}] ${alert.message}`);
    });
    console.log('');
  }
  
  // Performance trends
  if (state.metrics.cpu.length > 1) {
    const recentCpu = state.metrics.cpu.slice(-10);
    const avgCpu = recentCpu.reduce((sum, m) => sum + m.usage, 0) / recentCpu.length;
    
    const recentMemory = state.metrics.memory.slice(-10);
    const avgMemory = recentMemory.reduce((sum, m) => sum + m.percentage, 0) / recentMemory.length;
    
    console.log('📈 PERFORMANCE TRENDS (Last 10 readings)');
    console.log('─'.repeat(40));
    console.log(`Average CPU: ${avgCpu.toFixed(1)}%`);
    console.log(`Average Memory: ${avgMemory.toFixed(1)}%`);
    console.log('');
  }
  
  console.log('Press Ctrl+C to stop monitoring...');
}

// Main monitoring loop
async function monitoringLoop() {
  while (state.isRunning) {
    try {
      // Get metrics
      const systemMetrics = getSystemMetrics();
      const healthCheck = await checkApplicationHealth();
      
      // Generate alerts
      const alerts = generateAlerts(systemMetrics, healthCheck);
      
      // Save metrics
      saveMetrics(systemMetrics, healthCheck, alerts);
      
      // Display dashboard
      displayDashboard(systemMetrics, healthCheck, alerts);
      
      // Log alerts
      alerts.forEach(alert => {
        log(`ALERT: ${alert.message}`, alert.severity);
      });
      
      // Wait for next iteration
      await new Promise(resolve => setTimeout(resolve, CONFIG.MONITOR_INTERVAL));
      
    } catch (error) {
      log(`Monitoring error: ${error.message}`, 'ERROR');
      await new Promise(resolve => setTimeout(resolve, CONFIG.MONITOR_INTERVAL));
    }
  }
}

// Graceful shutdown
process.on('SIGINT', () => {
  log('Shutting down performance monitor...', 'INFO');
  state.isRunning = false;
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('Shutting down performance monitor...', 'INFO');
  state.isRunning = false;
  process.exit(0);
});

// Start monitoring
if (require.main === module) {
  log('Starting enhanced performance monitoring...', 'INFO');
  monitoringLoop().catch(error => {
    console.error('Performance monitor error:', error);
    process.exit(1);
  });
}

module.exports = { getSystemMetrics, checkApplicationHealth, generateAlerts };
