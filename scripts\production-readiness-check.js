#!/usr/bin/env node

/**
 * StreamOnPod Production Readiness Checker
 * Comprehensive validation for production deployment
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 StreamOnPod Production Readiness Check\n');

// Load environment variables
require('dotenv').config();

const checks = [];

// Helper functions
function addCheck(name, condition, message, severity = 'error') {
  checks.push({
    name,
    passed: condition,
    message,
    severity
  });
}

function logResult(check) {
  const icon = check.passed ? '✅' : (check.severity === 'warning' ? '⚠️' : '❌');
  const status = check.passed ? 'PASS' : (check.severity === 'warning' ? 'WARN' : 'FAIL');
  console.log(`${icon} [${status}] ${check.name}: ${check.message}`);
}

// 1. Environment Configuration
console.log('1. 🌍 Environment Configuration:');

addCheck(
  'NODE_ENV',
  process.env.NODE_ENV === 'production',
  process.env.NODE_ENV === 'production' ? 'Set to production' : `Currently: ${process.env.NODE_ENV || 'not set'}`
);

addCheck(
  'PORT Configuration',
  process.env.PORT && !isNaN(process.env.PORT),
  process.env.PORT ? `Port ${process.env.PORT}` : 'Port not configured'
);

addCheck(
  'Session Secret',
  process.env.SESSION_SECRET && process.env.SESSION_SECRET.length >= 32,
  process.env.SESSION_SECRET ? 'Strong session secret configured' : 'Session secret missing or weak'
);

addCheck(
  'CSRF Secret',
  process.env.CSRF_SECRET && process.env.CSRF_SECRET.length >= 32,
  process.env.CSRF_SECRET ? 'CSRF secret configured' : 'CSRF secret missing'
);

// 2. Security Configuration
console.log('\n2. 🔒 Security Configuration:');

addCheck(
  'CSRF Protection',
  process.env.DISABLE_CSRF !== 'true',
  process.env.DISABLE_CSRF === 'true' ? 'CSRF disabled (INSECURE for production)' : 'CSRF protection enabled'
);

addCheck(
  'HTTPS Configuration',
  process.env.FORCE_HTTPS === 'true' || process.env.BASE_URL?.startsWith('https://'),
  process.env.FORCE_HTTPS === 'true' ? 'HTTPS enforced' : 'HTTPS not enforced'
);

addCheck(
  'Proxy Trust',
  process.env.TRUST_PROXY === 'true',
  process.env.TRUST_PROXY === 'true' ? 'Proxy trust enabled for tunnel' : 'Proxy trust disabled',
  'warning'
);

// 3. Database Configuration
console.log('\n3. 🗄️ Database Configuration:');

const dbPath = process.env.DATABASE_PATH || './db/streamonpod.db';
addCheck(
  'Database File',
  fs.existsSync(dbPath),
  fs.existsSync(dbPath) ? `Database exists at ${dbPath}` : `Database missing at ${dbPath}`
);

addCheck(
  'Database Logging',
  process.env.ENABLE_DATABASE_LOGGING !== 'true',
  process.env.ENABLE_DATABASE_LOGGING === 'true' ? 'Database logging enabled (may impact performance)' : 'Database logging disabled',
  'warning'
);

// 4. Logging Configuration
console.log('\n4. 📝 Logging Configuration:');

addCheck(
  'Log Level',
  ['error', 'warn'].includes(process.env.LOG_LEVEL),
  process.env.LOG_LEVEL ? `Log level: ${process.env.LOG_LEVEL}` : 'Log level not set'
);

addCheck(
  'Console Logging',
  process.env.ENABLE_CONSOLE_LOGGING !== 'true',
  process.env.ENABLE_CONSOLE_LOGGING === 'true' ? 'Console logging enabled (may impact performance)' : 'Console logging optimized',
  'warning'
);

addCheck(
  'File Logging',
  process.env.ENABLE_FILE_LOGGING === 'true',
  process.env.ENABLE_FILE_LOGGING === 'true' ? 'File logging enabled' : 'File logging disabled'
);

// 5. Performance Configuration
console.log('\n5. ⚡ Performance Configuration:');

addCheck(
  'Compression',
  process.env.ENABLE_COMPRESSION === 'true',
  process.env.ENABLE_COMPRESSION === 'true' ? 'Compression enabled' : 'Compression disabled'
);

addCheck(
  'Caching',
  process.env.ENABLE_CACHING === 'true',
  process.env.ENABLE_CACHING === 'true' ? 'Caching enabled' : 'Caching disabled'
);

addCheck(
  'Load Balancer',
  process.env.LOAD_BALANCER_ENABLED === 'true',
  process.env.LOAD_BALANCER_ENABLED === 'true' ? 'Load balancer enabled' : 'Load balancer disabled'
);

// 6. Payment Configuration
console.log('\n6. 💳 Payment Configuration:');

addCheck(
  'Midtrans Server Key',
  process.env.MIDTRANS_SERVER_KEY && process.env.MIDTRANS_SERVER_KEY.startsWith('Mid-server-'),
  process.env.MIDTRANS_SERVER_KEY ? 'Midtrans server key configured' : 'Midtrans server key missing'
);

addCheck(
  'Midtrans Client Key',
  process.env.MIDTRANS_CLIENT_KEY && process.env.MIDTRANS_CLIENT_KEY.startsWith('Mid-client-'),
  process.env.MIDTRANS_CLIENT_KEY ? 'Midtrans client key configured' : 'Midtrans client key missing'
);

addCheck(
  'Midtrans Production Mode',
  process.env.MIDTRANS_IS_PRODUCTION === 'true',
  process.env.MIDTRANS_IS_PRODUCTION === 'true' ? 'Midtrans in production mode' : 'Midtrans in sandbox mode',
  'warning'
);

// 7. File System Checks
console.log('\n7. 📁 File System Checks:');

const requiredDirs = [
  './db',
  './logs',
  './public/uploads',
  './public/uploads/videos',
  './public/uploads/avatars'
];

requiredDirs.forEach(dir => {
  addCheck(
    `Directory: ${dir}`,
    fs.existsSync(dir),
    fs.existsSync(dir) ? 'Exists' : 'Missing'
  );
});

// 8. Critical Files Check
console.log('\n8. 📄 Critical Files Check:');

const criticalFiles = [
  'app.js',
  'package.json',
  'utils/errorHandler.js',
  'services/performanceMonitor.js',
  'models/User.js',
  'models/Subscription.js'
];

criticalFiles.forEach(file => {
  addCheck(
    `File: ${file}`,
    fs.existsSync(file),
    fs.existsSync(file) ? 'Exists' : 'Missing'
  );
});

// Display all results
checks.forEach(logResult);

// Summary
console.log('\n📊 Summary:');
const passed = checks.filter(c => c.passed).length;
const failed = checks.filter(c => !c.passed && c.severity === 'error').length;
const warnings = checks.filter(c => !c.passed && c.severity === 'warning').length;

console.log(`✅ Passed: ${passed}`);
console.log(`❌ Failed: ${failed}`);
console.log(`⚠️  Warnings: ${warnings}`);

// Final verdict
if (failed === 0) {
  console.log('\n🎉 Production Ready! All critical checks passed.');
  if (warnings > 0) {
    console.log('⚠️  Consider addressing warnings for optimal performance.');
  }
  process.exit(0);
} else {
  console.log('\n🚨 NOT Production Ready! Please fix the failed checks before deploying.');
  process.exit(1);
}
