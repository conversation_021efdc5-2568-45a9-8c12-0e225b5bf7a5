#!/usr/bin/env node

/**
 * StreamOnPod Production Startup Script
 * Optimized for production environment with minimal logging
 */

const fs = require('fs');
const path = require('path');

// Set production environment
process.env.NODE_ENV = 'production';

// Load production environment variables
const envPath = path.join(__dirname, '..', '.env.production');
if (fs.existsSync(envPath)) {
  require('dotenv').config({ path: envPath });
  console.log('✅ Production environment loaded');
} else {
  console.warn('⚠️  Production .env file not found, using defaults');
}

// Production logging configuration
process.env.LOG_LEVEL = process.env.LOG_LEVEL || 'error';
process.env.ENABLE_CONSOLE_LOGGING = process.env.ENABLE_CONSOLE_LOGGING || 'false';
process.env.ENABLE_VERBOSE_FFMPEG_LOGS = 'false';

// Initialize logger first (before any other modules)
require('../services/logger');

// Graceful shutdown handling
process.on('SIGTERM', () => {
  console.error('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.error('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Handle uncaught exceptions in production
process.on('uncaughtException', (error) => {
  console.error('UNCAUGHT EXCEPTION:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('UNHANDLED REJECTION at:', promise);
  console.error('Reason:', reason);
  process.exit(1);
});

// Production optimizations
if (process.env.ENABLE_COMPRESSION === 'true') {
  process.env.COMPRESSION_ENABLED = 'true';
}

// Memory monitoring for production
const monitorMemory = () => {
  const memUsage = process.memoryUsage();
  const memThreshold = parseInt(process.env.MEMORY_THRESHOLD) || 90;
  const memPercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
  
  if (memPercent > memThreshold) {
    console.error(`⚠️  High memory usage: ${memPercent.toFixed(2)}%`);
    
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
      console.error('🗑️  Forced garbage collection');
    }
  }
};

// Monitor memory every 5 minutes in production
setInterval(monitorMemory, 5 * 60 * 1000);

// Start the application
console.log('🚀 Starting StreamOnPod in production mode...');
require('../app.js');
