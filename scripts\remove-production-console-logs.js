#!/usr/bin/env node

/**
 * Remove Console Logs for Production
 * Removes or comments out console.log statements from production code
 */

const fs = require('fs');
const path = require('path');

console.log('🧹 Starting production console log cleanup...\n');

// Files to process (exclude scripts directory as those are tools)
const filesToProcess = [
  'app.js',
  'public/js/stream-modal.js',
  'public/js/notifications.js',
  'public/js/landing.js',
  'public/js/lazy-loading.js'
];

// Directories to process recursively
const directoriesToProcess = [
  'views',
  'models',
  'middleware',
  'services',
  'utils'
];

// Patterns to match console statements (more precise regex)
const consolePatterns = [
  /^(\s*)console\.log\([^)]*\);?\s*$/gm,
  /^(\s*)console\.debug\([^)]*\);?\s*$/gm,
  /^(\s*)console\.info\([^)]*\);?\s*$/gm,
  // Keep console.error and console.warn for production debugging
];

// Patterns to exclude (keep these console statements)
const excludePatterns = [
  /console\.error/,
  /console\.warn/,
  /\/\/ console\./, // Already commented
  /\/\* console\./, // Already commented
  /Removed for production/, // Already processed
];

let totalFilesProcessed = 0;
let totalLogsRemoved = 0;

function shouldExcludeFile(filePath) {
  // Exclude scripts directory (tools)
  if (filePath.includes('scripts/')) return true;
  
  // Exclude backup files
  if (filePath.includes('.backup')) return true;
  
  // Exclude node_modules
  if (filePath.includes('node_modules')) return true;
  
  // Exclude compressed files
  if (filePath.includes('.min.') || filePath.includes('.gz')) return true;
  
  return false;
}

function processFile(filePath) {
  if (shouldExcludeFile(filePath)) {
    return;
  }

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let modifiedContent = content;
    let logsRemovedInFile = 0;

    // Process each console pattern
    consolePatterns.forEach(pattern => {
      modifiedContent = modifiedContent.replace(pattern, (match, indent) => {
        // Check if this match should be excluded
        const shouldExclude = excludePatterns.some(excludePattern => 
          excludePattern.test(match)
        );

        if (!shouldExclude) {
          logsRemovedInFile++;
          // Comment out the console statement with proper indentation
          return `${indent}// ${match.trim()} // Removed for production`;
        }
        
        return match;
      });
    });

    // Write back if changes were made
    if (logsRemovedInFile > 0) {
      // Create backup first
      const backupPath = `${filePath}.backup.console-clean.${Date.now()}`;
      fs.copyFileSync(filePath, backupPath);
      
      fs.writeFileSync(filePath, modifiedContent);
      console.log(`✅ ${filePath}: ${logsRemovedInFile} console logs removed (backup: ${path.basename(backupPath)})`);
      
      totalFilesProcessed++;
      totalLogsRemoved += logsRemovedInFile;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
}

function processDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    return;
  }

  const items = fs.readdirSync(dirPath);
  
  items.forEach(item => {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);
    
    if (stat.isDirectory()) {
      processDirectory(itemPath);
    } else if (stat.isFile()) {
      // Process JavaScript and EJS files
      if (item.endsWith('.js') || item.endsWith('.ejs')) {
        processFile(itemPath);
      }
    }
  });
}

// Process individual files
console.log('📁 Processing individual files...');
filesToProcess.forEach(file => {
  if (fs.existsSync(file)) {
    processFile(file);
  } else {
    console.log(`⚠️  File not found: ${file}`);
  }
});

// Process directories
console.log('\n📂 Processing directories...');
directoriesToProcess.forEach(dir => {
  console.log(`\n📁 Processing directory: ${dir}`);
  processDirectory(dir);
});

console.log('\n🎉 Console log cleanup completed!');
console.log(`📊 Summary:`);
console.log(`   - Files processed: ${totalFilesProcessed}`);
console.log(`   - Console logs removed: ${totalLogsRemoved}`);
console.log(`   - Backups created for all modified files`);

if (totalLogsRemoved > 0) {
  console.log('\n💡 Note: Console logs were commented out rather than deleted.');
  console.log('   You can restore them by removing the "// " prefix and "// Removed for production" suffix.');
  console.log('\n🔄 To restore all console logs, run: npm run restore:console-logs');
}
