#!/usr/bin/env node

/**
 * Restore Console Logs
 * Restores console.log statements that were commented out for production
 */

const fs = require('fs');
const path = require('path');

console.log('🔄 Starting console log restoration...\n');

// Files to process
const filesToProcess = [
  'app.js',
  'public/js/stream-modal.js',
  'public/js/notifications.js',
  'public/js/landing.js',
  'public/js/lazy-loading.js'
];

// Directories to process recursively
const directoriesToProcess = [
  'views',
  'models',
  'middleware',
  'services',
  'utils'
];

// Pattern to match commented console statements
const commentedConsolePattern = /^(\s*)\/\/ (console\.(log|debug|info)\([^)]*\);?) \/\/ Removed for production\s*$/gm;

let totalFilesProcessed = 0;
let totalLogsRestored = 0;

function shouldExcludeFile(filePath) {
  // Exclude scripts directory (tools)
  if (filePath.includes('scripts/')) return true;
  
  // Exclude backup files
  if (filePath.includes('.backup')) return true;
  
  // Exclude node_modules
  if (filePath.includes('node_modules')) return true;
  
  // Exclude compressed files
  if (filePath.includes('.min.') || filePath.includes('.gz')) return true;
  
  return false;
}

function processFile(filePath) {
  if (shouldExcludeFile(filePath)) {
    return;
  }

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let modifiedContent = content;
    let logsRestoredInFile = 0;

    // Restore commented console statements
    modifiedContent = modifiedContent.replace(commentedConsolePattern, (match, indent, consoleStatement) => {
      logsRestoredInFile++;
      // Restore the console statement with proper indentation
      return `${indent}${consoleStatement}`;
    });

    // Write back if changes were made
    if (logsRestoredInFile > 0) {
      // Create backup first
      const backupPath = `${filePath}.backup.console-restore.${Date.now()}`;
      fs.copyFileSync(filePath, backupPath);
      
      fs.writeFileSync(filePath, modifiedContent);
      console.log(`✅ ${filePath}: ${logsRestoredInFile} console logs restored (backup: ${path.basename(backupPath)})`);
      
      totalFilesProcessed++;
      totalLogsRestored += logsRestoredInFile;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
}

function processDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    return;
  }

  const items = fs.readdirSync(dirPath);
  
  items.forEach(item => {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);
    
    if (stat.isDirectory()) {
      processDirectory(itemPath);
    } else if (stat.isFile()) {
      // Process JavaScript and EJS files
      if (item.endsWith('.js') || item.endsWith('.ejs')) {
        processFile(itemPath);
      }
    }
  });
}

// Process individual files
console.log('📁 Processing individual files...');
filesToProcess.forEach(file => {
  if (fs.existsSync(file)) {
    processFile(file);
  } else {
    console.log(`⚠️  File not found: ${file}`);
  }
});

// Process directories
console.log('\n📂 Processing directories...');
directoriesToProcess.forEach(dir => {
  console.log(`\n📁 Processing directory: ${dir}`);
  processDirectory(dir);
});

console.log('\n🎉 Console log restoration completed!');
console.log(`📊 Summary:`);
console.log(`   - Files processed: ${totalFilesProcessed}`);
console.log(`   - Console logs restored: ${totalLogsRestored}`);
console.log(`   - Backups created for all modified files`);

if (totalLogsRestored === 0) {
  console.log('\n💡 No commented console logs found to restore.');
  console.log('   This might mean they were already restored or never removed.');
}
