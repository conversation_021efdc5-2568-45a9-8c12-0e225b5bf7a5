#!/usr/bin/env node

/**
 * Midtrans Mode Switcher
 * Helps switch between sandbox and production Midtrans configurations
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function main() {
  console.log('🔄 Midtrans Mode Switcher for StreamOnPod\n');

  const envPath = path.join(__dirname, '..', '.env');
  const envProductionPath = path.join(__dirname, '..', '.env.production');

  // Check if files exist
  if (!fs.existsSync(envPath)) {
    console.error('❌ .env file not found');
    process.exit(1);
  }

  // Read current .env file
  const envContent = fs.readFileSync(envPath, 'utf8');
  const currentMode = envContent.includes('MIDTRANS_IS_PRODUCTION=true') ? 'production' : 'sandbox';

  console.log(`📊 Current Midtrans mode: ${currentMode.toUpperCase()}`);
  console.log('');

  const mode = await question('Switch to (sandbox/production): ');

  if (mode.toLowerCase() === 'production') {
    console.log('\n🚀 Switching to PRODUCTION mode...');
    
    if (!fs.existsSync(envProductionPath)) {
      console.error('❌ .env.production file not found');
      console.log('Please create .env.production with production credentials first');
      process.exit(1);
    }

    // Confirm production switch
    const confirm = await question('⚠️  Are you sure you want to switch to PRODUCTION? (yes/no): ');
    if (confirm.toLowerCase() !== 'yes') {
      console.log('❌ Production switch cancelled');
      process.exit(0);
    }

    // Copy production environment
    fs.copyFileSync(envProductionPath, envPath);
    console.log('✅ Switched to production configuration');
    console.log('');
    console.log('🔍 Please verify your production credentials:');
    console.log('1. MIDTRANS_SERVER_KEY should start with "Mid-server-"');
    console.log('2. MIDTRANS_CLIENT_KEY should start with "Mid-client-"');
    console.log('3. MIDTRANS_IS_PRODUCTION should be "true"');
    console.log('4. Configure webhooks in Midtrans Dashboard');
    console.log('');
    console.log('🧪 Run validation: node scripts/validateProductionConfig.js');

  } else if (mode.toLowerCase() === 'sandbox') {
    console.log('\n🧪 Switching to SANDBOX mode...');

    // Update .env to sandbox mode
    let newEnvContent = envContent
      .replace(/MIDTRANS_IS_PRODUCTION=true/g, 'MIDTRANS_IS_PRODUCTION=false')
      .replace(/MIDTRANS_SERVER_KEY=Mid-server-[^\n]*/g, 'MIDTRANS_SERVER_KEY=SB-Mid-server-L-oAVvIRyXTVVY65L4qCRRLr')
      .replace(/MIDTRANS_CLIENT_KEY=Mid-client-[^\n]*/g, 'MIDTRANS_CLIENT_KEY=SB-Mid-client-Gea0ZBqekgqxBYSf')
      .replace(/MIDTRANS_MERCHANT_ID=[^\n]*/g, 'MIDTRANS_MERCHANT_ID=G463893303');

    fs.writeFileSync(envPath, newEnvContent);
    console.log('✅ Switched to sandbox configuration');
    console.log('');
    console.log('🧪 Sandbox credentials configured:');
    console.log('- Server Key: SB-Mid-server-L-oAVvIRyXTVVY65L4qCRRLr');
    console.log('- Client Key: SB-Mid-client-Gea0ZBqekgqxBYSf');
    console.log('- Merchant ID: G463893303');
    console.log('');
    console.log('🧪 Test sandbox: node scripts/testMidtrans.js');

  } else {
    console.log('❌ Invalid mode. Please choose "sandbox" or "production"');
    process.exit(1);
  }

  rl.close();
}

main().catch(console.error);
