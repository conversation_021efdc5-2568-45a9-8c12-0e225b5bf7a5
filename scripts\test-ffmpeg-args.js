#!/usr/bin/env node

/**
 * Test script to verify FFmpeg argument order
 */

const cpuManager = require('../utils/cpuManager');

console.log('🧪 Testing FFmpeg Argument Order\n');

// Test encoding args
console.log('=== Encoding Arguments Test ===');
const encodingArgs = [
  '-hwaccel', 'auto',
  '-i', 'input.mp4',
  '-c:v', 'libx264',
  '-preset', 'medium',
  '-crf', '20',
  '-threads', '2',  // This should be replaced
  'output.mp4'
];

console.log('Original args:', encodingArgs.join(' '));

const modifiedEncodingArgs = cpuManager.addEncodingCPUAllocation([...encodingArgs]);
console.log('Modified args:', modifiedEncodingArgs.join(' '));

// Test streaming args
console.log('\n=== Streaming Arguments Test ===');
const streamingArgs = [
  '-hwaccel', 'auto',
  '-re',
  '-i', 'input.mp4',
  '-c:v', 'copy',
  '-c:a', 'copy',
  '-f', 'flv',
  'rtmp://example.com/live/key'
];

console.log('Original args:', streamingArgs.join(' '));

const modifiedStreamingArgs = cpuManager.addStreamingCPUAllocation([...streamingArgs]);
console.log('Modified args:', modifiedStreamingArgs.join(' '));

// Verify correct order
console.log('\n=== Verification ===');

function verifyArgOrder(args, type) {
  const inputIndex = args.indexOf('-i');
  const threadQueueIndex = args.indexOf('-thread_queue_size');
  const threadsIndex = args.indexOf('-threads');
  
  console.log(`${type} verification:`);
  console.log(`  -thread_queue_size at index: ${threadQueueIndex} (should be before -i at ${inputIndex})`);
  console.log(`  -threads at index: ${threadsIndex} (should be after -i at ${inputIndex})`);
  
  const isValid = threadQueueIndex < inputIndex && threadsIndex > inputIndex;
  console.log(`  ✅ Order is ${isValid ? 'CORRECT' : 'INCORRECT'}`);
  
  return isValid;
}

const encodingValid = verifyArgOrder(modifiedEncodingArgs, 'Encoding');
const streamingValid = verifyArgOrder(modifiedStreamingArgs, 'Streaming');

console.log(`\n🎯 Overall result: ${encodingValid && streamingValid ? '✅ PASS' : '❌ FAIL'}`);

if (encodingValid && streamingValid) {
  console.log('\n✅ FFmpeg argument order is correct!');
  console.log('The CPU allocation should now work without errors.');
} else {
  console.log('\n❌ FFmpeg argument order needs fixing.');
}
