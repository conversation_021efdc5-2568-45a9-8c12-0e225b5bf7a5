#!/usr/bin/env node

/**
 * Test MKV Optimizer
 * Tests the MKV optimization system for CPU management
 */

const fs = require('fs');
const path = require('path');

console.log('🎛️ Testing MKV Optimizer System...\n');

// Test configurations
const testCases = [
  {
    name: 'MKV Optimizer Service Exists',
    description: 'Check if MKV optimizer service file exists',
    test: () => {
      const exists = fs.existsSync('services/mkvOptimizer.js');
      return {
        passed: exists,
        details: { fileExists: exists }
      };
    }
  },
  {
    name: 'MKV Optimizer Integration',
    description: 'Check if MKV optimizer is integrated into streaming service',
    test: () => {
      const streamingService = fs.readFileSync('services/streamingService.js', 'utf8');
      const hasImport = streamingService.includes("require('./mkvOptimizer')");
      const hasIntegration = streamingService.includes('mkvOptimizer.getOptimizedMkvParams');
      const hasCleanup = streamingService.includes('mkvOptimizer.onMkvStreamStopped');
      return {
        passed: hasImport && hasIntegration && hasCleanup,
        details: {
          hasImport,
          hasIntegration,
          hasCleanup
        }
      };
    }
  },
  {
    name: 'API Endpoints Available',
    description: 'Check if MKV optimizer API endpoints are available',
    test: () => {
      const appJs = fs.readFileSync('app.js', 'utf8');
      const hasStatusEndpoint = appJs.includes('/api/mkv-optimizer/status');
      const hasConfigEndpoint = appJs.includes('/api/mkv-optimizer/config');
      const hasCanStreamEndpoint = appJs.includes('/api/mkv-optimizer/can-stream');
      return {
        passed: hasStatusEndpoint && hasConfigEndpoint && hasCanStreamEndpoint,
        details: {
          statusEndpoint: hasStatusEndpoint,
          configEndpoint: hasConfigEndpoint,
          canStreamEndpoint: hasCanStreamEndpoint
        }
      };
    }
  },
  {
    name: 'Frontend Interface Available',
    description: 'Check if frontend JavaScript for MKV optimizer exists',
    test: () => {
      const exists = fs.existsSync('public/js/mkv-optimizer.js');
      if (exists) {
        const content = fs.readFileSync('public/js/mkv-optimizer.js', 'utf8');
        const hasClass = content.includes('class MKVOptimizerManager');
        const hasAPI = content.includes('/api/mkv-optimizer/');
        return {
          passed: hasClass && hasAPI,
          details: {
            fileExists: exists,
            hasClass,
            hasAPI
          }
        };
      }
      return {
        passed: false,
        details: { fileExists: false }
      };
    }
  },
  {
    name: 'CPU Threshold Configuration',
    description: 'Check if CPU thresholds are properly configured',
    test: () => {
      const mkvOptimizer = fs.readFileSync('services/mkvOptimizer.js', 'utf8');
      const hasCritical = mkvOptimizer.includes('CRITICAL: 90');
      const hasHigh = mkvOptimizer.includes('HIGH: 80');
      const hasMedium = mkvOptimizer.includes('MEDIUM: 70');
      const hasLow = mkvOptimizer.includes('LOW: 60');
      return {
        passed: hasCritical && hasHigh && hasMedium && hasLow,
        details: {
          criticalThreshold: hasCritical,
          highThreshold: hasHigh,
          mediumThreshold: hasMedium,
          lowThreshold: hasLow
        }
      };
    }
  },
  {
    name: 'Quality Presets Configuration',
    description: 'Check if quality presets are properly configured',
    test: () => {
      const mkvOptimizer = fs.readFileSync('services/mkvOptimizer.js', 'utf8');
      const hasUltraLow = mkvOptimizer.includes('ULTRA_LOW') && mkvOptimizer.includes('320x240');
      const hasLow = mkvOptimizer.includes('480x360');
      const hasMedium = mkvOptimizer.includes('640x480');
      const hasNormal = mkvOptimizer.includes('1280x720');
      return {
        passed: hasUltraLow && hasLow && hasMedium && hasNormal,
        details: {
          ultraLowPreset: hasUltraLow,
          lowPreset: hasLow,
          mediumPreset: hasMedium,
          normalPreset: hasNormal
        }
      };
    }
  },
  {
    name: 'Stream Limiting Configuration',
    description: 'Check if concurrent stream limiting is configured',
    test: () => {
      const mkvOptimizer = fs.readFileSync('services/mkvOptimizer.js', 'utf8');
      const hasMaxStreams = mkvOptimizer.includes('maxMkvStreams: 2');
      const hasLimitCheck = mkvOptimizer.includes('activeMkvStreams.size >= this.config.recommendations.maxMkvStreams');
      return {
        passed: hasMaxStreams && hasLimitCheck,
        details: {
          maxStreamsConfig: hasMaxStreams,
          limitCheck: hasLimitCheck
        }
      };
    }
  },
  {
    name: 'Enhanced FFmpeg Parameters',
    description: 'Check if enhanced FFmpeg parameters are implemented',
    test: () => {
      const mkvOptimizer = fs.readFileSync('services/mkvOptimizer.js', 'utf8');
      const hasAnalyzeDuration = mkvOptimizer.includes('-analyzeduration');
      const hasProbeSize = mkvOptimizer.includes('-probesize');
      const hasBaselineProfile = mkvOptimizer.includes('-profile:v', 'baseline');
      const hasFlushPackets = mkvOptimizer.includes('-flush_packets');
      return {
        passed: hasAnalyzeDuration && hasProbeSize,
        details: {
          analyzeDuration: hasAnalyzeDuration,
          probeSize: hasProbeSize,
          baselineProfile: hasBaselineProfile,
          flushPackets: hasFlushPackets
        }
      };
    }
  }
];

// Run tests
let passedTests = 0;
let totalTests = testCases.length;

console.log('📋 Running MKV Optimizer Tests:\n');

testCases.forEach((testCase, index) => {
  try {
    const result = testCase.test();
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    
    console.log(`${index + 1}. ${testCase.name}`);
    console.log(`   ${testCase.description}`);
    console.log(`   Status: ${status}`);
    
    if (result.details) {
      console.log('   Details:');
      Object.entries(result.details).forEach(([key, value]) => {
        const icon = value ? '✓' : '✗';
        console.log(`     ${icon} ${key}: ${value}`);
      });
    }
    
    if (result.passed) {
      passedTests++;
    }
    
    console.log('');
  } catch (error) {
    console.log(`${index + 1}. ${testCase.name}`);
    console.log(`   Status: ❌ ERROR - ${error.message}`);
    console.log('');
  }
});

// Summary
console.log('📊 Test Summary:');
console.log(`   Passed: ${passedTests}/${totalTests} tests`);
console.log(`   Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 All MKV Optimizer tests passed!');
  console.log('   Your MKV CPU optimization system is ready to use.');
} else {
  console.log('\n⚠️  Some tests failed. Please check the implementation.');
}

// Performance benefits explanation
console.log('\n🚀 Expected Performance Benefits:');
console.log('   📉 CPU Usage: 90-100% → 60-80% (adaptive)');
console.log('   🎯 Quality: Automatic adjustment based on server load');
console.log('   🛡️  Stability: Prevents server overload from MKV streams');
console.log('   ⚡ Responsiveness: Maintains performance for other users');

// Usage instructions
console.log('\n💡 How It Works:');
console.log('   1. System monitors CPU usage every 30 seconds');
console.log('   2. Automatically adjusts MKV stream quality based on CPU load');
console.log('   3. Blocks new MKV streams if CPU > 90%');
console.log('   4. Limits concurrent MKV streams to prevent overload');
console.log('   5. Provides user notifications and recommendations');

// Configuration instructions
console.log('\n🔧 Configuration:');
console.log('   • Access: /api/mkv-optimizer/status (for monitoring)');
console.log('   • Config: /api/mkv-optimizer/config (for settings)');
console.log('   • Check: /api/mkv-optimizer/can-stream (before streaming)');

console.log('\n📈 Next Steps:');
console.log('   1. Test with actual MKV files');
console.log('   2. Monitor CPU usage during streaming');
console.log('   3. Adjust thresholds based on server capacity');
console.log('   4. Consider converting MKV to MP4 for best performance');
