#!/usr/bin/env node

/**
 * Test MKV Support
 * Tests the application's ability to handle MKV files
 */

const fs = require('fs');
const path = require('path');

console.log('🎬 Testing MKV Support in StreamOnPod...\n');

// Test configurations
const testCases = [
  {
    name: 'MKV File Upload Filter',
    description: 'Check if MKV files are accepted in upload filter',
    test: () => {
      const appJs = fs.readFileSync('app.js', 'utf8');
      const hasMatroskaSupport = appJs.includes('x-matroska');
      const hasMkvExtension = appJs.includes('.mkv');
      return {
        passed: hasMatroskaSupport && hasMkvExtension,
        details: {
          matroskaSupport: hasMatroskaSupport,
          mkvExtension: hasMkvExtension
        }
      };
    }
  },
  {
    name: 'Google Drive MKV Import',
    description: 'Check if Google Drive service supports MKV files',
    test: () => {
      const googleDriveService = fs.readFileSync('utils/googleDriveService.js', 'utf8');
      const hasMatroskaSupport = googleDriveService.includes('x-matroska');
      const hasMkvPattern = googleDriveService.includes('.mkv');
      return {
        passed: hasMatroskaSupport && hasMkvPattern,
        details: {
          matroskaSupport: hasMatroskaSupport,
          mkvPattern: hasMkvPattern
        }
      };
    }
  },
  {
    name: 'Streaming Service MKV Handling',
    description: 'Check if streaming service properly handles MKV files',
    test: () => {
      const streamingService = fs.readFileSync('services/streamingService.js', 'utf8');
      const hasMkvDetection = streamingService.includes("format.toLowerCase() === 'mkv'");
      const hasAnalyzeDuration = streamingService.includes('analyzeduration');
      const hasProbeSize = streamingService.includes('probesize');
      return {
        passed: hasMkvDetection && hasAnalyzeDuration && hasProbeSize,
        details: {
          mkvDetection: hasMkvDetection,
          analyzeDuration: hasAnalyzeDuration,
          probeSize: hasProbeSize
        }
      };
    }
  },
  {
    name: 'FFmpeg MKV Optimization',
    description: 'Check if FFmpeg commands are optimized for MKV files',
    test: () => {
      const streamingService = fs.readFileSync('services/streamingService.js', 'utf8');
      const hasIgnoreDts = streamingService.includes('igndts');
      const hasGenPts = streamingService.includes('genpts');
      const hasDiscardCorrupt = streamingService.includes('discardcorrupt');
      return {
        passed: hasIgnoreDts && hasGenPts && hasDiscardCorrupt,
        details: {
          ignoreDts: hasIgnoreDts,
          genPts: hasGenPts,
          discardCorrupt: hasDiscardCorrupt
        }
      };
    }
  },
  {
    name: 'Video Format Detection',
    description: 'Check if video format is properly detected for MKV files',
    test: () => {
      const appJs = fs.readFileSync('app.js', 'utf8');
      const hasMkvFormatDetection = appJs.includes("format = 'mkv'");
      const hasExtensionFallback = appJs.includes("endsWith('.mkv')");
      return {
        passed: hasMkvFormatDetection && hasExtensionFallback,
        details: {
          formatDetection: hasMkvFormatDetection,
          extensionFallback: hasExtensionFallback
        }
      };
    }
  }
];

// Run tests
let passedTests = 0;
let totalTests = testCases.length;

console.log('📋 Running MKV Support Tests:\n');

testCases.forEach((testCase, index) => {
  try {
    const result = testCase.test();
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    
    console.log(`${index + 1}. ${testCase.name}`);
    console.log(`   ${testCase.description}`);
    console.log(`   Status: ${status}`);
    
    if (result.details) {
      console.log('   Details:');
      Object.entries(result.details).forEach(([key, value]) => {
        const icon = value ? '✓' : '✗';
        console.log(`     ${icon} ${key}: ${value}`);
      });
    }
    
    if (result.passed) {
      passedTests++;
    }
    
    console.log('');
  } catch (error) {
    console.log(`${index + 1}. ${testCase.name}`);
    console.log(`   Status: ❌ ERROR - ${error.message}`);
    console.log('');
  }
});

// Summary
console.log('📊 Test Summary:');
console.log(`   Passed: ${passedTests}/${totalTests} tests`);
console.log(`   Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 All MKV support tests passed!');
  console.log('   Your application should now properly handle MKV files.');
} else {
  console.log('\n⚠️  Some tests failed. Please check the implementation.');
}

// Additional recommendations
console.log('\n💡 MKV Streaming Recommendations:');
console.log('   1. MKV files will automatically use re-encoding for RTMP compatibility');
console.log('   2. Re-encoding may take more CPU resources than copy mode');
console.log('   3. Consider converting MKV to MP4 for optimal streaming performance');
console.log('   4. Monitor FFmpeg logs for any codec-specific issues');

// Check if FFmpeg supports common MKV codecs
console.log('\n🔧 To verify FFmpeg codec support, run:');
console.log('   ffmpeg -codecs | grep -E "(h264|hevc|vp9|av1)"');
console.log('   ffmpeg -formats | grep matroska');
