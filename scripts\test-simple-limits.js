#!/usr/bin/env node

/**
 * Simple test for video processing service class structure
 */

console.log('🧪 Testing Video Processing Service Structure\n');

try {
  // Test 1: Check if the service can be instantiated
  console.log('📋 Test 1: Service Instantiation');
  
  // Create a minimal mock for testing
  class MockVideoProcessingService {
    constructor() {
      // Global processing limits
      this.maxGlobalConcurrentJobs = 3;
      this.maxUserConcurrentJobs = 1;
      this.activeJobs = 0;
      this.progressStore = new Map();

      // User-based queue organization
      this.userQueues = new Map();
      this.activeJobsByUser = new Map();
      this.lastProcessedUser = null;
      
      // Legacy support
      this.processingQueue = new Map();
      this.isProcessing = false;
      this.maxConcurrentJobs = this.maxGlobalConcurrentJobs;
    }

    getConfiguration() {
      return {
        maxGlobalConcurrentJobs: this.maxGlobalConcurrentJobs,
        maxUserConcurrentJobs: this.maxUserConcurrentJobs,
        activeJobs: this.activeJobs,
        totalUsers: this.userQueues.size,
        totalActiveUsers: this.activeJobsByUser.size
      };
    }

    setGlobalConcurrentJobLimit(limit) {
      if (limit > 0 && limit <= 10) {
        this.maxGlobalConcurrentJobs = limit;
        this.maxConcurrentJobs = limit;
        return true;
      }
      return false;
    }

    setUserConcurrentJobLimit(limit) {
      if (limit > 0 && limit <= 5) {
        this.maxUserConcurrentJobs = limit;
        return true;
      }
      return false;
    }

    addMockJob(videoId, userId) {
      const jobData = {
        id: videoId,
        userId: userId,
        addedAt: new Date(),
        retries: 0
      };

      if (!this.userQueues.has(userId)) {
        this.userQueues.set(userId, []);
      }
      this.userQueues.get(userId).push(jobData);
      this.processingQueue.set(videoId, jobData);
      
      return { success: true, message: 'Job added to queue' };
    }

    getQueueStatus() {
      let totalQueueLength = 0;
      const userQueueInfo = {};
      
      for (const [userId, queue] of this.userQueues.entries()) {
        totalQueueLength += queue.length;
        userQueueInfo[userId] = {
          queueLength: queue.length,
          activeJobs: this.activeJobsByUser.get(userId) || 0
        };
      }

      return {
        totalQueueLength,
        activeJobs: this.activeJobs,
        maxGlobalConcurrentJobs: this.maxGlobalConcurrentJobs,
        maxUserConcurrentJobs: this.maxUserConcurrentJobs,
        userQueues: userQueueInfo,
        lastProcessedUser: this.lastProcessedUser,
        queueLength: this.processingQueue.size,
        maxConcurrentJobs: this.maxConcurrentJobs,
        isProcessing: this.isProcessing
      };
    }

    getNextJobRoundRobin() {
      const userIds = Array.from(this.userQueues.keys()).filter(userId => 
        this.userQueues.get(userId).length > 0
      );

      if (userIds.length === 0) {
        return null;
      }

      let startIndex = 0;
      if (this.lastProcessedUser) {
        const lastIndex = userIds.indexOf(this.lastProcessedUser);
        if (lastIndex !== -1) {
          startIndex = (lastIndex + 1) % userIds.length;
        }
      }

      for (let i = 0; i < userIds.length; i++) {
        const userIndex = (startIndex + i) % userIds.length;
        const userId = userIds[userIndex];
        const userQueue = this.userQueues.get(userId);
        
        const userActiveJobs = this.activeJobsByUser.get(userId) || 0;
        if (userActiveJobs >= this.maxUserConcurrentJobs) {
          continue;
        }

        if (userQueue.length > 0) {
          const job = userQueue.shift();
          this.lastProcessedUser = userId;
          
          if (userQueue.length === 0) {
            this.userQueues.delete(userId);
          }
          
          return job;
        }
      }

      return null;
    }
  }

  const service = new MockVideoProcessingService();
  console.log('✅ Service instantiated successfully');
  console.log('Initial config:', service.getConfiguration());
  console.log('');

  // Test 2: Configuration changes
  console.log('📋 Test 2: Configuration Changes');
  const globalResult = service.setGlobalConcurrentJobLimit(5);
  console.log('Set global limit to 5:', globalResult);
  
  const userResult = service.setUserConcurrentJobLimit(2);
  console.log('Set user limit to 2:', userResult);
  
  const invalidResult = service.setGlobalConcurrentJobLimit(15);
  console.log('Set invalid global limit (15):', invalidResult);
  
  console.log('Updated config:', service.getConfiguration());
  console.log('');

  // Test 3: Queue operations
  console.log('📋 Test 3: Queue Operations');
  service.addMockJob('video1', 'user1');
  service.addMockJob('video2', 'user2');
  service.addMockJob('video3', 'user1');
  service.addMockJob('video4', 'user3');
  
  console.log('Queue status after adding jobs:');
  console.log(JSON.stringify(service.getQueueStatus(), null, 2));
  console.log('');

  // Test 4: Round-robin scheduling
  console.log('📋 Test 4: Round-Robin Scheduling');
  for (let i = 0; i < 5; i++) {
    const nextJob = service.getNextJobRoundRobin();
    if (nextJob) {
      console.log(`Round ${i + 1}: Selected ${nextJob.id} from ${nextJob.userId}`);
    } else {
      console.log(`Round ${i + 1}: No job available`);
      break;
    }
  }
  console.log('');

  console.log('✅ All tests passed successfully!');

} catch (error) {
  console.error('❌ Test failed:', error.message);
  console.error(error.stack);
}
