#!/usr/bin/env node

/**
 * Test script to verify stream status bug fixes
 * This script tests the enhanced status validation and synchronization
 */

const path = require('path');
const fs = require('fs');

// Add the project root to the module path
const projectRoot = path.resolve(__dirname, '..');
process.chdir(projectRoot);

// Import required modules
const streamingService = require('../services/streamingService');
const Stream = require('../models/Stream');

console.log('🔧 Testing Stream Status Bug Fixes');
console.log('=====================================\n');

async function testStatusValidation() {
  console.log('1. Testing validateStreamProcess function...');
  
  try {
    // Test with non-existent stream
    const result1 = streamingService.validateStreamProcess('non-existent-stream');
    console.log(`   ✅ Non-existent stream validation: ${result1} (expected: false)`);
    
    // Test with active streams
    const activeStreams = streamingService.getActiveStreams();
    console.log(`   📊 Currently active streams: ${activeStreams.length}`);
    
    if (activeStreams.length > 0) {
      for (const streamId of activeStreams) {
        const isValid = streamingService.validateStreamProcess(streamId);
        console.log(`   🔍 Stream ${streamId} validation: ${isValid}`);
      }
    } else {
      console.log('   ℹ️  No active streams to validate');
    }
    
  } catch (error) {
    console.error(`   ❌ Error testing validation: ${error.message}`);
  }
}

async function testStatusSync() {
  console.log('\n2. Testing stream status synchronization...');
  
  try {
    console.log('   🔄 Running status sync...');
    await streamingService.syncStreamStatuses();
    console.log('   ✅ Status sync completed successfully');
    
  } catch (error) {
    console.error(`   ❌ Error during status sync: ${error.message}`);
  }
}

async function testDatabaseConsistency() {
  console.log('\n3. Testing database consistency...');
  
  try {
    // Get all live streams from database
    const liveStreams = await Stream.findAll(null, 'live');
    console.log(`   📊 Streams marked as 'live' in database: ${liveStreams.length}`);
    
    // Get active streams from memory
    const activeStreams = streamingService.getActiveStreams();
    console.log(`   📊 Active streams in memory: ${activeStreams.length}`);
    
    // Check for inconsistencies
    let inconsistencies = 0;
    
    for (const stream of liveStreams) {
      const isActive = activeStreams.includes(stream.id);
      const isValid = streamingService.validateStreamProcess(stream.id);
      
      if (!isActive || !isValid) {
        console.log(`   ⚠️  Inconsistency found: Stream ${stream.id} (${stream.title})`);
        console.log(`       - Database status: live`);
        console.log(`       - In memory: ${isActive}`);
        console.log(`       - Process valid: ${isValid}`);
        inconsistencies++;
      }
    }
    
    for (const streamId of activeStreams) {
      const stream = await Stream.findById(streamId);
      if (!stream || stream.status !== 'live') {
        console.log(`   ⚠️  Inconsistency found: Stream ${streamId}`);
        console.log(`       - In memory: active`);
        console.log(`       - Database status: ${stream ? stream.status : 'not found'}`);
        inconsistencies++;
      }
    }
    
    if (inconsistencies === 0) {
      console.log('   ✅ No status inconsistencies found');
    } else {
      console.log(`   ⚠️  Found ${inconsistencies} status inconsistencies`);
    }
    
  } catch (error) {
    console.error(`   ❌ Error checking consistency: ${error.message}`);
  }
}

async function runTests() {
  try {
    await testStatusValidation();
    await testStatusSync();
    await testDatabaseConsistency();
    
    console.log('\n🎉 Status fix tests completed!');
    console.log('\nRecommendations:');
    console.log('- Monitor the application logs for "[StreamingService]" messages');
    console.log('- Check the dashboard for any "Status Issue" badges');
    console.log('- Use the "Fix Status" button if inconsistencies are found');
    console.log('- Status sync now runs every 2 minutes instead of 10 minutes');
    
  } catch (error) {
    console.error(`\n❌ Test failed: ${error.message}`);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = {
  testStatusValidation,
  testStatusSync,
  testDatabaseConsistency,
  runTests
};
