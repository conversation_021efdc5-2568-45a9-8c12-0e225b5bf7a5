#!/usr/bin/env node

/**
 * Stream Status Testing and Debugging Script
 * 
 * This script helps debug stream status issues by:
 * 1. Testing the validateStreamProcess function
 * 2. Checking for inconsistencies between memory and database
 * 3. Running manual status synchronization
 * 4. Providing detailed logging of stream states
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

const Stream = require('../models/Stream');
const streamingService = require('../services/streamingService');

async function testStreamStatus() {
  console.log('🔍 Stream Status Testing and Debugging');
  console.log('=====================================\n');

  try {
    // 1. Get current state
    console.log('1. Getting current stream states...');
    const activeStreamsInMemory = streamingService.getActiveStreams();
    const liveStreamsInDB = await Stream.findAll(null, 'live');
    const allStreams = await Stream.findAll();

    console.log(`   📊 Active streams in memory: ${activeStreamsInMemory.length}`);
    console.log(`   📊 Live streams in database: ${liveStreamsInDB.length}`);
    console.log(`   📊 Total streams in database: ${allStreams.length}`);

    if (activeStreamsInMemory.length > 0) {
      console.log(`   🔗 Active stream IDs: ${activeStreamsInMemory.join(', ')}`);
    }

    if (liveStreamsInDB.length > 0) {
      console.log(`   🔗 Live stream IDs: ${liveStreamsInDB.map(s => s.id).join(', ')}`);
    }

    console.log('');

    // 2. Test process validation
    console.log('2. Testing process validation...');
    const processValidation = {};
    
    for (const streamId of activeStreamsInMemory) {
      const isValid = streamingService.validateStreamProcess(streamId);
      processValidation[streamId] = isValid;
      console.log(`   🔍 Stream ${streamId}: ${isValid ? '✅ Valid' : '❌ Invalid'}`);
    }

    console.log('');

    // 3. Check for inconsistencies
    console.log('3. Checking for inconsistencies...');
    const inconsistencies = [];

    // Check streams marked live in DB but not in memory
    for (const stream of liveStreamsInDB) {
      const isInMemory = activeStreamsInMemory.includes(stream.id);
      if (!isInMemory) {
        inconsistencies.push({
          streamId: stream.id,
          issue: 'marked_live_but_not_in_memory',
          title: stream.title,
          lastUpdated: stream.status_updated_at
        });
        console.log(`   ⚠️  Stream ${stream.id} (${stream.title}): marked live in DB but not in memory`);
      } else {
        const isValidProcess = processValidation[stream.id];
        if (!isValidProcess) {
          inconsistencies.push({
            streamId: stream.id,
            issue: 'in_memory_but_invalid_process',
            title: stream.title,
            lastUpdated: stream.status_updated_at
          });
          console.log(`   ⚠️  Stream ${stream.id} (${stream.title}): in memory but invalid process`);
        }
      }
    }

    // Check streams in memory but not marked live in DB
    for (const streamId of activeStreamsInMemory) {
      const stream = liveStreamsInDB.find(s => s.id === streamId);
      if (!stream) {
        const dbStream = allStreams.find(s => s.id === streamId);
        inconsistencies.push({
          streamId: streamId,
          issue: 'in_memory_but_not_live_in_db',
          title: dbStream ? dbStream.title : 'Unknown',
          dbStatus: dbStream ? dbStream.status : 'not_found'
        });
        console.log(`   ⚠️  Stream ${streamId}: in memory but not marked live in DB (status: ${dbStream ? dbStream.status : 'not found'})`);
      }
    }

    if (inconsistencies.length === 0) {
      console.log('   ✅ No inconsistencies found!');
    } else {
      console.log(`   ❌ Found ${inconsistencies.length} inconsistencies`);
    }

    console.log('');

    // 4. Run manual sync
    console.log('4. Running manual status synchronization...');
    await streamingService.syncStreamStatuses();
    console.log('   ✅ Manual sync completed');

    console.log('');

    // 5. Check state after sync
    console.log('5. Checking state after synchronization...');
    const activeStreamsAfter = streamingService.getActiveStreams();
    const liveStreamsAfter = await Stream.findAll(null, 'live');

    console.log(`   📊 Active streams in memory: ${activeStreamsAfter.length}`);
    console.log(`   📊 Live streams in database: ${liveStreamsAfter.length}`);

    if (activeStreamsAfter.length !== activeStreamsInMemory.length || 
        liveStreamsAfter.length !== liveStreamsInDB.length) {
      console.log('   🔄 Changes detected after sync!');
    } else {
      console.log('   ➡️  No changes after sync');
    }

    console.log('');

    // 6. Summary
    console.log('6. Summary');
    console.log('==========');
    console.log(`   Initial inconsistencies: ${inconsistencies.length}`);
    console.log(`   Streams in memory before: ${activeStreamsInMemory.length}`);
    console.log(`   Streams in memory after: ${activeStreamsAfter.length}`);
    console.log(`   Live streams in DB before: ${liveStreamsInDB.length}`);
    console.log(`   Live streams in DB after: ${liveStreamsAfter.length}`);

    if (inconsistencies.length > 0) {
      console.log('\n   🔧 Recommendations:');
      console.log('   - Run this script again to see if issues persist');
      console.log('   - Check FFmpeg processes manually: tasklist | findstr ffmpeg (Windows) or ps aux | grep ffmpeg (Linux)');
      console.log('   - Use the admin debug endpoint: GET /api/admin/debug/stream-status');
      console.log('   - Consider restarting the application if issues persist');
    }

  } catch (error) {
    console.error('❌ Error during stream status testing:', error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testStreamStatus().then(() => {
    console.log('\n✅ Stream status testing completed');
    process.exit(0);
  }).catch(error => {
    console.error('\n❌ Stream status testing failed:', error);
    process.exit(1);
  });
}

module.exports = { testStreamStatus };
