#!/usr/bin/env node

/**
 * Test Upload Functionality
 * Tests video upload with different file sizes
 */

const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

class UploadTester {
  constructor() {
    this.baseURL = 'https://streamonpod.imthe.one';
    this.testDir = path.join(__dirname, 'test-files');
    this.sessionCookie = null;
  }

  // Create test directory
  ensureTestDir() {
    if (!fs.existsSync(this.testDir)) {
      fs.mkdirSync(this.testDir, { recursive: true });
    }
  }

  // Create a test video file of specified size
  createTestFile(sizeMB, filename) {
    const filePath = path.join(this.testDir, filename);
    const sizeBytes = sizeMB * 1024 * 1024;
    
    console.log(`📁 Creating test file: ${filename} (${sizeMB}MB)`);
    
    // Create a simple test file (not a real video, but for size testing)
    const buffer = Buffer.alloc(sizeBytes, 0);
    fs.writeFileSync(filePath, buffer);
    
    console.log(`✅ Created: ${filePath}`);
    return filePath;
  }

  // Login to get session cookie
  async login(username = 'aufanirsad', password = 'password') {
    try {
      console.log(`🔐 Logging in as ${username}...`);
      
      const response = await axios.post(`${this.baseURL}/login`, {
        username: username,
        password: password
      }, {
        withCredentials: true,
        validateStatus: () => true // Accept all status codes
      });

      if (response.status === 200 || response.status === 302) {
        // Extract session cookie
        const cookies = response.headers['set-cookie'];
        if (cookies) {
          this.sessionCookie = cookies.find(cookie => cookie.startsWith('connect.sid'));
          console.log('✅ Login successful');
          return true;
        }
      }
      
      console.log(`❌ Login failed: ${response.status} ${response.statusText}`);
      console.log('Response:', response.data);
      return false;
    } catch (error) {
      console.error('❌ Login error:', error.message);
      return false;
    }
  }

  // Test file upload
  async testUpload(filePath, expectedResult = 'success') {
    try {
      const filename = path.basename(filePath);
      const stats = fs.statSync(filePath);
      const sizeMB = (stats.size / (1024 * 1024)).toFixed(2);
      
      console.log(`\n📤 Testing upload: ${filename} (${sizeMB}MB)`);
      console.log(`   Expected result: ${expectedResult}`);

      const form = new FormData();
      form.append('video', fs.createReadStream(filePath), {
        filename: filename,
        contentType: 'video/mp4'
      });

      const headers = {
        ...form.getHeaders()
      };

      if (this.sessionCookie) {
        headers['Cookie'] = this.sessionCookie;
      }

      const response = await axios.post(`${this.baseURL}/api/videos/upload`, form, {
        headers: headers,
        maxContentLength: Infinity,
        maxBodyLength: Infinity,
        timeout: 300000, // 5 minutes
        validateStatus: () => true // Accept all status codes
      });

      console.log(`   Response: ${response.status} ${response.statusText}`);
      
      if (response.status === 200) {
        console.log('   ✅ Upload successful');
        if (response.data.video) {
          console.log(`   📹 Video ID: ${response.data.video.id}`);
          console.log(`   📁 File path: ${response.data.video.filepath}`);
        }
      } else {
        console.log('   ❌ Upload failed');
        console.log(`   Error: ${response.data.error || 'Unknown error'}`);
        console.log(`   Message: ${response.data.message || 'No message'}`);
        
        if (response.data.details) {
          console.log('   Details:', response.data.details);
        }
      }

      return {
        success: response.status === 200,
        status: response.status,
        data: response.data
      };

    } catch (error) {
      console.log('   ❌ Upload error:', error.message);
      
      if (error.response) {
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Data:`, error.response.data);
      }
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Run comprehensive upload tests
  async runTests() {
    console.log('🧪 STARTING UPLOAD TESTS\n');
    console.log('=' * 50);

    this.ensureTestDir();

    // Login first
    const loginSuccess = await this.login();
    if (!loginSuccess) {
      console.log('❌ Cannot proceed without login');
      return;
    }

    // Test cases - focus on 200MB issue
    const testCases = [
      { size: 50, name: 'test-50mb.mp4', expected: 'success' },
      { size: 100, name: 'test-100mb.mp4', expected: 'success' },
      { size: 200, name: 'test-200mb.mp4', expected: 'success' },
      { size: 300, name: 'test-300mb.mp4', expected: 'success' }
    ];

    const results = [];

    for (const testCase of testCases) {
      try {
        // Create test file
        const filePath = this.createTestFile(testCase.size, testCase.name);
        
        // Test upload
        const result = await this.testUpload(filePath, testCase.expected);
        results.push({
          ...testCase,
          ...result
        });

        // Clean up test file
        fs.unlinkSync(filePath);
        
        // Wait between tests
        await new Promise(resolve => setTimeout(resolve, 2000));
        
      } catch (error) {
        console.error(`❌ Test failed for ${testCase.name}:`, error.message);
        results.push({
          ...testCase,
          success: false,
          error: error.message
        });
      }
    }

    // Summary
    console.log('\n' + '=' * 50);
    console.log('📊 TEST RESULTS SUMMARY');
    console.log('=' * 50);

    results.forEach(result => {
      const status = result.success ? '✅ PASS' : '❌ FAIL';
      console.log(`${result.name} (${result.size}MB): ${status}`);
      if (!result.success && result.error) {
        console.log(`   Error: ${result.error}`);
      }
    });

    const passCount = results.filter(r => r.success).length;
    const totalCount = results.length;
    
    console.log(`\n🎯 Overall: ${passCount}/${totalCount} tests passed`);

    // Find the maximum successful upload size
    const successfulUploads = results.filter(r => r.success);
    if (successfulUploads.length > 0) {
      const maxSize = Math.max(...successfulUploads.map(r => r.size));
      console.log(`📈 Maximum successful upload: ${maxSize}MB`);
    }

    // Clean up test directory
    if (fs.existsSync(this.testDir)) {
      fs.rmSync(this.testDir, { recursive: true, force: true });
    }
  }

  // Quick test for 100MB file
  async quickTest100MB() {
    console.log('🚀 QUICK 100MB UPLOAD TEST\n');
    
    this.ensureTestDir();
    
    const loginSuccess = await this.login();
    if (!loginSuccess) {
      console.log('❌ Cannot proceed without login');
      return;
    }

    const filePath = this.createTestFile(100, 'quick-test-100mb.mp4');
    const result = await this.testUpload(filePath);
    
    // Clean up
    fs.unlinkSync(filePath);
    if (fs.existsSync(this.testDir)) {
      fs.rmSync(this.testDir, { recursive: true, force: true });
    }

    return result;
  }
}

// Check if FormData is available
function checkDependencies() {
  try {
    require('form-data');
    return true;
  } catch (error) {
    console.log('❌ form-data is not installed. Installing...');
    console.log('Run: npm install form-data');
    return false;
  }
}

async function main() {
  if (!checkDependencies()) {
    process.exit(1);
  }

  const tester = new UploadTester();
  
  // Check command line arguments
  const args = process.argv.slice(2);
  
  if (args.includes('--quick') || args.includes('-q')) {
    await tester.quickTest100MB();
  } else {
    await tester.runTests();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = UploadTester;
