#!/usr/bin/env node

/**
 * Test script for video processing concurrent job limits
 * Tests global and per-user limits with round-robin scheduling
 */

const videoProcessingService = require('../services/videoProcessingService');

// Mock video data for testing
const mockVideos = [
  { id: 'video1', user_id: 'user1', title: 'Video 1 from User 1' },
  { id: 'video2', user_id: 'user1', title: 'Video 2 from User 1' },
  { id: 'video3', user_id: 'user2', title: 'Video 1 from User 2' },
  { id: 'video4', user_id: 'user2', title: 'Video 2 from User 2' },
  { id: 'video5', user_id: 'user3', title: 'Video 1 from User 3' },
  { id: 'video6', user_id: 'user1', title: 'Video 3 from User 1' },
  { id: 'video7', user_id: 'user3', title: 'Video 2 from User 3' },
];

// Mock Video.findById to return test data
const originalFindById = require('../models/Video').findById;
require('../models/Video').findById = async function(videoId) {
  const mockVideo = mockVideos.find(v => v.id === videoId);
  if (mockVideo) {
    return {
      ...mockVideo,
      processing_status: 'pending',
      filepath: `/uploads/videos/${videoId}.mp4`,
      codec: 'h265' // Force processing needed
    };
  }
  return null;
};

// Mock Video.updateProcessingStatus to prevent database operations
require('../models/Video').updateProcessingStatus = async function(videoId, status) {
  console.log(`[Mock] Updated video ${videoId} status to: ${status}`);
  return true;
};

async function testConcurrentLimits() {
  console.log('🧪 Testing Video Processing Concurrent Job Limits\n');

  // Test 1: Check initial configuration
  console.log('📋 Test 1: Initial Configuration');
  const config = videoProcessingService.getConfiguration();
  console.log('Initial config:', config);
  console.log('');

  // Test 2: Add videos to queue and check round-robin behavior
  console.log('📋 Test 2: Adding videos to queue');
  for (const video of mockVideos) {
    try {
      const result = await videoProcessingService.addToQueue(video.id);
      console.log(`✅ Added ${video.id} (${video.user_id}): ${result.message}`);
    } catch (error) {
      console.log(`❌ Failed to add ${video.id}: ${error.message}`);
    }
  }
  console.log('');

  // Test 3: Check queue status
  console.log('📋 Test 3: Queue Status After Adding Videos');
  const queueStatus = videoProcessingService.getQueueStatus();
  console.log('Queue status:', JSON.stringify(queueStatus, null, 2));
  console.log('');

  // Test 4: Check detailed queue info
  console.log('📋 Test 4: Detailed Queue Information');
  const detailedInfo = videoProcessingService.getDetailedQueueInfo();
  console.log('Detailed info:', JSON.stringify(detailedInfo, null, 2));
  console.log('');

  // Test 5: Test configuration changes
  console.log('📋 Test 5: Testing Configuration Changes');
  console.log('Setting global limit to 2...');
  const globalResult = videoProcessingService.setGlobalConcurrentJobLimit(2);
  console.log('Global limit change result:', globalResult);

  console.log('Setting user limit to 1...');
  const userResult = videoProcessingService.setUserConcurrentJobLimit(1);
  console.log('User limit change result:', userResult);

  const newConfig = videoProcessingService.getConfiguration();
  console.log('New config:', newConfig);
  console.log('');

  // Test 6: Test round-robin job selection
  console.log('📋 Test 6: Testing Round-Robin Job Selection');
  for (let i = 0; i < 5; i++) {
    const nextJob = videoProcessingService.getNextJobRoundRobin();
    if (nextJob) {
      console.log(`Round ${i + 1}: Selected job ${nextJob.id} from user ${nextJob.userId}`);
    } else {
      console.log(`Round ${i + 1}: No job available`);
      break;
    }
  }
  console.log('');

  // Test 7: Test user queue removal
  console.log('📋 Test 7: Testing User Queue Removal');
  const removedJobs = videoProcessingService.removeUserFromQueue('user2');
  console.log(`Removed ${removedJobs} jobs from user2's queue`);

  const finalStatus = videoProcessingService.getQueueStatus();
  console.log('Final queue status:', JSON.stringify(finalStatus, null, 2));
  console.log('');

  // Test 8: Test invalid configuration values
  console.log('📋 Test 8: Testing Invalid Configuration Values');
  const invalidGlobal = videoProcessingService.setGlobalConcurrentJobLimit(15); // Too high
  console.log('Invalid global limit (15):', invalidGlobal);

  const invalidUser = videoProcessingService.setUserConcurrentJobLimit(0); // Too low
  console.log('Invalid user limit (0):', invalidUser);
  console.log('');

  console.log('✅ All tests completed!');
}

async function runTests() {
  try {
    await testConcurrentLimits();
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    // Restore original methods
    require('../models/Video').findById = originalFindById;
    process.exit(0);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { testConcurrentLimits };
