const midtransService = require('../services/midtrans');
require('dotenv').config();

async function testMidtransIntegration() {
  console.log('🧪 Testing Midtrans Integration...\n');

  // Test 1: Check environment variables
  console.log('1. Checking environment variables:');
  console.log(`   MIDTRANS_SERVER_KEY: ${process.env.MIDTRANS_SERVER_KEY ? '✓ Set' : '❌ Not set'}`);
  console.log(`   MIDTRANS_CLIENT_KEY: ${process.env.MIDTRANS_CLIENT_KEY ? '✓ Set' : '❌ Not set'}`);
  console.log(`   MIDTRANS_IS_PRODUCTION: ${process.env.MIDTRANS_IS_PRODUCTION || 'false'}`);
  console.log(`   MIDTRANS_MERCHANT_ID: ${process.env.MIDTRANS_MERCHANT_ID ? '✓ Set' : '❌ Not set'}\n`);

  // Test 2: IDR amount validation and formatting
  console.log('2. Testing IDR amount validation and formatting:');
  const testAmounts = [150000, 300000, 500000];
  testAmounts.forEach(idr => {
    const validated = midtransService.validateIDRAmount(idr);
    const formatted = midtransService.formatIDR(validated);
    console.log(`   ${idr} IDR = ${formatted}`);
  });
  console.log();

  // Test 3: Create test transaction (only if credentials are set)
  if (process.env.MIDTRANS_SERVER_KEY && process.env.MIDTRANS_CLIENT_KEY) {
    console.log('3. Testing transaction creation:');
    try {
      const testTransaction = await midtransService.createTransaction({
        orderId: `TEST-${Date.now()}`,
        amount: 150000, // Basic plan price in IDR
        customerDetails: {
          first_name: 'Test User',
          email: '<EMAIL>',
          phone: '+62812345678'
        },
        itemDetails: [{
          id: 'basic-plan',
          price: 150000,
          quantity: 1,
          name: 'StreamOnPod Basic Plan - monthly',
          category: 'subscription'
        }]
      });

      if (testTransaction.success) {
        console.log('   ✓ Transaction created successfully');
        console.log(`   Token: ${testTransaction.token.substring(0, 20)}...`);
        console.log(`   Redirect URL: ${testTransaction.redirect_url}`);
      } else {
        console.log('   ❌ Transaction creation failed');
        console.log(`   Error: ${testTransaction.error}`);
      }
    } catch (error) {
      console.log('   ❌ Transaction creation error');
      console.log(`   Error: ${error.message}`);
    }
  } else {
    console.log('3. Skipping transaction test (credentials not set)');
  }
  console.log();

  // Test 4: Signature verification
  console.log('4. Testing signature verification:');
  const testNotification = {
    order_id: 'TEST-123456',
    status_code: '200',
    gross_amount: '150000.00',
    signature_key: 'dummy_signature'
  };

  // This will fail with dummy data, but tests the function
  const isValid = midtransService.verifySignature(testNotification);
  console.log(`   Signature verification (with dummy data): ${isValid ? '✓ Valid' : '❌ Invalid (expected)'}`);
  console.log();

  console.log('✅ Midtrans integration test completed!');
  console.log('\n📝 Next steps:');
  console.log('1. Update .env file with your actual Midtrans credentials');
  console.log('2. Test with real Midtrans sandbox account');
  console.log('3. Configure webhook URL in Midtrans dashboard');
  console.log('4. Test payment flow end-to-end');
}

// Run the test
testMidtransIntegration().catch(console.error);
