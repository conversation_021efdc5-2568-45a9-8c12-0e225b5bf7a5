const { db } = require('../db/database');
const Stream = require('../models/Stream');

async function testStreamingFixes() {
  console.log('Testing streaming fixes...');
  
  try {
    // 1. Check for orphaned live streams
    console.log('\n1. Checking for orphaned live streams:');
    const liveStreams = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM streams WHERE status = ?', ['live'], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`Found ${liveStreams.length} streams marked as 'live':`);
    liveStreams.forEach(stream => {
      console.log(`  - ${stream.title} (ID: ${stream.id}) - Status: ${stream.status}`);
    });

    // 2. Check streaming slots calculation
    console.log('\n2. Testing streaming slots calculation:');
    const testUserId = liveStreams.length > 0 ? liveStreams[0].user_id : null;
    
    if (testUserId) {
      const Subscription = require('../models/Subscription');
      const quotaCheck = await Subscription.checkStreamingSlotLimit(testUserId);
      console.log(`User ${testUserId}:`);
      console.log(`  - Current slots: ${quotaCheck.currentSlots}`);
      console.log(`  - Max slots: ${quotaCheck.maxSlots}`);
      console.log(`  - Has limit: ${quotaCheck.hasLimit}`);
    } else {
      console.log('No users with streams found for testing');
    }

    // 3. Check all streams by status
    console.log('\n3. Stream status breakdown:');
    const statusCounts = await new Promise((resolve, reject) => {
      db.all(`
        SELECT status, COUNT(*) as count 
        FROM streams 
        GROUP BY status
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    statusCounts.forEach(row => {
      console.log(`  - ${row.status}: ${row.count} streams`);
    });

    // 4. Test RTMP URL validation
    console.log('\n4. Checking RTMP URLs for common issues:');
    const allStreams = await new Promise((resolve, reject) => {
      db.all('SELECT id, title, rtmp_url, stream_key FROM streams LIMIT 5', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    allStreams.forEach(stream => {
      const rtmpUrl = stream.rtmp_url;
      const streamKey = stream.stream_key;
      
      console.log(`\n  Stream: ${stream.title}`);
      console.log(`    RTMP URL: ${rtmpUrl}`);
      console.log(`    Stream Key: ${streamKey}`);
      
      // Check for common issues
      const issues = [];
      
      if (!rtmpUrl || rtmpUrl.trim() === '') {
        issues.push('Empty RTMP URL');
      }
      
      if (!rtmpUrl.startsWith('rtmp://')) {
        issues.push('RTMP URL should start with rtmp://');
      }
      
      if (!streamKey || streamKey.trim() === '') {
        issues.push('Empty stream key');
      }
      
      if (streamKey === '123' || streamKey === 'test' || streamKey === 'your_stream_key') {
        issues.push('Stream key appears to be a placeholder');
      }
      
      if (rtmpUrl.includes('youtube.com') && streamKey.length < 20) {
        issues.push('YouTube stream keys are usually longer than 20 characters');
      }
      
      if (issues.length > 0) {
        console.log(`    ⚠️  Issues found:`);
        issues.forEach(issue => console.log(`      - ${issue}`));
      } else {
        console.log(`    ✅ No obvious issues detected`);
      }
    });

    // 5. Recommendations
    console.log('\n5. Recommendations:');
    
    if (liveStreams.length > 0) {
      console.log('  ⚠️  Found streams marked as "live" - these should be cleaned up on server restart');
      console.log('     The restoreActiveStreams() function should mark these as offline');
    }
    
    const invalidRtmpStreams = allStreams.filter(s => 
      !s.rtmp_url || 
      !s.rtmp_url.startsWith('rtmp://') || 
      !s.stream_key || 
      s.stream_key === '123'
    );
    
    if (invalidRtmpStreams.length > 0) {
      console.log('  ⚠️  Found streams with invalid RTMP settings:');
      invalidRtmpStreams.forEach(s => {
        console.log(`     - ${s.title}: Check RTMP URL and stream key`);
      });
    }
    
    console.log('\n  ✅ Fixes implemented:');
    console.log('     - Auto-restart disabled (streams marked offline on server restart)');
    console.log('     - Infinite restart loop prevention with blacklist system');
    console.log('     - Streaming slots now count all streams (not just live ones)');
    console.log('     - Failed streams are temporarily disabled after max retries');
    console.log('     - Added API endpoint to clear failed stream status');

    console.log('\n✅ Streaming fixes test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run test if this script is executed directly
if (require.main === module) {
  testStreamingFixes().then(() => {
    console.log('Test script finished');
    process.exit(0);
  }).catch(error => {
    console.error('Test script failed:', error);
    process.exit(1);
  });
}

module.exports = { testStreamingFixes };
