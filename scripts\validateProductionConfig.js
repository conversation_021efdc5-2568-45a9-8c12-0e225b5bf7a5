#!/usr/bin/env node

/**
 * Production Configuration Validation Script
 * Validates all production settings including Midtrans configuration
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

// Load production environment
const envPath = path.join(__dirname, '..', '.env.production');
if (fs.existsSync(envPath)) {
  require('dotenv').config({ path: envPath });
} else {
  console.error('❌ .env.production file not found');
  process.exit(1);
}

console.log('🔍 StreamOnPod Production Configuration Validation\n');

let hasErrors = false;
let hasWarnings = false;

function logError(message) {
  console.error(`❌ ERROR: ${message}`);
  hasErrors = true;
}

function logWarning(message) {
  console.warn(`⚠️  WARNING: ${message}`);
  hasWarnings = true;
}

function logSuccess(message) {
  console.log(`✅ ${message}`);
}

function logInfo(message) {
  console.log(`ℹ️  ${message}`);
}

// 1. Basic Environment Validation
console.log('1. 🌍 Environment Configuration:');
if (process.env.NODE_ENV === 'production') {
  logSuccess('NODE_ENV is set to production');
} else {
  logError('NODE_ENV should be set to "production"');
}

if (process.env.PORT) {
  logSuccess(`Port configured: ${process.env.PORT}`);
} else {
  logWarning('PORT not specified, will default to 7575');
}

// 2. Security Configuration
console.log('\n2. 🔒 Security Configuration:');
if (process.env.SESSION_SECRET && process.env.SESSION_SECRET.length >= 32) {
  logSuccess('SESSION_SECRET is properly configured');
} else {
  logError('SESSION_SECRET must be at least 32 characters long');
}

if (process.env.CSRF_SECRET && process.env.CSRF_SECRET.length >= 32) {
  logSuccess('CSRF_SECRET is properly configured');
} else {
  logError('CSRF_SECRET must be at least 32 characters long');
}

// 3. Midtrans Configuration
console.log('\n3. 🏦 Midtrans Configuration:');

const requiredMidtransVars = [
  'MIDTRANS_SERVER_KEY',
  'MIDTRANS_CLIENT_KEY',
  'MIDTRANS_IS_PRODUCTION',
  'MIDTRANS_MERCHANT_ID'
];

let midtransConfigured = true;
requiredMidtransVars.forEach(varName => {
  if (process.env[varName]) {
    if (varName === 'MIDTRANS_SERVER_KEY' || varName === 'MIDTRANS_CLIENT_KEY') {
      const value = process.env[varName];
      if (value.includes('your-') || value.includes('here')) {
        logError(`${varName} contains placeholder text - replace with actual credentials`);
        midtransConfigured = false;
      } else {
        logSuccess(`${varName} is configured`);
      }
    } else {
      logSuccess(`${varName} is configured: ${process.env[varName]}`);
    }
  } else {
    logError(`${varName} is not configured`);
    midtransConfigured = false;
  }
});

// Validate Midtrans key formats
if (process.env.MIDTRANS_IS_PRODUCTION === 'true') {
  if (process.env.MIDTRANS_SERVER_KEY && !process.env.MIDTRANS_SERVER_KEY.startsWith('Mid-server-')) {
    logError('Production server key must start with "Mid-server-"');
  }
  if (process.env.MIDTRANS_CLIENT_KEY && !process.env.MIDTRANS_CLIENT_KEY.startsWith('Mid-client-')) {
    logError('Production client key must start with "Mid-client-"');
  }
} else {
  logWarning('MIDTRANS_IS_PRODUCTION is not set to true - this is sandbox mode');
}

// 4. Base URL Configuration
console.log('\n4. 🌐 URL Configuration:');
if (process.env.BASE_URL) {
  if (process.env.BASE_URL.startsWith('https://')) {
    logSuccess(`Base URL configured: ${process.env.BASE_URL}`);
  } else {
    logError('BASE_URL should use HTTPS in production');
  }
} else {
  logError('BASE_URL is not configured');
}

// 5. Database Configuration
console.log('\n5. 🗄️  Database Configuration:');
if (process.env.DATABASE_PATH) {
  const dbPath = path.resolve(process.env.DATABASE_PATH);
  if (fs.existsSync(dbPath)) {
    logSuccess(`Database file exists: ${dbPath}`);
  } else {
    logWarning(`Database file not found: ${dbPath} (will be created on first run)`);
  }
} else {
  logWarning('DATABASE_PATH not specified, will use default');
}

// 6. Performance Settings
console.log('\n6. ⚡ Performance Configuration:');
const performanceSettings = [
  'ENABLE_COMPRESSION',
  'ENABLE_CACHING',
  'LOAD_BALANCER_ENABLED'
];

performanceSettings.forEach(setting => {
  if (process.env[setting] === 'true') {
    logSuccess(`${setting} is enabled`);
  } else {
    logInfo(`${setting} is disabled or not configured`);
  }
});

// 7. Logging Configuration
console.log('\n7. 📝 Logging Configuration:');
if (process.env.LOG_LEVEL === 'error') {
  logSuccess('Log level set to error (recommended for production)');
} else {
  logWarning(`Log level is ${process.env.LOG_LEVEL || 'not set'} (consider "error" for production)`);
}

if (process.env.ENABLE_CONSOLE_LOGGING === 'false') {
  logSuccess('Console logging disabled (recommended for production)');
} else {
  logWarning('Console logging is enabled (consider disabling for production)');
}

// 8. Test Midtrans Connection (if configured)
console.log('\n8. 🧪 Testing Midtrans Connection:');
if (midtransConfigured) {
  try {
    // Test if we can load the Midtrans service
    const midtransServicePath = path.join(__dirname, '..', 'services', 'midtrans.js');
    if (fs.existsSync(midtransServicePath)) {
      logSuccess('Midtrans service file found');

      // Try to validate configuration without initializing
      const serverKey = process.env.MIDTRANS_SERVER_KEY;
      const clientKey = process.env.MIDTRANS_CLIENT_KEY;
      const isProduction = process.env.MIDTRANS_IS_PRODUCTION === 'true';

      logInfo(`Environment: ${isProduction ? 'Production' : 'Sandbox'}`);
      logInfo(`Server Key format: ${serverKey ? serverKey.substring(0, 15) + '...' : 'NOT SET'}`);
      logInfo(`Client Key format: ${clientKey ? clientKey.substring(0, 15) + '...' : 'NOT SET'}`);

      // Validate key formats
      if (isProduction) {
        if (serverKey && serverKey.startsWith('Mid-server-')) {
          logSuccess('Production server key format is correct');
        } else {
          logError('Production server key format is incorrect');
        }
        if (clientKey && clientKey.startsWith('Mid-client-')) {
          logSuccess('Production client key format is correct');
        } else {
          logError('Production client key format is incorrect');
        }
      } else {
        if (serverKey && serverKey.startsWith('SB-Mid-server-')) {
          logSuccess('Sandbox server key format is correct');
        } else {
          logError('Sandbox server key format is incorrect');
        }
        if (clientKey && clientKey.startsWith('SB-Mid-client-')) {
          logSuccess('Sandbox client key format is correct');
        } else {
          logError('Sandbox client key format is incorrect');
        }
      }
    } else {
      logError('Midtrans service file not found');
    }

  } catch (error) {
    logError(`Failed to validate Midtrans configuration: ${error.message}`);
  }
} else {
  logWarning('Skipping Midtrans connection test due to configuration issues');
}

// 9. Summary
console.log('\n' + '='.repeat(60));
console.log('📊 VALIDATION SUMMARY:');

if (hasErrors) {
  console.error('❌ Configuration has ERRORS that must be fixed before production deployment');
  console.error('   Please review the errors above and update your .env.production file');
} else if (hasWarnings) {
  console.warn('⚠️  Configuration has warnings but can be deployed');
  console.warn('   Consider addressing the warnings for optimal production setup');
} else {
  console.log('✅ All configuration checks passed!');
  console.log('   Your production environment is ready for deployment');
}

console.log('\n📚 Next Steps:');
console.log('1. Copy production environment: cp .env.production .env');
console.log('2. Update Midtrans credentials with actual production values');
console.log('3. Configure webhooks in Midtrans Dashboard');
console.log('4. Test payment flow in production');
console.log('5. Start production server: npm run production');

process.exit(hasErrors ? 1 : 0);
