class CacheService {
  constructor() {
    // Cache disabled - no-op implementation
    this.cache = new Map();
    this.ttlMap = new Map();
    this.defaultTTL = 0; // Disabled
    this.maxSize = 0; // Disabled
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0
    };

    // Cache disabled - no cleanup needed
    // console.log('[Cache] Cache service disabled - all operations will be no-op'); // Removed for production
  }

  // Set cache with TTL - NO-OP (cache disabled)
  set(key, value, ttl = this.defaultTTL) {
    // Cache disabled - always return true but don't store anything
    return true;
  }

  // Get cache value - NO-OP (cache disabled)
  get(key) {
    // Cache disabled - always return null (cache miss)
    return null;
  }

  // Delete cache entry - NO-OP (cache disabled)
  delete(key) {
    // Cache disabled - always return true
    return true;
  }

  // Check if key is expired - NO-OP (cache disabled)
  isExpired(key) {
    // Cache disabled - always return true (expired)
    return true;
  }

  // Clear all cache - NO-OP (cache disabled)
  clear() {
    // console.log(`[Cache] Cache disabled - clear operation is no-op`); // Removed for production
  }

  // Evict oldest entry - NO-OP (cache disabled)
  evictOldest() {
    // Cache disabled - no eviction needed
  }

  // Start cleanup interval - NO-OP (cache disabled)
  startCleanupInterval() {
    // Cache disabled - no cleanup needed
  }

  // Clean up expired entries - NO-OP (cache disabled)
  cleanupExpired() {
    // Cache disabled - no cleanup needed
  }

  // Get cache statistics - NO-OP (cache disabled)
  getStats() {
    return {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0,
      hitRate: '0%',
      size: 0,
      maxSize: 0,
      memoryUsage: 0,
      status: 'disabled'
    };
  }

  // Estimate memory usage - NO-OP (cache disabled)
  getMemoryUsage() {
    return 0;
  }

  // Estimate object size in bytes - NO-OP (cache disabled)
  estimateSize(obj) {
    return 0;
  }

  // Cache wrapper for database queries - NO-OP (cache disabled)
  async cacheQuery(key, queryFunction, ttl = this.defaultTTL) {
    try {
      // Cache disabled - always execute query directly
      return await queryFunction();
    } catch (error) {
      console.error('[Cache] Error in cacheQuery:', error);
      throw error;
    }
  }

  // Invalidate cache by pattern - NO-OP (cache disabled)
  invalidatePattern(pattern) {
    // Cache disabled - no invalidation needed
    return 0;
  }

  // Preload common data - NO-OP (cache disabled)
  async preloadCommonData() {
    // Cache disabled - no preloading needed
    // console.log('[Cache] Cache disabled - preloading skipped'); // Removed for production
  }

  // Cache keys for different data types
  static keys = {
    user: (id) => `user:${id}`,
    userStats: (id) => `user:stats:${id}`,
    userStreams: (id) => `user:streams:${id}`,
    userVideos: (id) => `user:videos:${id}`,
    stream: (id) => `stream:${id}`,
    streamWithVideo: (id) => `stream:video:${id}`,
    video: (id) => `video:${id}`,
    systemStats: () => 'system:stats',
    adminStats: () => 'admin:stats',
    subscriptionPlans: () => 'subscription:plans',
    loadBalancerStatus: () => 'loadbalancer:status',
    loadBalancerMetrics: () => 'loadbalancer:metrics'
  };

  // Invalidate user-related cache - NO-OP (cache disabled)
  invalidateUser(userId) {
    // Cache disabled - no invalidation needed
  }

  // Invalidate stream-related cache - NO-OP (cache disabled)
  invalidateStream(streamId, userId = null) {
    // Cache disabled - no invalidation needed
  }

  // Invalidate video-related cache - NO-OP (cache disabled)
  invalidateVideo(videoId, userId = null) {
    // Cache disabled - no invalidation needed
  }

  // Invalidate system cache - NO-OP (cache disabled)
  invalidateSystem() {
    // Cache disabled - no invalidation needed
  }
}

// Create singleton instance
const cacheService = new CacheService();

module.exports = cacheService;
