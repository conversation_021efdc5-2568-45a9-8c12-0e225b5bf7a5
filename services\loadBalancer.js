const { getSystemStats } = require('./systemMonitor');
const streamingService = require('./streamingService');

class LoadBalancer {
  constructor() {
    this.isRunning = false;
    this.monitoringInterval = null;
    this.config = {
      enabled: true,
      checkInterval: 30000, // 30 seconds (reduced from 10 seconds)
      cpuThresholds: {
        HIGH: 85,    // CPU > 85% = Minimal quality
        MEDIUM: 75,  // CPU 75-85% = Low quality
        LOW: 60      // CPU 60-75% = Medium quality
                     // CPU < 60% = Normal quality
      },
      notificationSettings: {
        notifyUsers: true,
        showQualityChanges: true
      }
    };
    this.stats = {
      totalQualityChanges: 0,
      lastCpuCheck: null,
      lastQualityChange: null,
      currentQualityLevel: 'NORMAL',
      averageCpuUsage: 0,
      cpuHistory: []
    };
  }

  start() {
    if (this.isRunning) {
      // console.log('[LoadBalancer] Already running'); // Removed for production
      return;
    }

    // console.log('[LoadBalancer] Starting CPU monitoring for load balancing...'); // Removed for production
    this.isRunning = true;

    // Start monitoring CPU usage
    this.monitoringInterval = setInterval(async () => {
      await this.checkCpuAndBalance();
    }, this.config.checkInterval);

    // console.log(`[LoadBalancer] Monitoring started with ${this.config.checkInterval}ms interval`); // Removed for production
  }

  stop() {
    if (!this.isRunning) {
      // console.log('[LoadBalancer] Not running'); // Removed for production
      return;
    }

    // console.log('[LoadBalancer] Stopping load balancer...'); // Removed for production
    this.isRunning = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    // console.log('[LoadBalancer] Load balancer stopped'); // Removed for production
  }

  async checkCpuAndBalance() {
    try {
      if (!this.config.enabled) {
        return;
      }

      // Get current system stats
      const systemStats = await getSystemStats();
      const cpuUsage = systemStats.cpu.usage;

      // Update stats
      this.stats.lastCpuCheck = new Date().toISOString();
      this.updateCpuHistory(cpuUsage);

      // Apply load balancing based on CPU usage
      await streamingService.applyLoadBalancing(cpuUsage);

      // Get current load balance status
      const loadBalanceStatus = streamingService.getLoadBalanceStatus();

      // Update our stats if quality changed
      if (loadBalanceStatus.currentQualityLevel !== this.stats.currentQualityLevel) {
        this.stats.currentQualityLevel = loadBalanceStatus.currentQualityLevel;
        this.stats.lastQualityChange = new Date().toISOString();
        this.stats.totalQualityChanges++;

        // console.log(`[LoadBalancer] Quality level changed to: ${this.stats.currentQualityLevel}`); // Removed for production
        // Send notification for load balancer activation
        this.sendLoadBalancerNotification(this.stats.currentQualityLevel, cpuUsage);
      }

    } catch (error) {
      console.error('[LoadBalancer] Error during CPU check and balancing:', error);
    }
  }

  updateCpuHistory(cpuUsage) {
    this.stats.cpuHistory.push({
      timestamp: Date.now(),
      usage: cpuUsage
    });

    // Keep only last 60 readings (10 minutes at 10s intervals)
    if (this.stats.cpuHistory.length > 60) {
      this.stats.cpuHistory.shift();
    }

    // Calculate average CPU usage
    const totalUsage = this.stats.cpuHistory.reduce((sum, entry) => sum + entry.usage, 0);
    this.stats.averageCpuUsage = Math.round(totalUsage / this.stats.cpuHistory.length);
  }

  getStatus() {
    const loadBalanceStatus = streamingService.getLoadBalanceStatus();

    return {
      isRunning: this.isRunning,
      config: this.config,
      stats: {
        ...this.stats,
        streamingServiceStatus: loadBalanceStatus
      },
      cpuThresholds: this.config.cpuThresholds,
      qualityLevels: {
        NORMAL: '720p+ (CPU < 60%)',
        MEDIUM: '480p (CPU 60-75%)',
        LOW: '360p (CPU 75-85%)',
        MINIMAL: '240p (CPU > 85%)'
      }
    };
  }

  updateConfig(newConfig) {
    try {
      if (newConfig.enabled !== undefined) {
        this.config.enabled = newConfig.enabled;
        streamingService.setLoadBalancingEnabled(newConfig.enabled);
      }

      if (newConfig.checkInterval && newConfig.checkInterval >= 5000) {
        this.config.checkInterval = newConfig.checkInterval;

        // Restart monitoring with new interval
        if (this.isRunning) {
          this.stop();
          this.start();
        }
      }

      if (newConfig.cpuThresholds) {
        Object.assign(this.config.cpuThresholds, newConfig.cpuThresholds);

        // Update streaming service config
        streamingService.updateLoadBalanceConfig({
          thresholds: this.config.cpuThresholds
        });
      }

      if (newConfig.notificationSettings) {
        Object.assign(this.config.notificationSettings, newConfig.notificationSettings);
      }

      // console.log('[LoadBalancer] Configuration updated:', newConfig); // Removed for production
      return { success: true, message: 'Configuration updated successfully' };
    } catch (error) {
      console.error('[LoadBalancer] Error updating configuration:', error);
      return { success: false, error: error.message };
    }
  }

  // Manual quality override
  async setQualityLevel(qualityLevel) {
    try {
      const validLevels = ['NORMAL', 'MEDIUM', 'LOW', 'MINIMAL'];
      if (!validLevels.includes(qualityLevel)) {
        throw new Error(`Invalid quality level. Must be one of: ${validLevels.join(', ')}`);
      }

      // Temporarily disable automatic load balancing
      const wasEnabled = this.config.enabled;
      this.config.enabled = false;

      // Apply the quality level by calling the internal function
      const qualityPreset = {
        NORMAL: { resolution: '1280x720', bitrate: 4000, fps: 30 },
        MEDIUM: { resolution: '720x480', bitrate: 2500, fps: 30 },
        LOW: { resolution: '480x360', bitrate: 1500, fps: 30 },
        MINIMAL: { resolution: '360x240', bitrate: 800, fps: 24 }
      };

      const preset = qualityPreset[qualityLevel];
      if (preset) {
        const activeStreamIds = streamingService.getActiveStreams();
        for (const streamId of activeStreamIds) {
          try {
            await this.restartStreamWithQuality(streamId, preset);
          } catch (error) {
            console.error(`[LoadBalancer] Error changing quality for stream ${streamId}:`, error);
          }
        }
      }

      this.stats.currentQualityLevel = qualityLevel;
      this.stats.lastQualityChange = new Date().toISOString();
      this.stats.totalQualityChanges++;

      // console.log(`[LoadBalancer] Manual quality override to: ${qualityLevel}`); // Removed for production
      // Re-enable after 5 minutes
      setTimeout(() => {
        this.config.enabled = wasEnabled;
        // console.log('[LoadBalancer] Automatic load balancing re-enabled after manual override'); // Removed for production
      }, 5 * 60 * 1000);

      return { success: true, message: `Quality set to ${qualityLevel}. Auto-balancing disabled for 5 minutes.` };
    } catch (error) {
      console.error('[LoadBalancer] Error setting quality level:', error);
      return { success: false, error: error.message };
    }
  }

  // Helper method to restart stream with new quality
  async restartStreamWithQuality(streamId, qualityPreset) {
    try {
      const Stream = require('../models/Stream');

      // Get current stream info
      const stream = await Stream.findById(streamId);
      if (!stream) return;

      // Stop current stream
      await streamingService.stopStream(streamId);

      // Wait a moment for cleanup
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update stream with new quality settings
      await Stream.update(streamId, {
        resolution: qualityPreset.resolution,
        bitrate: qualityPreset.bitrate,
        fps: qualityPreset.fps
      });

      // Restart with new settings
      const result = await streamingService.startStream(streamId);
      if (result.success) {
        // console.log(`[LoadBalancer] Stream ${streamId} restarted with ${qualityPreset.resolution} quality`); // Removed for production
      } else {
        console.error(`[LoadBalancer] Failed to restart stream ${streamId}:`, result.error);
      }
    } catch (error) {
      console.error(`[LoadBalancer] Error restarting stream ${streamId}:`, error);
    }
  }

  // Send load balancer notification
  async sendLoadBalancerNotification(qualityLevel, cpuUsage) {
    try {
      const notificationService = require('./notificationService');

      // Throttle notifications to avoid spam (max 1 per 10 minutes)
      const now = Date.now();
      const lastNotification = this.lastLoadBalancerNotification || 0;
      const throttleTime = 10 * 60 * 1000; // 10 minutes

      if (now - lastNotification < throttleTime) {
        return; // Skip notification due to throttling
      }

      this.lastLoadBalancerNotification = now;

      await notificationService.notifyLoadBalancerActivated(qualityLevel, cpuUsage);
    } catch (error) {
      console.error('Error sending load balancer notification:', error);
    }
  }

  // Get performance metrics
  getMetrics() {
    const now = Date.now();
    const last10Minutes = this.stats.cpuHistory.filter(entry =>
      now - entry.timestamp <= 10 * 60 * 1000
    );

    const maxCpu = last10Minutes.length > 0 ? Math.max(...last10Minutes.map(e => e.usage)) : 0;
    const minCpu = last10Minutes.length > 0 ? Math.min(...last10Minutes.map(e => e.usage)) : 0;

    return {
      currentCpu: last10Minutes.length > 0 ? last10Minutes[last10Minutes.length - 1].usage : 0,
      averageCpu: this.stats.averageCpuUsage,
      maxCpu,
      minCpu,
      totalQualityChanges: this.stats.totalQualityChanges,
      currentQualityLevel: this.stats.currentQualityLevel,
      activeStreams: streamingService.getActiveStreams().length,
      uptime: this.isRunning ? 'Running' : 'Stopped',
      lastCheck: this.stats.lastCpuCheck
    };
  }
}

// Create singleton instance
const loadBalancer = new LoadBalancer();

module.exports = loadBalancer;
