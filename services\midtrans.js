const midtransClient = require('midtrans-client');
require('dotenv').config();

class MidtransService {
  constructor() {
    // Validate required environment variables
    this.validateConfiguration();

    const isProduction = process.env.MIDTRANS_IS_PRODUCTION === 'true';
    const serverKey = process.env.MIDTRANS_SERVER_KEY;
    const clientKey = process.env.MIDTRANS_CLIENT_KEY;

    // Log configuration status (without exposing sensitive data)
    // console.log(`🏦 Midtrans initialized in ${isProduction ? 'PRODUCTION' : 'SANDBOX'} mode`); // Removed for production
    console.log(`🔑 Server Key: ${serverKey ? serverKey.substring(0, 10) + '...' : 'NOT SET'}`);
    console.log(`🔑 Client Key: ${clientKey ? clientKey.substring(0, 10) + '...' : 'NOT SET'}`);

    // Initialize Snap API
    this.snap = new midtransClient.Snap({
      isProduction,
      serverKey,
      clientKey
    });

    // Initialize Core API for transaction status
    this.coreApi = new midtransClient.CoreApi({
      isProduction,
      serverKey,
      clientKey
    });

    // Store configuration for reference
    this.isProduction = isProduction;
    this.enableSignatureVerification = process.env.MIDTRANS_ENABLE_SIGNATURE_VERIFICATION !== 'false';
    this.enableTransactionLogging = process.env.MIDTRANS_ENABLE_TRANSACTION_LOGGING !== 'false';
  }

  /**
   * Validate Midtrans configuration
   * @throws {Error} If configuration is invalid
   */
  validateConfiguration() {
    const requiredVars = [
      'MIDTRANS_SERVER_KEY',
      'MIDTRANS_CLIENT_KEY',
      'MIDTRANS_IS_PRODUCTION'
    ];

    const missing = requiredVars.filter(varName => !process.env[varName]);

    if (missing.length > 0) {
      throw new Error(`Missing required Midtrans environment variables: ${missing.join(', ')}`);
    }

    // Validate key formats
    const serverKey = process.env.MIDTRANS_SERVER_KEY;
    const clientKey = process.env.MIDTRANS_CLIENT_KEY;
    const isProduction = process.env.MIDTRANS_IS_PRODUCTION === 'true';

    if (isProduction) {
      if (!serverKey.startsWith('Mid-server-')) {
        throw new Error('Production server key must start with "Mid-server-"');
      }
      if (!clientKey.startsWith('Mid-client-')) {
        throw new Error('Production client key must start with "Mid-client-"');
      }
    } else {
      if (!serverKey.startsWith('SB-Mid-server-')) {
        throw new Error('Sandbox server key must start with "SB-Mid-server-"');
      }
      if (!clientKey.startsWith('SB-Mid-client-')) {
        throw new Error('Sandbox client key must start with "SB-Mid-client-"');
      }
    }

    // console.log('✅ Midtrans configuration validated successfully'); // Removed for production
  }

  /**
   * Create payment transaction
   * @param {Object} params - Payment parameters
   * @param {string} params.orderId - Unique order ID
   * @param {number} params.amount - Amount in IDR
   * @param {Object} params.customerDetails - Customer information
   * @param {Object} params.itemDetails - Item details
   * @returns {Promise<Object>} Snap token and redirect URL
   */
  async createTransaction(params) {
    try {
      const { orderId, amount, customerDetails, itemDetails } = params;

      // Calculate expiry time (24 hours from now)
      const expiryTime = new Date();
      expiryTime.setHours(expiryTime.getHours() + 24);
      const expiryTimeString = expiryTime.toISOString().replace(/\.\d{3}Z$/, ' +0700');

      console.log(`⏰ Setting Midtrans transaction expiry: 24 hours (${expiryTimeString})`);

      const parameter = {
        transaction_details: {
          order_id: orderId,
          gross_amount: amount
        },
        customer_details: customerDetails,
        item_details: itemDetails,
        credit_card: {
          secure: true
        },
        callbacks: {
          finish: `${process.env.BASE_URL || 'http://localhost:7575'}/payment/finish`
        },
        custom_expiry: {
          expiry_duration: 1440,
          unit: "minute"
        }
      };

      const transaction = await this.snap.createTransaction(parameter);
      return {
        success: true,
        token: transaction.token,
        redirect_url: transaction.redirect_url
      };
    } catch (error) {
      console.error('Midtrans create transaction error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get transaction status
   * @param {string} orderId - Order ID
   * @returns {Promise<Object>} Transaction status
   */
  async getTransactionStatus(orderId) {
    try {
      const statusResponse = await this.coreApi.transaction.status(orderId);
      return {
        success: true,
        data: statusResponse
      };
    } catch (error) {
      console.error('Midtrans get status error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Verify notification signature
   * @param {Object} notification - Notification data from Midtrans
   * @returns {boolean} Is signature valid
   */
  verifySignature(notification) {
    try {
      // Skip signature verification if disabled (for testing)
      if (!this.enableSignatureVerification) {
        // console.log('⚠️ Signature verification is disabled'); // Removed for production
        return true;
      }

      const crypto = require('crypto');
      const serverKey = process.env.MIDTRANS_SERVER_KEY;

      const { order_id, status_code, gross_amount, signature_key } = notification;

      // Validate required fields
      if (!order_id || !status_code || !gross_amount || !signature_key) {
        console.error('❌ Missing required fields for signature verification');
        return false;
      }

      const signatureKey = order_id + status_code + gross_amount + serverKey;
      const calculatedSignature = crypto.createHash('sha512').update(signatureKey).digest('hex');

      const isValid = calculatedSignature === signature_key;

      if (this.enableTransactionLogging) {
        // console.log(`🔐 Signature verification: ${isValid ? 'VALID' : 'INVALID'}`); // Removed for production
        if (!isValid) {
          // console.log(`📋 Expected: ${calculatedSignature}`); // Removed for production
          // console.log(`📋 Received: ${signature_key}`); // Removed for production
        }
      }

      return isValid;
    } catch (error) {
      console.error('❌ Signature verification error:', error);
      return false;
    }
  }

  /**
   * Get environment info for debugging
   * @returns {Object} Environment information
   */
  getEnvironmentInfo() {
    return {
      isProduction: this.isProduction,
      enableSignatureVerification: this.enableSignatureVerification,
      enableTransactionLogging: this.enableTransactionLogging,
      serverKeyPrefix: process.env.MIDTRANS_SERVER_KEY ? process.env.MIDTRANS_SERVER_KEY.substring(0, 10) + '...' : 'NOT SET',
      clientKeyPrefix: process.env.MIDTRANS_CLIENT_KEY ? process.env.MIDTRANS_CLIENT_KEY.substring(0, 10) + '...' : 'NOT SET'
    };
  }

  /**
   * Validate IDR amount (ensure it's a valid integer)
   * @param {number} idrAmount - Amount in IDR
   * @returns {number} Validated IDR amount
   */
  validateIDRAmount(idrAmount) {
    // Ensure amount is a positive integer
    const amount = Math.round(Math.abs(idrAmount));
    return amount;
  }

  /**
   * Format amount for display
   * @param {number} amount - Amount in IDR
   * @returns {string} Formatted amount
   */
  formatIDR(amount) {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount);
  }
}

module.exports = new MidtransService();
