/**
 * MKV Optimizer Service
 * Specialized service for optimizing MKV file streaming to reduce CPU usage
 */

const { getSystemStats } = require('./systemMonitor');

class MKVOptimizer {
  constructor() {
    this.config = {
      enabled: true,
      cpuThresholds: {
        CRITICAL: 90,  // CPU > 90% = Stop MKV streams
        HIGH: 80,      // CPU > 80% = Ultra-low quality MKV
        MEDIUM: 70,    // CPU > 70% = Low quality MKV
        LOW: 60        // CPU > 60% = Medium quality MKV
      },
      mkvPresets: {
        ULTRA_LOW: {
          resolution: '320x240',
          bitrate: 500,
          fps: 15,
          preset: 'ultrafast',
          crf: 35
        },
        LOW: {
          resolution: '480x360',
          bitrate: 800,
          fps: 20,
          preset: 'ultrafast',
          crf: 30
        },
        MEDIUM: {
          resolution: '640x480',
          bitrate: 1200,
          fps: 24,
          preset: 'ultrafast',
          crf: 28
        },
        NORMAL: {
          resolution: '1280x720',
          bitrate: 2000,
          fps: 30,
          preset: 'ultrafast',
          crf: 25
        }
      },
      recommendations: {
        showConversionSuggestion: true,
        autoConvertEnabled: false,
        maxMkvStreams: 2 // Limit concurrent MKV streams
      }
    };
    this.activeMkvStreams = new Map();
    this.conversionQueue = [];
  }

  /**
   * Get optimized FFmpeg parameters for MKV based on current CPU usage
   */
  async getOptimizedMkvParams(video, stream, originalParams) {
    try {
      if (!this.config.enabled || video.format?.toLowerCase() !== 'mkv') {
        return originalParams;
      }

      const systemStats = await getSystemStats();
      const cpuUsage = systemStats.cpu.usage;
      
      // Determine quality level based on CPU usage
      let qualityLevel = 'NORMAL';
      if (cpuUsage > this.config.cpuThresholds.CRITICAL) {
        // Don't allow new MKV streams if CPU is critical
        throw new Error('CPU usage too high for MKV streaming. Please try again later or convert to MP4.');
      } else if (cpuUsage > this.config.cpuThresholds.HIGH) {
        qualityLevel = 'ULTRA_LOW';
      } else if (cpuUsage > this.config.cpuThresholds.MEDIUM) {
        qualityLevel = 'LOW';
      } else if (cpuUsage > this.config.cpuThresholds.LOW) {
        qualityLevel = 'MEDIUM';
      }

      // Check concurrent MKV stream limit
      if (this.activeMkvStreams.size >= this.config.recommendations.maxMkvStreams) {
        throw new Error(`Maximum ${this.config.recommendations.maxMkvStreams} concurrent MKV streams allowed. Please stop other MKV streams first.`);
      }

      const preset = this.config.mkvPresets[qualityLevel];
      
      // Build optimized FFmpeg parameters
      const optimizedParams = this.buildOptimizedMkvParams(video, stream, preset, originalParams);
      
      // Track this MKV stream
      this.activeMkvStreams.set(stream.id, {
        qualityLevel,
        startTime: Date.now(),
        cpuAtStart: cpuUsage,
        preset
      });

      return {
        params: optimizedParams,
        qualityLevel,
        cpuUsage,
        recommendation: this.getRecommendation(qualityLevel, cpuUsage)
      };

    } catch (error) {
      throw error;
    }
  }

  /**
   * Build optimized FFmpeg parameters for MKV
   */
  buildOptimizedMkvParams(video, stream, preset, originalParams) {
    const videoPath = video.filepath.replace('/uploads/videos/', './uploads/videos/');
    const rtmpUrl = `${stream.rtmp_url}/${stream.stream_key}`;
    
    // Ultra-optimized parameters for MKV
    return [
      '-hwaccel', 'auto',
      '-loglevel', 'error',
      '-analyzeduration', '5000000',    // Reduced from 10M
      '-probesize', '5000000',          // Reduced from 10M
      '-fflags', '+genpts+discardcorrupt+igndts+flush_packets',
      '-avoid_negative_ts', 'make_zero',
      '-re',
      '-i', videoPath,
      
      // Video encoding optimizations
      '-c:v', 'libx264',
      '-preset', preset.preset,
      '-tune', 'zerolatency',
      '-crf', preset.crf.toString(),
      '-maxrate', `${preset.bitrate}k`,
      '-bufsize', `${preset.bitrate}k`,  // Smaller buffer
      '-pix_fmt', 'yuv420p',
      '-profile:v', 'baseline',          // Use baseline profile for compatibility
      '-level', '3.0',                   // Lower level for better compatibility
      '-g', '30',                        // Smaller GOP size
      '-keyint_min', '15',               // More frequent keyframes
      '-sc_threshold', '0',
      '-s', preset.resolution,
      '-r', preset.fps.toString(),
      
      // Audio optimizations
      '-c:a', 'aac',
      '-b:a', '64k',                     // Lower audio bitrate
      '-ar', '44100',
      '-ac', '2',
      '-aac_coder', 'twoloop',
      
      // Output optimizations
      '-f', 'flv',
      '-flvflags', 'no_duration_filesize',
      '-flush_packets', '1',
      rtmpUrl
    ];
  }

  /**
   * Get recommendation message for user
   */
  getRecommendation(qualityLevel, cpuUsage) {
    const messages = {
      ULTRA_LOW: `⚠️ CPU usage is high (${cpuUsage}%). MKV streaming at minimal quality (240p). Consider converting to MP4 for better performance.`,
      LOW: `⚠️ CPU usage is elevated (${cpuUsage}%). MKV streaming at low quality (360p). Converting to MP4 recommended.`,
      MEDIUM: `ℹ️ CPU usage is moderate (${cpuUsage}%). MKV streaming at medium quality (480p). MP4 conversion would improve performance.`,
      NORMAL: `✅ CPU usage is normal (${cpuUsage}%). MKV streaming at standard quality (720p).`
    };
    
    return messages[qualityLevel] || messages.NORMAL;
  }

  /**
   * Remove MKV stream from tracking when stopped
   */
  onMkvStreamStopped(streamId) {
    this.activeMkvStreams.delete(streamId);
  }

  /**
   * Get current MKV streaming status
   */
  getStatus() {
    return {
      enabled: this.config.enabled,
      activeMkvStreams: this.activeMkvStreams.size,
      maxMkvStreams: this.config.recommendations.maxMkvStreams,
      cpuThresholds: this.config.cpuThresholds,
      qualityPresets: this.config.mkvPresets,
      activeStreams: Array.from(this.activeMkvStreams.entries()).map(([id, info]) => ({
        streamId: id,
        qualityLevel: info.qualityLevel,
        duration: Math.round((Date.now() - info.startTime) / 1000),
        cpuAtStart: info.cpuAtStart
      }))
    };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig) {
    if (newConfig.enabled !== undefined) {
      this.config.enabled = newConfig.enabled;
    }
    
    if (newConfig.cpuThresholds) {
      Object.assign(this.config.cpuThresholds, newConfig.cpuThresholds);
    }
    
    if (newConfig.maxMkvStreams) {
      this.config.recommendations.maxMkvStreams = newConfig.maxMkvStreams;
    }
    
    return { success: true, message: 'MKV Optimizer configuration updated' };
  }

  /**
   * Check if MKV streaming should be allowed based on current system state
   */
  async canStreamMkv() {
    try {
      const systemStats = await getSystemStats();
      const cpuUsage = systemStats.cpu.usage;
      
      if (cpuUsage > this.config.cpuThresholds.CRITICAL) {
        return {
          allowed: false,
          reason: `CPU usage too high (${cpuUsage}%). MKV streaming disabled.`,
          suggestion: 'Wait for CPU usage to decrease or convert MKV to MP4.'
        };
      }
      
      if (this.activeMkvStreams.size >= this.config.recommendations.maxMkvStreams) {
        return {
          allowed: false,
          reason: `Maximum ${this.config.recommendations.maxMkvStreams} concurrent MKV streams reached.`,
          suggestion: 'Stop other MKV streams first or convert to MP4.'
        };
      }
      
      return {
        allowed: true,
        cpuUsage,
        expectedQuality: this.getExpectedQuality(cpuUsage)
      };
    } catch (error) {
      return {
        allowed: false,
        reason: 'Unable to check system status',
        error: error.message
      };
    }
  }

  /**
   * Get expected quality level based on CPU usage
   */
  getExpectedQuality(cpuUsage) {
    if (cpuUsage > this.config.cpuThresholds.HIGH) return 'ULTRA_LOW';
    if (cpuUsage > this.config.cpuThresholds.MEDIUM) return 'LOW';
    if (cpuUsage > this.config.cpuThresholds.LOW) return 'MEDIUM';
    return 'NORMAL';
  }
}

// Create singleton instance
const mkvOptimizer = new MKVOptimizer();

module.exports = mkvOptimizer;
