const EventEmitter = require('events');
const Notification = require('../models/Notification');

class NotificationService extends EventEmitter {
  constructor() {
    super();
    this.io = null; // Will be set when Socket.IO is initialized
    this.adminSockets = new Map(); // Track admin socket connections
    this.userSockets = new Map(); // Track user socket connections

    // Start cleanup interval (every 2 hours - reduced frequency)
    setInterval(() => {
      this.cleanupExpiredNotifications();
    }, 2 * 60 * 60 * 1000);
  }

  // Initialize Socket.IO
  setSocketIO(io) {
    this.io = io;
    this.setupSocketHandlers();
  }

  setupSocketHandlers() {
    if (!this.io) return;

    this.io.on('connection', (socket) => {
      // Handle admin connections
      socket.on('admin:join', (data) => {
        if (data && data.isAdmin) {
          this.adminSockets.set(socket.id, {
            socket,
            userId: data.userId,
            joinedAt: new Date()
          });
          console.log(`Admin ${data.userId} connected to notifications`);
        }
      });

      // Handle user connections
      socket.on('user:join', (data) => {
        if (data && data.userId) {
          this.userSockets.set(socket.id, {
            socket,
            userId: data.userId,
            joinedAt: new Date()
          });
          console.log(`User ${data.userId} connected to notifications`);
        }
      });

      socket.on('disconnect', () => {
        if (this.adminSockets.has(socket.id)) {
          console.log('Admin disconnected from notifications');
          this.adminSockets.delete(socket.id);
        }
        if (this.userSockets.has(socket.id)) {
          console.log('User disconnected from notifications');
          this.userSockets.delete(socket.id);
        }
      });

      // Handle notification actions
      socket.on('notification:markRead', async (notificationId) => {
        try {
          await Notification.markAsRead(notificationId);
          this.emitToAdmins('notification:updated', { id: notificationId, isRead: true });
        } catch (error) {
          console.error('Error marking notification as read:', error);
        }
      });

      socket.on('notification:markAllRead', async () => {
        try {
          await Notification.markAllAsRead();
          this.emitToAdmins('notification:allMarkedRead');
        } catch (error) {
          console.error('Error marking all notifications as read:', error);
        }
      });
    });
  }

  // Emit events to all connected admin sockets
  emitToAdmins(event, data) {
    if (!this.io) return;

    this.adminSockets.forEach(({ socket }) => {
      socket.emit(event, data);
    });
  }

  // Emit events to a specific user
  emitToUser(userId, event, data) {
    if (!this.io) return;

    this.userSockets.forEach(({ socket, userId: socketUserId }) => {
      if (socketUserId === userId) {
        socket.emit(event, data);
      }
    });
  }

  // Emit events to all connected user sockets
  emitToAllUsers(event, data) {
    if (!this.io) return;

    this.userSockets.forEach(({ socket }) => {
      socket.emit(event, data);
    });
  }

  // Create and emit notification
  async createNotification(data) {
    try {
      const notification = await Notification.create(data);

      // Emit to connected admins if it's an admin notification
      if (!data.target_user_id) {
        this.emitToAdmins('notification:new', notification);
      } else {
        // Emit to specific user if it's a user notification
        this.emitToUser(data.target_user_id, 'notification:new', notification);
      }

      // Emit internal event for other services to listen
      this.emit('notification:created', notification);

      return notification;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  // Predefined notification creators for common events
  async notifySystemAlert(title, message, priority = 'high', metadata = null) {
    return this.createNotification({
      title,
      message,
      type: 'error',
      category: 'system',
      priority,
      metadata
    });
  }

  async notifyPerformanceIssue(title, message, priority = 'warning', metadata = null) {
    return this.createNotification({
      title,
      message,
      type: 'warning',
      category: 'performance',
      priority,
      metadata
    });
  }

  async notifyUserActivity(title, message, priority = 'normal', metadata = null) {
    return this.createNotification({
      title,
      message,
      type: 'info',
      category: 'users',
      priority,
      metadata
    });
  }

  async notifyStreamEvent(title, message, priority = 'normal', metadata = null) {
    return this.createNotification({
      title,
      message,
      type: 'info',
      category: 'streams',
      priority,
      metadata
    });
  }

  async notifySuccess(title, message, priority = 'normal', metadata = null) {
    return this.createNotification({
      title,
      message,
      type: 'success',
      category: 'system',
      priority,
      metadata
    });
  }

  // Stream-related notifications
  async notifyStreamStarted(streamId, userId, streamTitle) {
    return this.notifyStreamEvent(
      'Stream Started',
      `Stream "${streamTitle}" has been started by user ${userId}`,
      'normal',
      { streamId, userId, action: 'started' }
    );
  }

  async notifyStreamStopped(streamId, userId, streamTitle, reason = null) {
    const message = reason
      ? `Stream "${streamTitle}" stopped: ${reason}`
      : `Stream "${streamTitle}" has been stopped by user ${userId}`;

    return this.notifyStreamEvent(
      'Stream Stopped',
      message,
      reason ? 'warning' : 'normal',
      { streamId, userId, action: 'stopped', reason }
    );
  }

  async notifyStreamError(streamId, userId, streamTitle, error) {
    return this.notifyStreamEvent(
      'Stream Error',
      `Stream "${streamTitle}" encountered an error: ${error}`,
      'high',
      { streamId, userId, action: 'error', error }
    );
  }

  // User-related notifications
  async notifyUserRegistered(userId, username, email) {
    return this.notifyUserActivity(
      'New User Registration',
      `New user "${username}" (${email}) has registered`,
      'normal',
      { userId, username, email, action: 'registered' }
    );
  }

  async notifyUserUpgraded(userId, username, oldPlan, newPlan) {
    return this.notifyUserActivity(
      'Plan Upgrade',
      `User "${username}" upgraded from ${oldPlan} to ${newPlan}`,
      'normal',
      { userId, username, oldPlan, newPlan, action: 'upgraded' }
    );
  }

  async notifyUserDowngraded(userId, username, oldPlan, newPlan) {
    return this.notifyUserActivity(
      'Plan Downgrade',
      `User "${username}" downgraded from ${oldPlan} to ${newPlan}`,
      'normal',
      { userId, username, oldPlan, newPlan, action: 'downgraded' }
    );
  }

  // System-related notifications
  async notifyHighCPUUsage(cpuUsage) {
    return this.notifyPerformanceIssue(
      'High CPU Usage',
      `System CPU usage is at ${cpuUsage}%`,
      cpuUsage > 90 ? 'critical' : 'high',
      { cpuUsage, type: 'cpu' }
    );
  }

  async notifyHighMemoryUsage(memoryUsage) {
    return this.notifyPerformanceIssue(
      'High Memory Usage',
      `System memory usage is at ${memoryUsage}%`,
      memoryUsage > 95 ? 'critical' : 'high',
      { memoryUsage, type: 'memory' }
    );
  }

  async notifyStorageQuotaExceeded(userId, username, usedGB, maxGB) {
    return this.notifyUserActivity(
      'Storage Quota Exceeded',
      `User "${username}" has exceeded storage quota (${usedGB}GB / ${maxGB}GB)`,
      'warning',
      { userId, username, usedGB, maxGB, action: 'quota_exceeded' }
    );
  }

  async notifyLoadBalancerActivated(qualityLevel, cpuUsage) {
    return this.notifyPerformanceIssue(
      'Load Balancer Activated',
      `Quality reduced to ${qualityLevel} due to high CPU usage (${cpuUsage}%)`,
      'warning',
      { qualityLevel, cpuUsage, type: 'load_balancer' }
    );
  }

  // User-specific notification methods
  async notifyUserSpecific(userId, title, message, type = 'info', priority = 'normal', metadata = null) {
    return this.createNotification({
      title,
      message,
      type,
      category: 'user',
      priority,
      target_user_id: userId,
      metadata
    });
  }

  async notifyUserStreamError(userId, streamTitle, error) {
    return this.notifyUserSpecific(
      userId,
      'Stream Error',
      `Your stream "${streamTitle}" encountered an error: ${error}`,
      'error',
      'high',
      { streamTitle, error, action: 'stream_error' }
    );
  }

  async notifyUserStreamStopped(userId, streamTitle, reason = null) {
    const message = reason
      ? `Your stream "${streamTitle}" was stopped: ${reason}`
      : `Your stream "${streamTitle}" has been stopped`;

    return this.notifyUserSpecific(
      userId,
      'Stream Stopped',
      message,
      reason ? 'warning' : 'info',
      reason ? 'high' : 'normal',
      { streamTitle, reason, action: 'stream_stopped' }
    );
  }

  async notifyUserStorageQuotaExceeded(userId, usedGB, maxGB) {
    return this.notifyUserSpecific(
      userId,
      'Storage Quota Exceeded',
      `You have exceeded your storage quota (${usedGB}GB / ${maxGB}GB). Please delete some files or upgrade your plan.`,
      'warning',
      'high',
      { usedGB, maxGB, action: 'quota_exceeded' }
    );
  }

  async notifyUserPlanChanged(userId, oldPlan, newPlan, isUpgrade = true) {
    const action = isUpgrade ? 'upgraded' : 'downgraded';
    const type = isUpgrade ? 'success' : 'info';

    return this.notifyUserSpecific(
      userId,
      `Plan ${isUpgrade ? 'Upgrade' : 'Downgrade'}`,
      `Your plan has been ${action} from ${oldPlan} to ${newPlan}`,
      type,
      'normal',
      { oldPlan, newPlan, action }
    );
  }

  async notifyUserWelcome(userId, username) {
    return this.notifyUserSpecific(
      userId,
      'Welcome to StreamFlow!',
      `Welcome ${username}! Your account has been created successfully. You can now start streaming.`,
      'success',
      'normal',
      { username, action: 'welcome' }
    );
  }

  // Utility methods
  async getUnreadCount() {
    return Notification.getUnreadCount();
  }

  async getRecentNotifications(limit = 10) {
    return Notification.findAll({ limit, is_read: false });
  }

  async cleanupExpiredNotifications() {
    try {
      const deletedCount = await Notification.deleteExpired();
      if (deletedCount > 0) {
        console.log(`Cleaned up ${deletedCount} expired notifications`);
      }
    } catch (error) {
      console.error('Error cleaning up expired notifications:', error);
    }
  }

  async cleanupOldNotifications(daysToKeep = 30) {
    try {
      const deletedCount = await Notification.cleanup(daysToKeep);
      if (deletedCount > 0) {
        console.log(`Cleaned up ${deletedCount} old notifications`);
      }
    } catch (error) {
      console.error('Error cleaning up old notifications:', error);
    }
  }
}

// Create singleton instance
const notificationService = new NotificationService();

module.exports = notificationService;
