const os = require('os');
const process = require('process');
const cacheService = require('./cacheService');

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      requests: {
        total: 0,
        success: 0,
        errors: 0,
        avgResponseTime: 0,
        responseTimeHistory: []
      },
      database: {
        queries: 0,
        slowQueries: 0,
        avgQueryTime: 0,
        queryTimeHistory: []
      },
      cache: {
        hits: 0,
        misses: 0,
        hitRate: 0
      },
      system: {
        cpuUsage: 0,
        memoryUsage: 0,
        diskUsage: 0,
        uptime: 0
      },
      streams: {
        active: 0,
        total: 0,
        errors: 0
      }
    };

    this.startTime = Date.now();
    this.requestTimes = new Map();
    this.slowQueryThreshold = 1000; // 1 second
    this.maxHistorySize = 100;

    // Start monitoring intervals
    this.startMonitoring();
  }

  // Start monitoring intervals
  startMonitoring() {
    // Update system metrics every 2 minutes (reduced from 30 seconds)
    setInterval(() => {
      this.updateSystemMetrics();
    }, 120000);

    // Update cache metrics every 1 minute (reduced from 10 seconds)
    setInterval(() => {
      this.updateCacheMetrics();
    }, 60000);

    // Clean old data every 10 minutes (increased from 5 minutes)
    setInterval(() => {
      this.cleanOldData();
    }, 600000);
  }

  // Middleware to track request performance
  requestTracker() {
    return (req, res, next) => {
      const startTime = Date.now();
      const requestId = `${Date.now()}-${Math.random()}`;

      this.requestTimes.set(requestId, startTime);
      this.metrics.requests.total++;

      // Override res.end to capture response time
      const originalEnd = res.end;
      res.end = (...args) => {
        const endTime = Date.now();
        const responseTime = endTime - startTime;

        // Update metrics
        this.updateRequestMetrics(responseTime, res.statusCode);
        this.requestTimes.delete(requestId);

        // Call original end
        originalEnd.apply(res, args);
      };

      next();
    };
  }

  // Update request metrics
  updateRequestMetrics(responseTime, statusCode) {
    // Track success/error
    if (statusCode >= 200 && statusCode < 400) {
      this.metrics.requests.success++;
    } else {
      this.metrics.requests.errors++;
    }

    // Update response time
    this.metrics.requests.responseTimeHistory.push({
      time: responseTime,
      timestamp: Date.now()
    });

    // Keep history size manageable
    if (this.metrics.requests.responseTimeHistory.length > this.maxHistorySize) {
      this.metrics.requests.responseTimeHistory.shift();
    }

    // Calculate average response time
    const totalTime = this.metrics.requests.responseTimeHistory.reduce((sum, entry) => sum + entry.time, 0);
    this.metrics.requests.avgResponseTime = Math.round(totalTime / this.metrics.requests.responseTimeHistory.length);
  }

  // Track application errors
  trackError(error) {
    this.metrics.requests.errors++;

    // Log error details for debugging
    console.error('[Performance Monitor] Error tracked:', {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });
  }

  // Track database query performance
  trackDatabaseQuery(queryTime, query = '') {
    this.metrics.database.queries++;

    // Track slow queries
    if (queryTime > this.slowQueryThreshold) {
      this.metrics.database.slowQueries++;
      console.warn(`[Performance] Slow query detected (${queryTime}ms): ${query.substring(0, 100)}...`);
    }

    // Update query time history
    this.metrics.database.queryTimeHistory.push({
      time: queryTime,
      timestamp: Date.now(),
      query: query.substring(0, 100)
    });

    // Keep history size manageable
    if (this.metrics.database.queryTimeHistory.length > this.maxHistorySize) {
      this.metrics.database.queryTimeHistory.shift();
    }

    // Calculate average query time
    const totalTime = this.metrics.database.queryTimeHistory.reduce((sum, entry) => sum + entry.time, 0);
    this.metrics.database.avgQueryTime = Math.round(totalTime / this.metrics.database.queryTimeHistory.length);
  }

  // Update system metrics
  updateSystemMetrics() {
    try {
      // CPU usage
      const cpus = os.cpus();
      let totalIdle = 0;
      let totalTick = 0;

      cpus.forEach(cpu => {
        for (const type in cpu.times) {
          totalTick += cpu.times[type];
        }
        totalIdle += cpu.times.idle;
      });

      this.metrics.system.cpuUsage = Math.round(100 - (totalIdle / totalTick) * 100);

      // Memory usage
      const totalMem = os.totalmem();
      const freeMem = os.freemem();
      this.metrics.system.memoryUsage = Math.round(((totalMem - freeMem) / totalMem) * 100);

      // Process uptime
      this.metrics.system.uptime = Math.round(process.uptime());

      // Node.js memory usage
      const memUsage = process.memoryUsage();
      this.metrics.system.nodeMemory = {
        rss: Math.round(memUsage.rss / 1024 / 1024), // MB
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
        external: Math.round(memUsage.external / 1024 / 1024) // MB
      };

    } catch (error) {
      console.error('[Performance] Error updating system metrics:', error);
    }
  }

  // Update cache metrics
  updateCacheMetrics() {
    try {
      const cacheStats = cacheService.getStats();
      this.metrics.cache = {
        hits: cacheStats.hits,
        misses: cacheStats.misses,
        hitRate: parseFloat(cacheStats.hitRate),
        size: cacheStats.size,
        maxSize: cacheStats.maxSize
      };
    } catch (error) {
      console.error('[Performance] Error updating cache metrics:', error);
    }
  }

  // Update stream metrics
  updateStreamMetrics(activeStreams, totalStreams, streamErrors) {
    this.metrics.streams = {
      active: activeStreams,
      total: totalStreams,
      errors: streamErrors
    };
  }

  // Get performance summary
  getPerformanceSummary() {
    return {
      ...this.metrics,
      uptime: Date.now() - this.startTime,
      timestamp: new Date().toISOString()
    };
  }

  // Get detailed performance report
  getDetailedReport() {
    const summary = this.getPerformanceSummary();

    return {
      ...summary,
      recommendations: this.generateRecommendations(),
      alerts: this.generateAlerts(),
      trends: this.analyzeTrends()
    };
  }

  // Generate performance recommendations
  generateRecommendations() {
    const recommendations = [];

    // High error rate
    if (this.metrics.requests.total > 0) {
      const errorRate = (this.metrics.requests.errors / this.metrics.requests.total) * 100;
      if (errorRate > 5) {
        recommendations.push({
          type: 'error_rate',
          severity: 'high',
          message: `High error rate detected: ${errorRate.toFixed(2)}%`,
          suggestion: 'Review error logs and fix underlying issues'
        });
      }
    }

    // Slow response times
    if (this.metrics.requests.avgResponseTime > 2000) {
      recommendations.push({
        type: 'response_time',
        severity: 'medium',
        message: `Average response time is high: ${this.metrics.requests.avgResponseTime}ms`,
        suggestion: 'Consider optimizing database queries and adding more caching'
      });
    }

    // Low cache hit rate
    if (this.metrics.cache.hitRate < 70 && this.metrics.cache.hits + this.metrics.cache.misses > 100) {
      recommendations.push({
        type: 'cache_hit_rate',
        severity: 'medium',
        message: `Low cache hit rate: ${this.metrics.cache.hitRate}%`,
        suggestion: 'Review caching strategy and increase cache TTL for stable data'
      });
    }

    // High memory usage
    if (this.metrics.system.memoryUsage > 85) {
      recommendations.push({
        type: 'memory_usage',
        severity: 'high',
        message: `High memory usage: ${this.metrics.system.memoryUsage}%`,
        suggestion: 'Consider increasing server memory or optimizing memory usage'
      });
    }

    // Slow database queries
    if (this.metrics.database.queries > 0) {
      const slowQueryRate = (this.metrics.database.slowQueries / this.metrics.database.queries) * 100;
      if (slowQueryRate > 10) {
        recommendations.push({
          type: 'slow_queries',
          severity: 'high',
          message: `High slow query rate: ${slowQueryRate.toFixed(2)}%`,
          suggestion: 'Optimize database queries and add missing indexes'
        });
      }
    }

    return recommendations;
  }

  // Generate performance alerts
  generateAlerts() {
    const alerts = [];

    // Critical CPU usage
    if (this.metrics.system.cpuUsage > 90) {
      const alert = {
        type: 'cpu_critical',
        severity: 'critical',
        message: `Critical CPU usage: ${this.metrics.system.cpuUsage}%`,
        timestamp: new Date().toISOString()
      };
      alerts.push(alert);

      // Send notification for critical CPU usage
      this.sendPerformanceNotification('cpu', this.metrics.system.cpuUsage);
    }

    // Critical memory usage
    if (this.metrics.system.memoryUsage > 95) {
      const alert = {
        type: 'memory_critical',
        severity: 'critical',
        message: `Critical memory usage: ${this.metrics.system.memoryUsage}%`,
        timestamp: new Date().toISOString()
      };
      alerts.push(alert);

      // Send notification for critical memory usage
      this.sendPerformanceNotification('memory', this.metrics.system.memoryUsage);
    }

    // Very slow response times
    if (this.metrics.requests.avgResponseTime > 5000) {
      const alert = {
        type: 'response_time_critical',
        severity: 'critical',
        message: `Critical response time: ${this.metrics.requests.avgResponseTime}ms`,
        timestamp: new Date().toISOString()
      };
      alerts.push(alert);

      // Send notification for slow response times
      this.sendPerformanceNotification('response_time', this.metrics.requests.avgResponseTime);
    }

    return alerts;
  }

  // Send performance notifications
  async sendPerformanceNotification(type, value) {
    try {
      const notificationService = require('./notificationService');

      // Throttle notifications to avoid spam (max 1 per 5 minutes per type)
      const now = Date.now();
      const lastNotification = this.lastNotifications?.[type] || 0;
      const throttleTime = 5 * 60 * 1000; // 5 minutes

      if (now - lastNotification < throttleTime) {
        return; // Skip notification due to throttling
      }

      if (!this.lastNotifications) {
        this.lastNotifications = {};
      }
      this.lastNotifications[type] = now;

      switch (type) {
        case 'cpu':
          await notificationService.notifyHighCPUUsage(value);
          break;
        case 'memory':
          await notificationService.notifyHighMemoryUsage(value);
          break;
        case 'response_time':
          await notificationService.notifyPerformanceIssue(
            'Slow Response Time',
            `Average response time is ${value}ms`,
            'critical',
            { responseTime: value, type: 'response_time' }
          );
          break;
      }
    } catch (error) {
      console.error('Error sending performance notification:', error);
    }
  }

  // Analyze performance trends
  analyzeTrends() {
    const trends = {};

    // Response time trend
    if (this.metrics.requests.responseTimeHistory.length >= 10) {
      const recent = this.metrics.requests.responseTimeHistory.slice(-10);
      const older = this.metrics.requests.responseTimeHistory.slice(-20, -10);

      if (older.length > 0) {
        const recentAvg = recent.reduce((sum, entry) => sum + entry.time, 0) / recent.length;
        const olderAvg = older.reduce((sum, entry) => sum + entry.time, 0) / older.length;

        trends.responseTime = {
          direction: recentAvg > olderAvg ? 'increasing' : 'decreasing',
          change: Math.round(((recentAvg - olderAvg) / olderAvg) * 100)
        };
      }
    }

    return trends;
  }

  // Clean old data
  cleanOldData() {
    const maxAge = 60 * 60 * 1000; // 1 hour
    const now = Date.now();

    // Clean response time history
    this.metrics.requests.responseTimeHistory = this.metrics.requests.responseTimeHistory.filter(
      entry => now - entry.timestamp < maxAge
    );

    // Clean query time history
    this.metrics.database.queryTimeHistory = this.metrics.database.queryTimeHistory.filter(
      entry => now - entry.timestamp < maxAge
    );
  }

  // Reset metrics
  resetMetrics() {
    this.metrics.requests = {
      total: 0,
      success: 0,
      errors: 0,
      avgResponseTime: 0,
      responseTimeHistory: []
    };

    this.metrics.database = {
      queries: 0,
      slowQueries: 0,
      avgQueryTime: 0,
      queryTimeHistory: []
    };

    // console.log('[Performance] Metrics reset'); // Removed for production
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

module.exports = performanceMonitor;
