const Stream = require('../models/Stream');
const { convertFromUTC, formatDateTimeWithTimezone } = require('../utils/timezone');
const scheduledTerminations = new Map();
const SCHEDULE_LOOKAHEAD_SECONDS = 120; // Increased to 2 minutes for better coverage
let streamingService = null;
function init(streamingServiceInstance) {
  streamingService = streamingServiceInstance;
  const isProduction = process.env.NODE_ENV === 'production';

  // console.log('[Scheduler] Initializing stream scheduler...'); // Removed for production
  // console.log(`[Scheduler] Production mode: ${isProduction}`); // Removed for production
  // console.log(`[Scheduler] Check interval: 2 minutes`); // Removed for production
  // console.log(`[Scheduler] Lookahead time: ${SCHEDULE_LOOKAHEAD_SECONDS} seconds`); // Removed for production
  // Set intervals - check every 30 seconds for better responsiveness
  const scheduleInterval = setInterval(checkScheduledStreams, 30 * 1000);
  const durationInterval = setInterval(checkStreamDurations, 60 * 1000);

  // console.log('[Scheduler] Intervals set, running initial checks...'); // Removed for production
  // Run initial checks
  checkScheduledStreams();
  checkStreamDurations();

  // console.log('[Scheduler] Stream scheduler initialized successfully'); // Removed for production
}
async function checkScheduledStreams() {
  try {
    if (!streamingService) {
      console.error('StreamingService not initialized in scheduler');
      return;
    }
    const now = new Date();
    const lookAheadTime = new Date(now.getTime() + SCHEDULE_LOOKAHEAD_SECONDS * 1000);
    const isProduction = process.env.NODE_ENV === 'production';

    // Always log scheduler activity for debugging
    console.log(`[Scheduler] Checking for scheduled streams at ${now.toISOString()}`);
    console.log(`[Scheduler] Looking for streams between ${now.toISOString()} and ${lookAheadTime.toISOString()}`);

    const streams = await Stream.findScheduledInRange(now, lookAheadTime);
    // console.log(`[Scheduler] Found ${streams.length} streams in range`); // Removed for production
    if (streams.length > 0) {
      // console.info(`Found ${streams.length} streams to schedule start`); // Removed for production
      for (const stream of streams) {
        // Convert scheduled time from UTC to local timezone for logging
        const localScheduleTime = stream.schedule_timezone && stream.schedule_timezone !== 'UTC'
          ? formatDateTimeWithTimezone(stream.schedule_time, stream.schedule_timezone)
          : new Date(stream.schedule_time).toISOString();

        console.info(`Starting scheduled stream: ${stream.id} - ${stream.title} (scheduled for ${localScheduleTime})`);
        // console.log(`[Scheduler] Stream details: schedule_time=${stream.schedule_time}, timezone=${stream.schedule_timezone}`); // Removed for production
        const result = await streamingService.startStream(stream.id);
        if (result.success) {
          // console.info(`Successfully started scheduled stream: ${stream.id}`); // Removed for production
          // Only schedule termination if duration > 0
          if (stream.duration && stream.duration > 0) {
            // console.log(`[Scheduler] Scheduling auto-termination for stream ${stream.id} after ${stream.duration} minutes`); // Removed for production
            scheduleStreamTermination(stream.id, stream.duration);
          } else {
            console.log(`[Scheduler] Stream ${stream.id} has no duration limit (duration: ${stream.duration}) - will run indefinitely`);
          }
        } else {
          console.error(`Failed to start scheduled stream ${stream.id}: ${result.error}`);
        }
      }
    } else {
      // Also check all scheduled streams for debugging
      const allScheduled = await Stream.findAll(null, 'scheduled');
      // console.log(`[Scheduler] Total scheduled streams in DB: ${allScheduled.length}`); // Removed for production
      if (allScheduled.length > 0) {
        // console.log('[Scheduler] Scheduled streams:'); // Removed for production
        allScheduled.forEach(stream => {
          console.log(`  - ${stream.id}: ${stream.title} at ${stream.schedule_time} (${stream.schedule_timezone})`);
        });
      }
    }
  } catch (error) {
    console.error('Error checking scheduled streams:', error);
  }
}
async function checkStreamDurations() {
  try {
    if (!streamingService) {
      console.error('StreamingService not initialized in scheduler');
      return;
    }
    const liveStreams = await Stream.findAll(null, 'live');
    // console.log(`[Scheduler] Checking durations for ${liveStreams.length} live streams`); // Removed for production
    for (const stream of liveStreams) {
      // Only check duration if it's set and > 0
      if (stream.duration && stream.duration > 0 && stream.start_time && !scheduledTerminations.has(stream.id)) {
        const startTime = new Date(stream.start_time);
        const durationMs = stream.duration * 60 * 1000;
        const shouldEndAt = new Date(startTime.getTime() + durationMs);
        const now = new Date();

        console.log(`[Scheduler] Stream ${stream.id}: duration=${stream.duration}min, started=${startTime.toISOString()}, should end=${shouldEndAt.toISOString()}`);

        if (shouldEndAt <= now) {
          console.log(`Stream ${stream.id} exceeded duration (${stream.duration} minutes), stopping now`);
          await streamingService.stopStream(stream.id);
        } else {
          const timeUntilEnd = shouldEndAt.getTime() - now.getTime();
          console.log(`[Scheduler] Stream ${stream.id} will auto-stop in ${Math.round(timeUntilEnd / 60000)} minutes`);
          scheduleStreamTermination(stream.id, timeUntilEnd / 60000);
        }
      } else if (stream.duration === 0 || stream.duration === null) {
        console.log(`[Scheduler] Stream ${stream.id} has no duration limit (duration: ${stream.duration}) - running indefinitely`);
      }
    }
  } catch (error) {
    console.error('Error checking stream durations:', error);
  }
}
function scheduleStreamTermination(streamId, durationMinutes) {
  if (scheduledTerminations.has(streamId)) {
    clearTimeout(scheduledTerminations.get(streamId));
  }
  const durationMs = durationMinutes * 60 * 1000;
  // console.log(`Scheduling termination for stream ${streamId} after ${durationMinutes} minutes`); // Removed for production
  const timeoutId = setTimeout(async () => {
    try {
      // console.log(`Terminating stream ${streamId} after ${durationMinutes} minute duration`); // Removed for production
      await streamingService.stopStream(streamId);
      scheduledTerminations.delete(streamId);
    } catch (error) {
      console.error(`Error terminating stream ${streamId}:`, error);
    }
  }, durationMs);
  scheduledTerminations.set(streamId, timeoutId);
}
function cancelStreamTermination(streamId) {
  if (scheduledTerminations.has(streamId)) {
    clearTimeout(scheduledTerminations.get(streamId));
    scheduledTerminations.delete(streamId);
    // console.log(`Cancelled scheduled termination for stream ${streamId}`); // Removed for production
    return true;
  }
  return false;
}
function handleStreamStopped(streamId) {
  return cancelStreamTermination(streamId);
}
module.exports = {
  init,
  scheduleStreamTermination,
  cancelStreamTermination,
  handleStreamStopped
};