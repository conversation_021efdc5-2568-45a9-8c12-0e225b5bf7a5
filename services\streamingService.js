const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const ffmpegInstaller = require('@ffmpeg-installer/ffmpeg');
const schedulerService = require('./schedulerService');
const { v4: uuidv4 } = require('uuid');
const { db } = require('../db/database');
const cpuManager = require('../utils/cpuManager');
let ffmpegPath;
if (fs.existsSync('/usr/bin/ffmpeg')) {
  ffmpegPath = '/usr/bin/ffmpeg';
  // console.log('Using system FFmpeg at:', ffmpegPath); // Removed for production
} else {
  ffmpegPath = ffmpegInstaller.path;
  // console.log('Using bundled FFmpeg at:', ffmpegPath); // Removed for production
}
const Stream = require('../models/Stream');
const Video = require('../models/Video');
const notificationService = require('./notificationService');
const mkvOptimizer = require('./mkvOptimizer');
const {
  createStreamingError,
  createNotFoundError,
  createValidationError,
  logError,
  retryOperation
} = require('../utils/errorHandler');
const activeStreams = new Map();
const streamLogs = new Map();
const streamRetryCount = new Map();
const MAX_RETRY_ATTEMPTS = 3;
const manuallyStoppingStreams = new Set();
const failedStreams = new Set(); // Blacklist for streams that consistently fail
const streamFailureTimestamps = new Map(); // Track failure timestamps
const MAX_LOG_LINES = 100;

// Memory management constants
const CLEANUP_INTERVAL = 60 * 60 * 1000; // 1 hour
const DATA_RETENTION_TIME = 24 * 60 * 60 * 1000; // 24 hours
const MAX_FAILURE_HISTORY = 50; // Maximum failure records per stream

// Auto-stop configuration
const AUTO_STOP_CONFIG = {
  MAX_CONSECUTIVE_FAILURES: 5,        // Max consecutive failures before auto-stop
  FAILURE_WINDOW_MINUTES: 5,          // Time window to count failures
  I_O_ERROR_THRESHOLD: 3,             // Max I/O errors before immediate stop
  CONNECTION_ERROR_THRESHOLD: 3       // Max connection errors before immediate stop
};

// Load balancing configuration
const LOAD_BALANCE_CONFIG = {
  CPU_CHECK_INTERVAL: 10000,          // Check CPU every 10 seconds
  QUALITY_CHANGE_COOLDOWN: 30000,     // Wait 30s between quality changes
  CPU_THRESHOLDS: {
    HIGH: 85,      // CPU > 85% = Minimal quality
    MEDIUM: 75,    // CPU 75-85% = Low quality
    LOW: 60        // CPU 60-75% = Medium quality
                   // CPU < 60% = Normal quality
  },
  QUALITY_PRESETS: {
    MINIMAL: { resolution: '360x240', bitrate: 1200, fps: 24, preset: 'ultrafast' },
    LOW: { resolution: '480x360', bitrate: 2000, fps: 30, preset: 'ultrafast' },
    MEDIUM: { resolution: '720x480', bitrate: 3500, fps: 30, preset: 'ultrafast' },
    NORMAL: { resolution: '1280x720', bitrate: 5000, fps: 30, preset: 'veryfast' }
  }
};

// Load balancing state
let currentCpuUsage = 0;
let currentQualityLevel = 'NORMAL';
let lastQualityChange = 0;
let loadBalancingEnabled = true;
let qualityChangeHistory = new Map(); // streamId -> last change time

// Load balancing functions
function determineQualityLevel(cpuUsage) {
  if (cpuUsage >= LOAD_BALANCE_CONFIG.CPU_THRESHOLDS.HIGH) {
    return 'MINIMAL';
  } else if (cpuUsage >= LOAD_BALANCE_CONFIG.CPU_THRESHOLDS.MEDIUM) {
    return 'LOW';
  } else if (cpuUsage >= LOAD_BALANCE_CONFIG.CPU_THRESHOLDS.LOW) {
    return 'MEDIUM';
  } else {
    return 'NORMAL';
  }
}

function getQualityPreset(qualityLevel) {
  return LOAD_BALANCE_CONFIG.QUALITY_PRESETS[qualityLevel] || LOAD_BALANCE_CONFIG.QUALITY_PRESETS.NORMAL;
}

function shouldChangeQuality(newQualityLevel) {
  const now = Date.now();
  const timeSinceLastChange = now - lastQualityChange;

  return (
    loadBalancingEnabled &&
    newQualityLevel !== currentQualityLevel &&
    timeSinceLastChange >= LOAD_BALANCE_CONFIG.QUALITY_CHANGE_COOLDOWN
  );
}

async function applyLoadBalancing(cpuUsage) {
  try {
    currentCpuUsage = cpuUsage;
    const newQualityLevel = determineQualityLevel(cpuUsage);

    if (shouldChangeQuality(newQualityLevel)) {
      // console.log(`[LoadBalancer] CPU usage: ${cpuUsage}% - Changing quality from ${currentQualityLevel} to ${newQualityLevel}`); // Removed for production
      const activeStreamIds = Array.from(activeStreams.keys());
      if (activeStreamIds.length > 0) {
        await changeAllStreamsQuality(newQualityLevel);
        currentQualityLevel = newQualityLevel;
        lastQualityChange = Date.now();

        // Log quality change
        addLoadBalanceLog(`Quality changed to ${newQualityLevel} due to CPU usage: ${cpuUsage}%`);
      }
    }
  } catch (error) {
    console.error('[LoadBalancer] Error applying load balancing:', error);
  }
}

async function changeAllStreamsQuality(qualityLevel) {
  const activeStreamIds = Array.from(activeStreams.keys());
  const qualityPreset = getQualityPreset(qualityLevel);

  // console.log(`[LoadBalancer] Applying ${qualityLevel} quality to ${activeStreamIds.length} active streams`); // Removed for production
  // Batch load all streams at once to prevent N+1 query problem
  const streams = await Stream.findByIds(activeStreamIds);
  const streamMap = new Map(streams.map(s => [s.id, s]));

  for (const streamId of activeStreamIds) {
    try {
      const now = Date.now();
      const lastChange = qualityChangeHistory.get(streamId) || 0;

      // Prevent too frequent changes per stream
      if (now - lastChange < LOAD_BALANCE_CONFIG.QUALITY_CHANGE_COOLDOWN) {
        continue;
      }

      const stream = streamMap.get(streamId);
      if (stream && stream.use_advanced_settings) {
        // Only apply load balancing to streams with advanced settings
        await restartStreamWithQuality(streamId, qualityPreset);
        qualityChangeHistory.set(streamId, now);

        addStreamLog(streamId, `[LoadBalancer] Quality changed to ${qualityLevel} (CPU: ${currentCpuUsage}%)`);
      }
    } catch (error) {
      console.error(`[LoadBalancer] Error changing quality for stream ${streamId}:`, error);
    }
  }
}

async function restartStreamWithQuality(streamId, qualityPreset) {
  try {
    // Get current stream info
    const stream = await Stream.findById(streamId);
    if (!stream) return;

    // Stop current stream
    await stopStream(streamId);

    // Wait a moment for cleanup
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Update stream with new quality settings
    await Stream.update(streamId, {
      resolution: qualityPreset.resolution,
      bitrate: qualityPreset.bitrate,
      fps: qualityPreset.fps
    });

    // Restart with new settings
    const result = await startStream(streamId);
    if (result.success) {
      // console.log(`[LoadBalancer] Stream ${streamId} restarted with ${qualityPreset.resolution} quality`); // Removed for production
    } else {
      console.error(`[LoadBalancer] Failed to restart stream ${streamId}:`, result.error);
    }
  } catch (error) {
    console.error(`[LoadBalancer] Error restarting stream ${streamId}:`, error);
  }
}

// Load balance logging with production optimization
const loadBalanceLogs = [];
const isProduction = process.env.NODE_ENV === 'production';

function addLoadBalanceLog(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] ${message}`;

  loadBalanceLogs.push(logEntry);
  if (loadBalanceLogs.length > MAX_LOG_LINES) {
    loadBalanceLogs.shift();
  }

  // In production, only log warnings and errors
  if (!isProduction || level === 'warn' || level === 'error') {
    if (level === 'error') {
      console.error(`[LoadBalancer] ${message}`);
    } else if (level === 'warn') {
      console.warn(`[LoadBalancer] ${message}`);
    } else {
      // console.info(`[LoadBalancer] ${message}`); // Removed for production
    }
  }
}

function getLoadBalanceStatus() {
  return {
    enabled: loadBalancingEnabled,
    currentCpuUsage,
    currentQualityLevel,
    lastQualityChange: new Date(lastQualityChange).toISOString(),
    activeStreams: activeStreams.size,
    thresholds: LOAD_BALANCE_CONFIG.CPU_THRESHOLDS,
    qualityPresets: LOAD_BALANCE_CONFIG.QUALITY_PRESETS,
    recentLogs: loadBalanceLogs.slice(-10)
  };
}

// Function to clean up orphaned streams after server restart
async function restoreActiveStreams() {
  try {
    // console.log('[StreamingService] Cleaning up orphaned streams...'); // Removed for production
    const liveStreams = await Stream.findAll(null, 'live');

    if (liveStreams && liveStreams.length > 0) {
      // console.log(`[StreamingService] Found ${liveStreams.length} streams marked as 'live' in database`); // Removed for production
      console.log('[StreamingService] Marking all as offline since server restarted (streams cannot survive restart)');

      for (const stream of liveStreams) {
        try {
          console.log(`[StreamingService] Marking stream ${stream.id} as offline (server restart cleanup)`);
          await Stream.updateStatus(stream.id, 'offline');
        } catch (error) {
          console.error(`[StreamingService] Error marking stream ${stream.id} as offline:`, error);
        }
      }
    } else {
      // console.log('[StreamingService] No orphaned streams found to clean up'); // Removed for production
    }
  } catch (error) {
    console.error('[StreamingService] Error during stream cleanup:', error);
  }
}

// Helper function to detect if video needs re-encoding
function needsReencoding(video, targetResolution, targetBitrate, targetFps) {
  if (!video.resolution || !video.bitrate || !video.fps) {
    return true; // Re-encode if we don't have metadata
  }

  // Check codec compatibility - HEVC videos and some MKV codecs need re-encoding for streaming
  if (video.codec) {
    const codecLower = video.codec.toLowerCase();
    if (codecLower.includes('hevc') || codecLower.includes('h265') ||
        codecLower.includes('vp9') || codecLower.includes('av1')) {
      // console.log(`[StreamingService] Video uses ${video.codec} codec, re-encoding required for streaming compatibility`); // Removed for production
      return true;
    }
  }

  // Check container format - MKV files often need re-encoding for RTMP streaming
  if (video.format && video.format.toLowerCase() === 'mkv') {
    // console.log(`[StreamingService] MKV container detected, re-encoding recommended for RTMP compatibility`); // Removed for production
    return true;
  }

  const [currentWidth, currentHeight] = video.resolution.split('x').map(Number);
  const [targetWidth, targetHeight] = targetResolution.split('x').map(Number);

  // Check if current video exceeds target parameters significantly
  const bitrateExceeds = video.bitrate > (targetBitrate * 1.5);
  const resolutionExceeds = (currentWidth > targetWidth * 1.2) || (currentHeight > targetHeight * 1.2);
  const fpsExceeds = video.fps > (targetFps * 1.2);

  return bitrateExceeds || resolutionExceeds || fpsExceeds;
}

// Helper function to get copy-mode compatible settings for a video
function getCopyModeCompatibleSettings(video) {
  if (!video.resolution || !video.bitrate || !video.fps) {
    return null; // Can't determine compatibility without metadata
  }

  // Check codec compatibility
  if (video.codec) {
    const codecLower = video.codec.toLowerCase();
    if (codecLower.includes('hevc') || codecLower.includes('h265') ||
        codecLower.includes('vp9') || codecLower.includes('av1')) {
      return null; // Incompatible codec
    }
  }

  // Check container format
  if (video.format && video.format.toLowerCase() === 'mkv') {
    return null; // MKV needs re-encoding for RTMP
  }

  const [currentWidth, currentHeight] = video.resolution.split('x').map(Number);

  // Calculate maximum compatible settings (with some safety margin)
  const maxBitrate = Math.floor(video.bitrate * 1.4); // 40% margin
  const maxFps = Math.floor(video.fps * 1.1); // 10% margin

  // Available resolution options that are compatible
  const allResolutions = [
    { value: '480x360', width: 480, height: 360, label: '360p (480x360)' },
    { value: '640x480', width: 640, height: 480, label: '480p (640x480)' },
    { value: '854x480', width: 854, height: 480, label: '480p Wide (854x480)' },
    { value: '1280x720', width: 1280, height: 720, label: '720p HD (1280x720)' },
    { value: '1920x1080', width: 1920, height: 1080, label: '1080p FHD (1920x1080)' },
    { value: '2560x1440', width: 2560, height: 1440, label: '1440p QHD (2560x1440)' },
    { value: '3840x2160', width: 3840, height: 2160, label: '2160p 4K (3840x2160)' }
  ];

  // Determine video orientation
  const isVideoVertical = currentHeight > currentWidth;

  // Only return copy-mode compatible resolutions (no upscaling, no re-encoding)
  const compatibleResolutions = allResolutions.filter(res => {
    // For vertical videos, compare with vertical resolution options
    if (isVideoVertical) {
      // For vertical video (1080x1920), check against vertical resolution options
      const verticalWidth = res.height; // In vertical mode, height becomes width
      const verticalHeight = res.width; // In vertical mode, width becomes height
      return verticalWidth <= currentWidth * 1.1 && verticalHeight <= currentHeight * 1.1;
    } else {
      // For horizontal videos, use standard comparison
      return res.width <= currentWidth * 1.1 && res.height <= currentHeight * 1.1;
    }
  });

  // For backward compatibility, also provide copyModeResolutions
  const copyModeResolutions = compatibleResolutions;

  // Available bitrate options that are compatible
  const bitrateOptions = [1000, 1500, 2000, 2500, 3000, 4000, 5000, 6000, 8000, 10000]
    .filter(bitrate => bitrate <= maxBitrate);

  // Available FPS options that are compatible
  const fpsOptions = [15, 20, 24, 25, 30, 50, 60, 120]
    .filter(fps => fps <= maxFps);

  return {
    maxBitrate,
    maxFps,
    compatibleResolutions,
    copyModeResolutions,
    bitrateOptions,
    fpsOptions,
    videoProperties: {
      resolution: video.resolution,
      bitrate: video.bitrate,
      fps: video.fps,
      codec: video.codec,
      format: video.format
    }
  };
}

// Helper function to detect available hardware acceleration
function getHardwareAcceleration() {
  // For now, we'll use software encoding for maximum compatibility
  // In the future, this can be enhanced to detect NVENC, QSV, etc.
  return 'none';
}

// Helper function to calculate optimal bitrate for copy mode
function getOptimalCopyModeBitrate(video, userBitrate = null) {
  // For copy mode, we can use higher bitrates since no CPU encoding is involved
  // Prioritize user setting, then fall back to video-based optimization

  // If user specified a bitrate, use it (with reasonable caps for bandwidth)
  if (userBitrate && userBitrate > 0) {
    // Allow high bitrates for copy mode, cap at 15Mbps for bandwidth safety
    return Math.min(userBitrate, 15000);
  }

  if (!video.resolution) {
    return 4000; // Default high bitrate for copy mode
  }

  const [width, height] = video.resolution.split('x').map(Number);
  const totalPixels = width * height;

  // Calculate optimal bitrate based on resolution (fallback when no user setting)
  // Higher resolution = higher bitrate (since copy mode doesn't use CPU)
  if (totalPixels >= 1920 * 1080) {
    // 1080p and above - use high bitrate for best quality
    return Math.min(video.bitrate || 6000, 12000); // Increased cap for 1080p+
  } else if (totalPixels >= 1280 * 720) {
    // 720p - use medium-high bitrate
    return Math.min(video.bitrate || 4000, 8000); // Increased cap for 720p
  } else if (totalPixels >= 854 * 480) {
    // 480p - use medium bitrate
    return Math.min(video.bitrate || 2500, 5000); // Increased cap for 480p
  } else {
    // Lower resolutions - use moderate bitrate
    return Math.min(video.bitrate || 1500, 3000); // Increased cap for lower res
  }
}

function addStreamLog(streamId, message) {
  if (!streamLogs.has(streamId)) {
    streamLogs.set(streamId, []);
  }
  const logs = streamLogs.get(streamId);
  logs.push({
    timestamp: new Date().toISOString(),
    message
  });
  if (logs.length > MAX_LOG_LINES) {
    logs.shift();
  }
}

// Memory management functions
function cleanupStreamData(streamId) {
  // console.log(`[MemoryCleanup] Cleaning up data for stream ${streamId}`); // Removed for production
  // Remove from all tracking maps and sets
  activeStreams.delete(streamId);
  streamLogs.delete(streamId);
  streamRetryCount.delete(streamId);
  qualityChangeHistory.delete(streamId);
  streamFailureTimestamps.delete(streamId);
  manuallyStoppingStreams.delete(streamId);
  failedStreams.delete(streamId);

  // Clean up MKV optimizer tracking
  mkvOptimizer.onMkvStreamStopped(streamId);

  // console.log(`[MemoryCleanup] Cleaned up data for stream ${streamId}`); // Removed for production
}

function performPeriodicCleanup() {
  const now = Date.now();
  let cleanedItems = 0;

  // console.log('[MemoryCleanup] Starting periodic cleanup...'); // Removed for production
  // Clean old quality change history
  for (const [streamId, timestamp] of qualityChangeHistory.entries()) {
    if (now - timestamp > DATA_RETENTION_TIME) {
      qualityChangeHistory.delete(streamId);
      cleanedItems++;
    }
  }

  // Clean old failure timestamps and limit history size
  for (const [streamId, failures] of streamFailureTimestamps.entries()) {
    // Remove old failures
    const recentFailures = failures.filter(f => now - f.timestamp < DATA_RETENTION_TIME);

    // Limit history size
    if (recentFailures.length > MAX_FAILURE_HISTORY) {
      recentFailures.splice(0, recentFailures.length - MAX_FAILURE_HISTORY);
    }

    if (recentFailures.length === 0) {
      streamFailureTimestamps.delete(streamId);
      cleanedItems++;
    } else if (recentFailures.length !== failures.length) {
      streamFailureTimestamps.set(streamId, recentFailures);
      cleanedItems++;
    }
  }

  // Clean old stream logs for inactive streams
  for (const [streamId, logs] of streamLogs.entries()) {
    if (!activeStreams.has(streamId)) {
      // Check if the last log entry is old
      const lastLog = logs[logs.length - 1];
      if (lastLog && now - new Date(lastLog.timestamp).getTime() > DATA_RETENTION_TIME) {
        streamLogs.delete(streamId);
        cleanedItems++;
      }
    }
  }

  // Clean orphaned retry counts
  for (const streamId of streamRetryCount.keys()) {
    if (!activeStreams.has(streamId)) {
      streamRetryCount.delete(streamId);
      cleanedItems++;
    }
  }

  // console.log(`[MemoryCleanup] Periodic cleanup completed. Cleaned ${cleanedItems} items.`); // Removed for production
  // console.log(`[MemoryCleanup] Current memory usage: activeStreams=${activeStreams.size}, streamLogs=${streamLogs.size}, qualityChangeHistory=${qualityChangeHistory.size}, failureTimestamps=${streamFailureTimestamps.size}`); // Removed for production
}
async function buildFFmpegArgs(stream) {
  const video = await Video.findById(stream.video_id);
  if (!video) {
    throw new Error(`Video record not found in database for video_id: ${stream.video_id}`);
  }
  // Use streaming-ready path if available, otherwise use original
  const preferredPath = video.streaming_ready_path || video.filepath;
  const relativeVideoPath = preferredPath.startsWith('/') ? preferredPath.substring(1) : preferredPath;
  const projectRoot = path.resolve(__dirname, '..');
  const videoPath = path.join(projectRoot, 'public', relativeVideoPath);
  if (!fs.existsSync(videoPath)) {
    // If streaming-ready file doesn't exist but original does, use original
    if (video.streaming_ready_path && preferredPath === video.streaming_ready_path) {
      console.warn(`[StreamingService] Streaming-ready file not found for ${stream.id}, falling back to original file`);
      const originalRelativePath = video.filepath.startsWith('/') ? video.filepath.substring(1) : video.filepath;
      const originalVideoPath = path.join(projectRoot, 'public', originalRelativePath);

      if (fs.existsSync(originalVideoPath)) {
        console.log(`[StreamingService] Using original file for stream ${stream.id}: ${originalVideoPath}`);
        // Recursively call with original path
        const originalVideo = { ...video, streaming_ready_path: null };
        return buildFFmpegArgs({ ...stream }, originalVideo);
      }
    }

    console.error(`[StreamingService] CRITICAL: Video file not found on disk.`);
    console.error(`[StreamingService] Checked path: ${videoPath}`);
    console.error(`[StreamingService] stream.video_id: ${stream.video_id}`);
    console.error(`[StreamingService] video.filepath (from DB): ${video.filepath}`);
    console.error(`[StreamingService] video.streaming_ready_path (from DB): ${video.streaming_ready_path}`);
    console.error(`[StreamingService] Calculated relativeVideoPath: ${relativeVideoPath}`);
    console.error(`[StreamingService] process.cwd(): ${process.cwd()}`);
    throw new Error('Video file not found on disk. Please check paths and file existence.');
  }
  const rtmpUrl = `${stream.rtmp_url.replace(/\/$/, '')}/${stream.stream_key}`;
  const loopOption = '-stream_loop';
  const loopValue = stream.loop_video ? '-1' : '0';

  // If using streaming-ready file, we can always use copy mode for optimal performance
  if (video.streaming_ready_path && preferredPath === video.streaming_ready_path) {
    // console.log(`[StreamingService] Using streaming-ready file for ${stream.id} - copy mode enabled for optimal performance`); // Removed for production

    // Get user bitrate setting for streaming-ready files too
    const userBitrate = stream.bitrate;
    const optimalBitrate = getOptimalCopyModeBitrate(video, userBitrate);

    return [
      '-hwaccel', getHardwareAcceleration(),
      '-loglevel', 'error',
      '-re',
      '-fflags', '+genpts+discardcorrupt',
      '-avoid_negative_ts', 'make_zero',
      loopOption, loopValue,
      '-i', videoPath,
      '-c:v', 'copy',
      '-c:a', 'copy',
      '-b:v', `${optimalBitrate}k`,  // Add bitrate control for streaming-ready files
      '-maxrate', `${optimalBitrate}k`, // Ensure consistent bitrate
      '-bufsize', `${optimalBitrate * 2}k`, // Buffer for smooth streaming
      '-bsf:v', 'h264_mp4toannexb',
      '-f', 'flv',
      '-flvflags', 'no_duration_filesize',
      rtmpUrl
    ];
  }

  // Optimized basic copy mode - most efficient for streaming
  if (!stream.use_advanced_settings) {
    // Check if video codec is compatible with copy mode
    const isHEVC = video.codec && (video.codec.toLowerCase().includes('hevc') || video.codec.toLowerCase().includes('h265'));

    if (isHEVC) {
      // Force re-encoding for HEVC videos
      // console.log(`[StreamingService] HEVC video detected, using re-encoding mode for stream ${stream.id}`); // Removed for production
      const resolution = '1280x720';
      const bitrate = 2500;
      const fps = 30;

      return [
        '-hwaccel', getHardwareAcceleration(),
        '-loglevel', 'error',
        '-re',
        '-fflags', '+genpts+discardcorrupt',
        '-avoid_negative_ts', 'make_zero',
        loopOption, loopValue,
        '-i', videoPath,
        '-c:v', 'libx264',
        '-preset', 'ultrafast',
        '-tune', 'zerolatency',
        '-b:v', `${bitrate}k`,
        '-maxrate', `${bitrate * 1.2}k`,
        '-bufsize', `${bitrate * 1.5}k`,
        '-pix_fmt', 'yuv420p',
        '-g', '60',
        '-keyint_min', '60',
        '-sc_threshold', '0',
        '-s', resolution,
        '-r', fps.toString(),
        '-c:a', 'aac',
        '-b:a', '128k',
        '-ar', '44100',
        '-ac', '2',
        '-f', 'flv',
        '-flvflags', 'no_duration_filesize',
        rtmpUrl
      ];
    }

    // Check if video codec is compatible with copy mode
    const videoCodec = video.video_codec || 'unknown';

    if (videoCodec.toLowerCase().includes('h264') || videoCodec.toLowerCase().includes('avc')) {
      // Standard copy mode for H.264 videos with user-specified or optimal bitrate
      const userBitrate = stream.bitrate; // Get user setting from stream
      const optimalBitrate = getOptimalCopyModeBitrate(video, userBitrate);
      // console.log(`[StreamingService] Using copy mode for H.264 stream ${stream.id} with bitrate ${optimalBitrate}k (user: ${userBitrate}k)`); // Removed for production

      // For copy mode, we need to add bitrate control to ensure proper streaming bitrate
      // Even though we're copying, we can still control the output bitrate for streaming
      return [
        '-hwaccel', getHardwareAcceleration(),
        '-loglevel', 'error',
        '-re',
        '-fflags', '+genpts+discardcorrupt',
        '-avoid_negative_ts', 'make_zero',
        loopOption, loopValue,
        '-i', videoPath,
        '-c:v', 'copy',
        '-c:a', 'copy',
        '-b:v', `${optimalBitrate}k`,  // Add bitrate control for copy mode
        '-maxrate', `${optimalBitrate}k`, // Ensure consistent bitrate
        '-bufsize', `${optimalBitrate * 2}k`, // Buffer for smooth streaming
        '-bsf:v', 'h264_mp4toannexb',
        '-f', 'flv',
        '-flvflags', 'no_duration_filesize',
        rtmpUrl
      ];
    } else {
      // Force re-encoding for non-H.264 codecs to ensure compatibility
      // console.log(`[StreamingService] Video codec ${videoCodec} not compatible with copy mode, using re-encoding for stream ${stream.id}`); // Removed for production
      const resolution = stream.resolution || '1280x720';
      const bitrate = stream.bitrate || 2500;
      const fps = stream.fps || 30;

      // Check if this is an MKV file and use optimizer
      const isMKV = video.format && video.format.toLowerCase() === 'mkv';

      if (isMKV) {
        try {
          // Use MKV optimizer for CPU-aware streaming
          const mkvResult = await mkvOptimizer.getOptimizedMkvParams(video, stream, []);
          addStreamLog(stream.id, mkvResult.recommendation);
          return mkvResult.params;
        } catch (mkvError) {
          // If MKV optimizer fails, throw the error to prevent streaming
          throw new Error(`MKV Streaming Error: ${mkvError.message}`);
        }
      }

      // Enhanced re-encoding for other complex formats
      const extraOptions = ['-fflags', '+genpts+discardcorrupt'];

      return [
        '-hwaccel', getHardwareAcceleration(),
        '-loglevel', 'error',
        '-re',
        ...extraOptions,
        '-avoid_negative_ts', 'make_zero',
        loopOption, loopValue,
        '-i', videoPath,
        '-c:v', 'libx264',
        '-preset', 'ultrafast',
        '-tune', 'zerolatency',
        '-b:v', `${bitrate}k`,
        '-maxrate', `${bitrate * 1.2}k`,
        '-bufsize', `${bitrate * 1.5}k`,
        '-pix_fmt', 'yuv420p',
        '-g', '60',
        '-keyint_min', '60',
        '-sc_threshold', '0',
        '-s', resolution,
        '-r', fps.toString(),
        '-c:a', 'aac',
        '-b:a', '128k',
        '-ar', '44100',
        '-ac', '2',
        '-f', 'flv',
        '-flvflags', 'no_duration_filesize',
        rtmpUrl
      ];
    }
  }
  // Advanced settings with smart copy/encode decision
  const resolution = stream.resolution || '1280x720';
  const bitrate = stream.bitrate || 2500;
  const fps = stream.fps || 30;

  // Check if we can use copy mode even with advanced settings
  const shouldReencode = needsReencoding(video, resolution, bitrate, fps);

  if (!shouldReencode) {
    // Check if video codec is compatible with copy mode
    const isHEVC = video.codec && (video.codec.toLowerCase().includes('hevc') || video.codec.toLowerCase().includes('h265'));

    if (isHEVC) {
      // console.log(`[StreamingService] HEVC video detected in advanced mode, forcing re-encoding for stream ${stream.id}`); // Removed for production
      // Force re-encoding for HEVC even in copy mode
    } else {
      // Use optimized copy mode for H.264 videos with user-specified bitrate
      const userBitrate = stream.bitrate; // Get user setting from advanced settings
      const optimalBitrate = getOptimalCopyModeBitrate(video, userBitrate);
      // console.log(`[StreamingService] Using optimized copy mode for stream ${stream.id} (video is compatible) with bitrate ${optimalBitrate}k (user: ${userBitrate}k)`); // Removed for production

      // For advanced copy mode, also add bitrate control to ensure proper streaming bitrate
      return [
        '-hwaccel', getHardwareAcceleration(),
        '-loglevel', 'error',
        '-re',
        '-fflags', '+genpts+discardcorrupt',
        '-avoid_negative_ts', 'make_zero',
        loopOption, loopValue,
        '-i', videoPath,
        '-c:v', 'copy',
        '-c:a', 'copy',
        '-b:v', `${optimalBitrate}k`,  // Add bitrate control for advanced copy mode
        '-maxrate', `${optimalBitrate}k`, // Ensure consistent bitrate
        '-bufsize', `${optimalBitrate * 2}k`, // Buffer for smooth streaming
        '-bsf:v', 'h264_mp4toannexb',
        '-f', 'flv',
        '-flvflags', 'no_duration_filesize',
        rtmpUrl
      ];
    }
  }

  // Full re-encoding mode (only when necessary)
  // console.log(`[StreamingService] Using re-encoding mode for stream ${stream.id} (video needs optimization)`); // Removed for production

  // Check if this is an MKV file and use optimizer for advanced settings too
  const isMKV = video.format && video.format.toLowerCase() === 'mkv';

  if (isMKV) {
    try {
      // Use MKV optimizer for CPU-aware streaming in advanced mode
      const mkvResult = await mkvOptimizer.getOptimizedMkvParams(video, stream, []);
      addStreamLog(stream.id, `[Advanced] ${mkvResult.recommendation}`);
      return mkvResult.params;
    } catch (mkvError) {
      // If MKV optimizer fails, throw the error to prevent streaming
      throw new Error(`MKV Advanced Streaming Error: ${mkvError.message}`);
    }
  }

  // Enhanced re-encoding for other complex formats
  const extraOptions = ['-fflags', '+genpts+discardcorrupt'];

  return [
    '-hwaccel', getHardwareAcceleration(),
    '-loglevel', 'error',
    '-re',
    ...extraOptions,
    '-avoid_negative_ts', 'make_zero',
    loopOption, loopValue,
    '-i', videoPath,
    '-c:v', 'libx264',
    '-preset', 'ultrafast', // Changed from 'veryfast' to 'ultrafast' for better performance
    '-tune', 'zerolatency', // Optimize for low latency streaming
    '-b:v', `${bitrate}k`,
    '-maxrate', `${bitrate * 1.2}k`, // Reduced from 1.5x to 1.2x
    '-bufsize', `${bitrate * 1.5}k`, // Reduced from 2x to 1.5x
    '-pix_fmt', 'yuv420p',
    '-g', '60',
    '-keyint_min', '60',
    '-sc_threshold', '0',
    '-s', resolution,
    '-r', fps.toString(),
    '-c:a', 'aac',
    '-b:a', '128k',
    '-ar', '44100',
    '-ac', '2',
    '-f', 'flv',
    '-flvflags', 'no_duration_filesize',
    rtmpUrl
  ];
}
async function startStream(streamId) {
  try {
    // Validate stream ID
    if (!streamId) {
      throw createValidationError('Stream ID is required', null, 'streamId');
    }

    // Check if stream is blacklisted due to repeated failures
    if (failedStreams.has(streamId)) {
      throw createStreamingError(
        'Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.',
        streamId,
        'start'
      );
    }

    streamRetryCount.set(streamId, 0);

    // Check if stream is already active
    if (activeStreams.has(streamId)) {
      throw createStreamingError('Stream is already active', streamId, 'start');
    }

    // Get stream from database
    const stream = await Stream.findById(streamId);
    if (!stream) {
      throw createNotFoundError('Stream', streamId);
    }

    // Validate stream configuration
    if (!stream.rtmp_url) {
      throw createValidationError('RTMP URL is required', null, 'rtmp_url');
    }

    if (!stream.stream_key) {
      throw createValidationError('Stream key is required', null, 'stream_key');
    }

    if (!stream.video_id) {
      throw createValidationError('Video is required for streaming', null, 'video_id');
    }
    const ffmpegArgs = await buildFFmpegArgs(stream);

    // Add CPU allocation for streaming
    const cpuAllocatedArgs = cpuManager.addStreamingCPUAllocation([...ffmpegArgs]);

    const fullCommand = `${ffmpegPath} ${cpuAllocatedArgs.join(' ')}`;
    addStreamLog(streamId, `Starting stream with command: ${fullCommand}`);

    // Log CPU allocation info
    const cpuInfo = cpuManager.getAllocationInfo();
    addStreamLog(streamId, `[CPU] Using ${cpuInfo.streaming.threads} threads on cores ${cpuInfo.streaming.range} for streaming`);

    // console.log(`Starting stream: ${fullCommand}`); // Removed for production
    const ffmpegProcess = cpuManager.spawnWithCPUAffinity(ffmpegPath, cpuAllocatedArgs, {
      detached: false,
      stdio: ['ignore', 'pipe', 'pipe']
    }, 'streaming');
    // Store the process in activeStreams with start timestamp
    ffmpegProcess.startTime = Date.now();
    activeStreams.set(streamId, ffmpegProcess);

    // Update status to 'live' immediately - FFmpeg process started successfully
    // console.log(`[StreamingService] ✅ FFmpeg process started successfully for stream ${streamId}`); // Removed for production
    await Stream.updateStatus(streamId, 'live', stream.user_id);
    // console.log(`[StreamingService] ✅ Stream ${streamId} status updated to 'live'`); // Removed for production
    // Send notification for stream started
    try {
      await notificationService.notifyStreamStarted(streamId, stream.user_id, stream.title);
    } catch (notifError) {
      console.error('Error sending stream start notification:', notifError);
    }
    // Production-optimized FFmpeg logging
    const isProduction = process.env.NODE_ENV === 'production';
    const enableVerboseFFmpegLogs = process.env.ENABLE_VERBOSE_FFMPEG_LOGS === 'true';

    ffmpegProcess.stdout.on('data', (data) => {
      const message = data.toString().trim();
      if (message && !isProduction) {
        addStreamLog(streamId, `[OUTPUT] ${message}`);
        if (enableVerboseFFmpegLogs) {
          // console.debug(`[FFMPEG_STDOUT] ${streamId}: ${message}`); // Removed for production
        }
      }
    });

    ffmpegProcess.stderr.on('data', (data) => {
      const message = data.toString().trim();
      if (message) {
        // Always log to stream logs for debugging
        addStreamLog(streamId, `[FFmpeg] ${message}`);

        // Filter out progress messages and only log important events
        const isProgressMessage = message.includes('frame=') ||
                                 message.includes('fps=') ||
                                 message.includes('bitrate=') ||
                                 message.includes('time=') ||
                                 message.includes('speed=');

        const isImportantMessage = message.includes('error') ||
                                  message.includes('Error') ||
                                  message.includes('warning') ||
                                  message.includes('Warning') ||
                                  message.includes('failed') ||
                                  message.includes('Failed');

        // In production, only log errors and warnings
        if (isImportantMessage) {
          console.error(`[FFMPEG_STDERR] ${streamId}: ${message}`);
        } else if (!isProduction && !isProgressMessage && enableVerboseFFmpegLogs) {
          // console.debug(`[FFMPEG_STDERR] ${streamId}: ${message}`); // Removed for production
        }

        // Check for critical errors that should trigger immediate auto-stop
        if (message.includes('I/O error') ||
            message.includes('Connection refused') ||
            message.includes('Network is unreachable') ||
            message.includes('Invalid stream key') ||
            message.includes('Authentication failed') ||
            message.includes('403 Forbidden') ||
            message.includes('404 Not Found')) {

          const autoStopDecision = shouldAutoStopStream(streamId, message);

          if (autoStopDecision.shouldStop) {
            // console.log(`[StreamingService] Critical error detected, auto-stopping stream ${streamId}: ${autoStopDecision.reason}`); // Removed for production
            // Send notification for stream error (async wrapper)
            (async () => {
              try {
                const stream = await Stream.findById(streamId);
                if (stream) {
                  await notificationService.notifyStreamError(streamId, stream.user_id, stream.title, message);
                }
              } catch (notifError) {
                console.error('Error sending stream error notification:', notifError);
              }
            })();
            // Use setTimeout to avoid blocking the current stderr processing
            setTimeout(async () => {
              await autoStopStream(streamId, autoStopDecision.reason);
            }, 1000);
          }
        }
      }
    });
    ffmpegProcess.on('exit', async (code, signal) => {
      addStreamLog(streamId, `Stream ended with code ${code}, signal: ${signal}`);
      // console.log(`[FFMPEG_EXIT] ${streamId}: Code=${code}, Signal=${signal}`); // Removed for production
      // No timer to clear - using immediate status update

      // Immediately remove from active streams to prevent status inconsistency
      const wasActive = activeStreams.delete(streamId);
      const isManualStop = manuallyStoppingStreams.has(streamId);

      if (isManualStop) {
        // console.log(`[StreamingService] Stream ${streamId} was manually stopped, not restarting`); // Removed for production
        // Clean up all stream data for manual stops
        cleanupStreamData(streamId);
        if (wasActive) {
          try {
            await Stream.updateStatus(streamId, 'offline');
            if (typeof schedulerService !== 'undefined' && schedulerService.cancelStreamTermination) {
              schedulerService.handleStreamStopped(streamId);
            }
          } catch (error) {
            console.error(`[StreamingService] Error updating stream status after manual stop: ${error.message}`);
          }
        }
        return;
      }
      if (signal === 'SIGSEGV') {
        const retryCount = streamRetryCount.get(streamId) || 0;
        if (retryCount < MAX_RETRY_ATTEMPTS) {
          streamRetryCount.set(streamId, retryCount + 1);
          // console.log(`[StreamingService] FFmpeg crashed with SIGSEGV. Attempting restart #${retryCount + 1} for stream ${streamId}`); // Removed for production
          addStreamLog(streamId, `FFmpeg crashed with SIGSEGV. Attempting restart #${retryCount + 1}`);
          setTimeout(async () => {
            try {
              const streamInfo = await Stream.findById(streamId);
              if (streamInfo) {
                const result = await startStream(streamId);
                if (!result.success) {
                  console.error(`[StreamingService] Failed to restart stream: ${result.error}`);
                  await Stream.updateStatus(streamId, 'offline');
                }
              } else {
                console.error(`[StreamingService] Cannot restart stream ${streamId}: not found in database`);
              }
            } catch (error) {
              console.error(`[StreamingService] Error during stream restart: ${error.message}`);
              try {
                await Stream.updateStatus(streamId, 'offline');
              } catch (dbError) {
                console.error(`Error updating stream status: ${dbError.message}`);
              }
            }
          }, 3000);
          return;
        } else {
          console.error(`[StreamingService] Maximum retry attempts (${MAX_RETRY_ATTEMPTS}) reached for stream ${streamId}`);
          addStreamLog(streamId, `Maximum retry attempts (${MAX_RETRY_ATTEMPTS}) reached, stopping stream`);
        }
      }
      else {
        let errorMessage = '';
        if (code !== 0 && code !== null) {
          errorMessage = `FFmpeg process exited with error code ${code}`;
          addStreamLog(streamId, errorMessage);
          console.error(`[StreamingService] ${errorMessage} for stream ${streamId}`);

          // Check if stream should be auto-stopped due to repeated failures
          const autoStopDecision = shouldAutoStopStream(streamId, errorMessage);

          if (autoStopDecision.shouldStop) {
            // console.log(`[StreamingService] Auto-stopping stream ${streamId}: ${autoStopDecision.reason}`); // Removed for production
            await autoStopStream(streamId, autoStopDecision.reason);
            return;
          }

          const retryCount = streamRetryCount.get(streamId) || 0;
          if (retryCount < MAX_RETRY_ATTEMPTS) {
            streamRetryCount.set(streamId, retryCount + 1);
            // console.log(`[StreamingService] FFmpeg exited with code ${code}. Attempting restart #${retryCount + 1} for stream ${streamId}`); // Removed for production
            setTimeout(async () => {
              try {
                const streamInfo = await Stream.findById(streamId);
                if (streamInfo) {
                  const result = await startStream(streamId);
                  if (!result.success) {
                    console.error(`[StreamingService] Failed to restart stream: ${result.error}`);
                    await Stream.updateStatus(streamId, 'offline');
                  }
                }
              } catch (error) {
                console.error(`[StreamingService] Error during stream restart: ${error.message}`);
                await Stream.updateStatus(streamId, 'offline');
              }
            }, 3000);
            return;
          } else {
            // Max retries reached, auto-stop the stream
            console.error(`[StreamingService] Maximum retry attempts (${MAX_RETRY_ATTEMPTS}) reached for stream ${streamId}`);
            await autoStopStream(streamId, `Maximum retry attempts reached (${MAX_RETRY_ATTEMPTS})`);
          }
        }
        if (wasActive) {
          try {
            // Get stream info for proper status update with user_id
            const stream = await Stream.findById(streamId);
            // console.log(`[StreamingService] Updating stream ${streamId} status to offline after FFmpeg exit`); // Removed for production
            if (stream) {
              await Stream.updateStatus(streamId, 'offline', stream.user_id);
              // console.log(`[StreamingService] ✅ Successfully updated stream ${streamId} status to offline`); // Removed for production
            } else {
              // console.log(`[StreamingService] ⚠️ Stream ${streamId} not found in database, cannot update status`); // Removed for production
            }

            if (typeof schedulerService !== 'undefined' && schedulerService.handleStreamStopped) {
              schedulerService.handleStreamStopped(streamId);
            }
          } catch (error) {
            console.error(`[StreamingService] ❌ Error updating stream status after exit: ${error.message}`);
          }
        }
      }
    });
    ffmpegProcess.on('error', async (err) => {
      addStreamLog(streamId, `Error in stream process: ${err.message}`);
      console.error(`[FFMPEG_PROCESS_ERROR] ${streamId}: ${err.message}`);

      // No timer to clear - using immediate status update

      // Immediately remove from active streams to prevent status inconsistency
      activeStreams.delete(streamId);

      // Clean up stream data on error
      cleanupStreamData(streamId);

      try {
        // Get stream info for proper status update with user_id
        const stream = await Stream.findById(streamId);
        // console.log(`[StreamingService] Updating stream ${streamId} status to offline after process error`); // Removed for production
        if (stream) {
          await Stream.updateStatus(streamId, 'offline', stream.user_id);
          // console.log(`[StreamingService] ✅ Successfully updated stream ${streamId} status to offline after error`); // Removed for production
        } else {
          // console.log(`[StreamingService] ⚠️ Stream ${streamId} not found in database during error handling`); // Removed for production
        }
      } catch (error) {
        console.error(`[StreamingService] ❌ Error updating stream status after process error: ${error.message}`);
      }
    });
    // Removed ffmpegProcess.unref() to ensure proper process management
    if (stream.duration && typeof schedulerService !== 'undefined') {
      schedulerService.scheduleStreamTermination(streamId, stream.duration);
    }
    return {
      success: true,
      message: 'Stream started successfully',
      isAdvancedMode: stream.use_advanced_settings
    };
  } catch (error) {
    // Enhanced error logging with context
    const errorContext = {
      streamId,
      operation: 'startStream',
      timestamp: new Date().toISOString()
    };

    logError(error, null, errorContext);
    addStreamLog(streamId, `Failed to start stream: ${error.message}`);

    // Clean up any partial state
    activeStreams.delete(streamId);
    streamRetryCount.delete(streamId);

    // Return structured error response
    return {
      success: false,
      error: error.message,
      errorType: error.type || 'STREAMING_ERROR',
      errorId: error.errorId
    };
  }
}
async function stopStream(streamId) {
  try {
    const ffmpegProcess = activeStreams.get(streamId);
    const isActive = ffmpegProcess !== undefined;
    // console.log(`[StreamingService] Stop request for stream ${streamId}, isActive: ${isActive}`); // Removed for production
    if (!isActive) {
      const stream = await Stream.findById(streamId);
      if (stream && stream.status === 'live') {
        // console.log(`[StreamingService] Stream ${streamId} not active in memory but status is 'live' in DB. Fixing status.`); // Removed for production
        await Stream.updateStatus(streamId, 'offline', stream.user_id);
        if (typeof schedulerService !== 'undefined' && schedulerService.cancelStreamTermination) {
          schedulerService.handleStreamStopped(streamId);
        }
        return { success: true, message: 'Stream status fixed (was not active but marked as live)' };
      }
      return { success: false, error: 'Stream is not active' };
    }
    addStreamLog(streamId, 'Stopping stream...');
    // console.log(`[StreamingService] Stopping active stream ${streamId}`); // Removed for production
    manuallyStoppingStreams.add(streamId);

    // Enhanced process termination with timeout and fallback to SIGKILL
    try {
      // console.log(`[StreamingService] Sending SIGTERM to FFmpeg process for stream ${streamId}`); // Removed for production
      ffmpegProcess.kill('SIGTERM');

      // Set a timeout to force kill if SIGTERM doesn't work
      const forceKillTimeout = setTimeout(() => {
        if (activeStreams.has(streamId)) {
          // console.log(`[StreamingService] SIGTERM timeout, sending SIGKILL to FFmpeg process for stream ${streamId}`); // Removed for production
          try {
            ffmpegProcess.kill('SIGKILL');
          } catch (forceKillError) {
            console.error(`[StreamingService] Error force killing FFmpeg process: ${forceKillError.message}`);
          }
        }
      }, 5000); // 5 second timeout

      // Clear timeout if process exits normally
      ffmpegProcess.once('exit', () => {
        clearTimeout(forceKillTimeout);
      });

    } catch (killError) {
      console.error(`[StreamingService] Error killing FFmpeg process: ${killError.message}`);
      manuallyStoppingStreams.delete(streamId);
    }
    const stream = await Stream.findById(streamId);
    activeStreams.delete(streamId);
    if (stream) {
      await Stream.updateStatus(streamId, 'offline', stream.user_id);
      const updatedStream = await Stream.findById(streamId);
      await saveStreamHistory(updatedStream);

      // Send notification for stream stopped
      try {
        await notificationService.notifyStreamStopped(streamId, stream.user_id, stream.title);
      } catch (notifError) {
        console.error('Error sending stream stop notification:', notifError);
      }
    }
    if (typeof schedulerService !== 'undefined' && schedulerService.cancelStreamTermination) {
      schedulerService.handleStreamStopped(streamId);
    }
    return { success: true, message: 'Stream stopped successfully' };
  } catch (error) {
    manuallyStoppingStreams.delete(streamId);
    console.error(`[StreamingService] Error stopping stream ${streamId}:`, error);
    return { success: false, error: error.message };
  }
}
async function syncStreamStatuses() {
  try {
    // console.log('[StreamingService] Syncing stream statuses...'); // Removed for production
    const liveStreams = await Stream.findAll(null, 'live');
    let fixedStreams = 0;
    let validatedStreams = 0;

    // First pass: Check streams marked as 'live' in database
    for (const stream of liveStreams) {
      const isInMemory = activeStreams.has(stream.id);

      if (!isInMemory) {
        // Stream is marked live in DB but not in memory - definitely inconsistent
        // console.log(`[StreamingService] INCONSISTENT: Stream ${stream.id} marked 'live' in DB but not in memory`); // Removed for production
        try {
          await Stream.updateStatus(stream.id, 'offline', stream.user_id);
          // console.log(`[StreamingService] ✅ Fixed: Updated stream ${stream.id} status to 'offline'`); // Removed for production
          fixedStreams++;
        } catch (error) {
          console.error(`[StreamingService] ❌ Error updating stream ${stream.id} to offline:`, error.message);
        }
      } else {
        // Stream is in memory, validate if process is actually running
        const isReallyActive = validateStreamProcess(stream.id);

        if (!isReallyActive) {
          // Process is dead but still in memory - this should have been cleaned up by validateStreamProcess
          // console.log(`[StreamingService] INCONSISTENT: Stream ${stream.id} in memory but process is dead`); // Removed for production
          try {
            await Stream.updateStatus(stream.id, 'offline', stream.user_id);
            // console.log(`[StreamingService] ✅ Fixed: Updated dead stream ${stream.id} status to 'offline'`); // Removed for production
            fixedStreams++;
          } catch (error) {
            console.error(`[StreamingService] ❌ Error updating dead stream ${stream.id} to offline:`, error.message);
          }
        } else {
          validatedStreams++;
        }
      }
    }

    // Second pass: Check streams in memory but not marked as 'live' in database
    const activeStreamIds = Array.from(activeStreams.keys());
    for (const streamId of activeStreamIds) {
      // First validate the process is still running
      const isValidProcess = validateStreamProcess(streamId);

      if (!isValidProcess) {
        // console.log(`[StreamingService] Process ${streamId} is no longer valid, already cleaned up by validateStreamProcess`); // Removed for production
        continue;
      }

      try {
        const stream = await Stream.findById(streamId);
        if (!stream) {
          // console.log(`[StreamingService] ORPHANED: Stream ${streamId} active in memory but not found in DB`); // Removed for production
          const process = activeStreams.get(streamId);
          if (process) {
            try {
              // console.log(`[StreamingService] Killing orphaned process for stream ${streamId}`); // Removed for production
              process.kill('SIGTERM');
              setTimeout(() => {
                if (!process.killed) {
                  // console.log(`[StreamingService] Force killing orphaned process for stream ${streamId}`); // Removed for production
                  process.kill('SIGKILL');
                }
              }, 5000);
            } catch (error) {
              console.error(`[StreamingService] Error killing orphaned process ${streamId}:`, error.message);
            }
          }
          activeStreams.delete(streamId);
          cleanupStreamData(streamId);
        } else if (stream.status !== 'live') {
          // console.log(`[StreamingService] INCONSISTENT: Stream ${streamId} active in memory but status is '${stream.status}' in DB`); // Removed for production
          try {
            await Stream.updateStatus(streamId, 'live', stream.user_id);
            // console.log(`[StreamingService] ✅ Fixed: Updated stream ${streamId} status to 'live'`); // Removed for production
            fixedStreams++;
          } catch (error) {
            console.error(`[StreamingService] ❌ Error updating stream ${streamId} to live:`, error.message);
          }
        }
      } catch (error) {
        console.error(`[StreamingService] Error checking stream ${streamId}:`, error.message);
      }
    }

    const finalActiveCount = Array.from(activeStreams.keys()).length;
    // console.log(`[StreamingService] ✅ Stream status sync completed:`); // Removed for production
    // console.log(`   - Active streams in memory: ${finalActiveCount}`); // Removed for production
    // console.log(`   - Validated streams: ${validatedStreams}`); // Removed for production
    // console.log(`   - Fixed inconsistencies: ${fixedStreams}`); // Removed for production
    // Log current active streams for debugging
    if (finalActiveCount > 0) {
      const activeIds = Array.from(activeStreams.keys());
      console.log(`   - Active stream IDs: ${activeIds.join(', ')}`);
    }
  } catch (error) {
    console.error('[StreamingService] ❌ Error syncing stream statuses:', error);
  }
}

// Gentle initial sync after 30 seconds delay
// console.log('[StreamingService] Scheduling gentle initial sync in 30 seconds...'); // Removed for production
setTimeout(() => {
  syncStreamStatuses().then(() => {
    // console.log('[StreamingService] Initial gentle sync completed'); // Removed for production
  }).catch(error => {
    console.error('[StreamingService] Error during initial sync:', error);
  });
}, 30000); // 30 second delay

// Start periodic memory cleanup
// console.log('[StreamingService] Starting periodic memory cleanup...'); // Removed for production
const cleanupTimer = setInterval(() => {
  try {
    performPeriodicCleanup();
  } catch (error) {
    console.error('[StreamingService] Error during periodic cleanup:', error);
  }
}, CLEANUP_INTERVAL);

// Ensure cleanup timer doesn't prevent process exit
cleanupTimer.unref();

// Gentle sync every 30 minutes to prevent conflicts and reduce status flickering
setInterval(syncStreamStatuses, 30 * 60 * 1000); // 30 minutes
console.log('[StreamingService] ✅ Gentle sync enabled (30 minute intervals)');
function isStreamActive(streamId) {
  return activeStreams.has(streamId);
}

// Simplified function to validate if a stream is actually running
function validateStreamProcess(streamId) {
  const process = activeStreams.get(streamId);
  if (!process) {
    // console.log(`[StreamingService] 🔍 Process ${streamId} not found in activeStreams`); // Removed for production
    return false;
  }

  try {
    // Basic validation - check if process is still alive
    if (process.killed) {
      // console.log(`[StreamingService] 💀 Process ${streamId} is marked as killed`); // Removed for production
      activeStreams.delete(streamId);
      cleanupStreamData(streamId);
      return false;
    }

    if (process.exitCode !== null) {
      // console.log(`[StreamingService] 🚪 Process ${streamId} has exit code: ${process.exitCode}`); // Removed for production
      activeStreams.delete(streamId);
      cleanupStreamData(streamId);
      return false;
    }

    // Check if process has a valid PID
    if (!process.pid) {
      // console.log(`[StreamingService] 🆔 Process ${streamId} has no PID`); // Removed for production
      activeStreams.delete(streamId);
      cleanupStreamData(streamId);
      return false;
    }

    // For new processes, be more lenient
    const processStartTime = process.startTime || Date.now();
    const timeSinceStart = Date.now() - processStartTime;

    if (timeSinceStart < 5000) {
      // Process is very new, consider it valid (increased to 5 seconds for stability)
      return true;
    }

    // For older processes, we can be more strict but let's keep it simple
    return true;

  } catch (error) {
    console.error(`[StreamingService] ❌ Error validating process ${streamId}:`, error.message);
    // Don't automatically delete on error - might be temporary
    return false;
  }
}

// Function to force cleanup orphaned FFmpeg processes
async function forceCleanupOrphanedProcesses() {
  const { exec } = require('child_process');
  const os = require('os');

  try {
    // console.log('[StreamingService] Checking for orphaned FFmpeg processes...'); // Removed for production
    if (os.platform() === 'win32') {
      // Windows - check for FFmpeg processes
      exec('tasklist /FI "IMAGENAME eq ffmpeg.exe" /FO CSV', (error, stdout) => {
        if (!error && stdout) {
          const lines = stdout.split('\n');
          const ffmpegProcesses = lines.filter(line => line.includes('ffmpeg.exe'));

          if (ffmpegProcesses.length > 1) { // More than header line
            // console.log(`[StreamingService] Found ${ffmpegProcesses.length - 1} FFmpeg processes, cleaning up...`); // Removed for production
            exec('taskkill /F /IM ffmpeg.exe', (killError) => {
              if (killError) {
                console.error('[StreamingService] Error killing orphaned FFmpeg processes:', killError.message);
              } else {
                // console.log('[StreamingService] Orphaned FFmpeg processes cleaned up'); // Removed for production
              }
            });
          }
        }
      });
    } else {
      // Linux/Mac - check for FFmpeg processes
      exec('pgrep -f ffmpeg', (error, stdout) => {
        if (!error && stdout) {
          const pids = stdout.trim().split('\n').filter(pid => pid);
          if (pids.length > 0) {
            // console.log(`[StreamingService] Found ${pids.length} FFmpeg processes, cleaning up...`); // Removed for production
            exec(`kill -9 ${pids.join(' ')}`, (killError) => {
              if (killError) {
                console.error('[StreamingService] Error killing orphaned FFmpeg processes:', killError.message);
              } else {
                // console.log('[StreamingService] Orphaned FFmpeg processes cleaned up'); // Removed for production
              }
            });
          }
        }
      });
    }
  } catch (error) {
    console.error('[StreamingService] Error during orphaned process cleanup:', error);
  }
}
function getActiveStreams() {
  return Array.from(activeStreams.keys());
}
function getStreamLogs(streamId) {
  const logs = streamLogs.get(streamId) || [];
  // Ensure all logs are strings to prevent "[object Object]" display
  return logs.map(log => typeof log === 'string' ? log : JSON.stringify(log));
}

// Function to track stream failures
function trackStreamFailure(streamId, errorMessage) {
  const now = Date.now();

  if (!streamFailureTimestamps.has(streamId)) {
    streamFailureTimestamps.set(streamId, []);
  }

  const failures = streamFailureTimestamps.get(streamId);
  failures.push({ timestamp: now, error: errorMessage });

  // Keep only failures within the time window
  const windowStart = now - (AUTO_STOP_CONFIG.FAILURE_WINDOW_MINUTES * 60 * 1000);
  const recentFailures = failures.filter(f => f.timestamp >= windowStart);
  streamFailureTimestamps.set(streamId, recentFailures);

  return recentFailures;
}

// Function to analyze if stream should be auto-stopped
function shouldAutoStopStream(streamId, errorMessage) {
  const recentFailures = trackStreamFailure(streamId, errorMessage);

  // Check for immediate stop conditions
  const ioErrors = recentFailures.filter(f =>
    f.error.includes('I/O error') ||
    f.error.includes('Connection refused') ||
    f.error.includes('Network is unreachable')
  ).length;

  const connectionErrors = recentFailures.filter(f =>
    f.error.includes('Connection') ||
    f.error.includes('timeout') ||
    f.error.includes('refused')
  ).length;

  // Immediate stop conditions
  if (ioErrors >= AUTO_STOP_CONFIG.I_O_ERROR_THRESHOLD) {
    console.log(`[StreamingService] Auto-stopping stream ${streamId}: Too many I/O errors (${ioErrors})`);
    return { shouldStop: true, reason: `Too many I/O errors (${ioErrors}/${AUTO_STOP_CONFIG.I_O_ERROR_THRESHOLD})` };
  }

  if (connectionErrors >= AUTO_STOP_CONFIG.CONNECTION_ERROR_THRESHOLD) {
    console.log(`[StreamingService] Auto-stopping stream ${streamId}: Too many connection errors (${connectionErrors})`);
    return { shouldStop: true, reason: `Too many connection errors (${connectionErrors}/${AUTO_STOP_CONFIG.CONNECTION_ERROR_THRESHOLD})` };
  }

  // General failure threshold
  if (recentFailures.length >= AUTO_STOP_CONFIG.MAX_CONSECUTIVE_FAILURES) {
    console.log(`[StreamingService] Auto-stopping stream ${streamId}: Too many consecutive failures (${recentFailures.length})`);
    return { shouldStop: true, reason: `Too many consecutive failures (${recentFailures.length}/${AUTO_STOP_CONFIG.MAX_CONSECUTIVE_FAILURES})` };
  }

  return { shouldStop: false, reason: null };
}

// Function to auto-stop a problematic stream
async function autoStopStream(streamId, reason) {
  try {
    // console.log(`[StreamingService] Auto-stopping stream ${streamId}: ${reason}`); // Removed for production
    // Add to failed streams list
    failedStreams.add(streamId);

    // Stop the stream
    await stopStream(streamId);

    // Add log entry
    addStreamLog(streamId, `Stream auto-stopped: ${reason}`);
    addStreamLog(streamId, `Stream has been temporarily disabled. Check your RTMP settings and clear failed status to retry.`);

    // Update stream status to "error" instead of "offline"
    const stream = await Stream.findById(streamId);
    if (stream) {
      await Stream.updateStatus(streamId, 'error', stream.user_id, reason);
      // console.log(`[StreamingService] Stream ${streamId} status set to 'error': ${reason}`); // Removed for production
    }

    // Clear retry count
    streamRetryCount.delete(streamId);

    // Remove from blacklist after 15 minutes
    setTimeout(() => {
      failedStreams.delete(streamId);
      streamFailureTimestamps.delete(streamId);
      // console.log(`[StreamingService] Stream ${streamId} removed from failed streams list after cooldown`); // Removed for production
    }, 15 * 60 * 1000); // 15 minutes

    return true;
  } catch (error) {
    console.error(`[StreamingService] Error auto-stopping stream ${streamId}:`, error);
    return false;
  }
}

// Function to clear failed streams (for manual recovery)
async function clearFailedStream(streamId) {
  const wasBlacklisted = failedStreams.has(streamId);

  // Use centralized cleanup function
  cleanupStreamData(streamId);

  // Reset status from "error" to "offline" when clearing failed status
  try {
    const stream = await Stream.findById(streamId);
    if (stream && stream.status === 'error') {
      await Stream.updateStatus(streamId, 'offline', stream.user_id);
      // console.log(`[StreamingService] Reset stream ${streamId} status from 'error' to 'offline'`); // Removed for production
    }
  } catch (error) {
    console.error(`[StreamingService] Error resetting stream status: ${error.message}`);
  }

  // console.log(`[StreamingService] Cleared failed status for stream ${streamId}, was blacklisted: ${wasBlacklisted}`); // Removed for production
  return wasBlacklisted;
}
async function saveStreamHistory(stream) {
  try {
    if (!stream.start_time) {
      // console.log(`[StreamingService] Not saving history for stream ${stream.id} - no start time recorded`); // Removed for production
      return false;
    }
    const startTime = new Date(stream.start_time);
    const endTime = stream.end_time ? new Date(stream.end_time) : new Date();
    const durationSeconds = Math.floor((endTime - startTime) / 1000);
    if (durationSeconds < 5) { // Changed from 1 to 5 seconds minimum
      console.log(`[StreamingService] Not saving history for stream ${stream.id} - duration too short (${durationSeconds}s)`);
      return false;
    }

    // Get video details with error handling
    let videoDetails = null;
    if (stream.video_id) {
      try {
        videoDetails = await Video.findById(stream.video_id);
      } catch (error) {
        console.warn(`[StreamingService] Could not fetch video details for video_id ${stream.video_id}:`, error.message);
      }
    }

    const historyData = {
      id: uuidv4(),
      stream_id: stream.id,
      title: stream.title || 'Untitled Stream',
      platform: stream.platform || 'Custom',
      platform_icon: stream.platform_icon || 'ti-broadcast',
      video_id: stream.video_id,
      video_title: videoDetails ? videoDetails.title : null,
      resolution: stream.resolution || '1280x720',
      bitrate: stream.bitrate || 2500,
      fps: stream.fps || 30,
      start_time: stream.start_time,
      end_time: stream.end_time || new Date().toISOString(),
      duration: durationSeconds,
      use_advanced_settings: stream.use_advanced_settings ? 1 : 0,
      user_id: stream.user_id
    };

    return new Promise((resolve, reject) => {
      const { db } = require('../db/database');
      db.run(
        `INSERT INTO stream_history (
          id, stream_id, title, platform, platform_icon, video_id, video_title,
          resolution, bitrate, fps, start_time, end_time, duration, use_advanced_settings, user_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          historyData.id, historyData.stream_id, historyData.title,
          historyData.platform, historyData.platform_icon, historyData.video_id, historyData.video_title,
          historyData.resolution, historyData.bitrate, historyData.fps,
          historyData.start_time, historyData.end_time, historyData.duration,
          historyData.use_advanced_settings, historyData.user_id
        ],
        function (err) {
          if (err) {
            console.error('[StreamingService] Error saving stream history:', err.message);
            return reject(err);
          }
          console.log(`[StreamingService] Stream history saved for stream ${stream.id}, duration: ${Math.floor(durationSeconds/60)}m ${durationSeconds%60}s`);
          resolve(historyData);
        }
      );
    });
  } catch (error) {
    console.error('[StreamingService] Failed to save stream history:', error);
    return false;
  }
}
module.exports = {
  startStream,
  stopStream,
  isStreamActive,
  validateStreamProcess,
  getActiveStreams,
  getStreamLogs,
  syncStreamStatuses,
  saveStreamHistory,
  restoreActiveStreams,
  cleanupOrphanedStreams: restoreActiveStreams, // Alias for the cleanup function
  clearFailedStream,
  forceCleanupOrphanedProcesses,
  // Memory management functions
  cleanupStreamData,
  performPeriodicCleanup,
  // Load balancing functions
  applyLoadBalancing,
  getLoadBalanceStatus,
  setLoadBalancingEnabled: (enabled) => { loadBalancingEnabled = enabled; },
  updateLoadBalanceConfig: (config) => {
    if (config.thresholds) Object.assign(LOAD_BALANCE_CONFIG.CPU_THRESHOLDS, config.thresholds);
    if (config.qualityPresets) Object.assign(LOAD_BALANCE_CONFIG.QUALITY_PRESETS, config.qualityPresets);
    if (config.checkInterval) LOAD_BALANCE_CONFIG.CPU_CHECK_INTERVAL = config.checkInterval;
    if (config.cooldown) LOAD_BALANCE_CONFIG.QUALITY_CHANGE_COOLDOWN = config.cooldown;
  },
  // Copy mode compatibility functions
  getCopyModeCompatibleSettings,
  needsReencoding
};
schedulerService.init(module.exports);