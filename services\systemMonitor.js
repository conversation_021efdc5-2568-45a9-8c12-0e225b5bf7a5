const si = require('systeminformation');
async function getSystemStats() {
  try {
    const [cpuData, memData, diskData] = await Promise.all([
      si.currentLoad(),
      si.mem(),
      si.fsSize()
    ]);
    const cpuUsage = cpuData.currentLoad || cpuData.avg || 0;
    const formatMemory = (bytes) => {
      if (bytes >= 1073741824) {
        return (bytes / 1073741824).toFixed(2) + " GB";
      } else {
        return (bytes / 1048576).toFixed(2) + " MB";
      }
    };

    // Calculate total disk usage (sum of all mounted filesystems)
    let totalDiskSize = 0;
    let totalDiskUsed = 0;
    let totalDiskFree = 0;

    if (diskData && diskData.length > 0) {
      diskData.forEach(disk => {
        // Only include main filesystems (exclude virtual/temporary mounts)
        if (disk.mount === '/' || disk.mount === 'C:' || disk.mount.includes(':\\') ||
            (!disk.mount.includes('/snap/') && !disk.mount.includes('/dev/') &&
             !disk.mount.includes('/sys/') && !disk.mount.includes('/proc/'))) {
          totalDiskSize += disk.size || 0;
          totalDiskUsed += disk.used || 0;
          totalDiskFree += disk.available || 0;
        }
      });
    }
    return {
      cpu: {
        usage: Math.round(cpuUsage),
        cores: cpuData.cpus ? cpuData.cpus.length : 0
      },
      memory: {
        total: formatMemory(memData.total),
        used: formatMemory(memData.active),
        free: formatMemory(memData.available),
        usagePercent: Math.round((memData.active / memData.total) * 100)
      },
      storage: {
        total: formatMemory(totalDiskSize),
        used: formatMemory(totalDiskUsed),
        free: formatMemory(totalDiskFree),
        usagePercent: totalDiskSize > 0 ? Math.round((totalDiskUsed / totalDiskSize) * 100) : 0
      },
      platform: process.platform,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('Error getting system stats:', error);
    return {
      cpu: { usage: 0, cores: 0 },
      memory: { total: "0 GB", used: "0 GB", free: "0 GB", usagePercent: 0 },
      storage: { total: "0 GB", used: "0 GB", free: "0 GB", usagePercent: 0 },
      platform: process.platform,
      timestamp: Date.now()
    };
  }
}
module.exports = { getSystemStats };