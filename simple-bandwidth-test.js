// Simple Bandwidth Test - StreamOnPod
// Alternative bandwidth test without external dependencies

const https = require('https');
const http = require('http');

console.log('🌐 Simple VPS Bandwidth Test\n');

// Test 1: Download speed test using a known file
function testDownloadSpeed() {
  return new Promise((resolve) => {
    console.log('📥 Testing Download Speed...');
    
    const testUrl = 'http://speedtest.ftp.otenet.gr/files/test1Mb.db'; // 1MB test file
    const startTime = Date.now();
    
    const req = http.get(testUrl, (res) => {
      let totalBytes = 0;
      
      res.on('data', (chunk) => {
        totalBytes += chunk.length;
      });
      
      res.on('end', () => {
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000; // seconds
        const speedMbps = (totalBytes * 8) / (duration * 1000000); // Mbps
        
        console.log(`   📊 Downloaded: ${(totalBytes / 1024 / 1024).toFixed(2)} MB`);
        console.log(`   ⏱️  Duration: ${duration.toFixed(2)} seconds`);
        console.log(`   📈 Download Speed: ${speedMbps.toFixed(2)} Mbps`);
        resolve(speedMbps);
      });
      
      res.on('error', () => {
        console.log('   ❌ Download test failed');
        resolve(0);
      });
    });
    
    req.on('error', () => {
      console.log('   ❌ Download test failed');
      resolve(0);
    });
    
    req.setTimeout(30000, () => {
      console.log('   ⏱️  Download test timeout');
      req.destroy();
      resolve(0);
    });
  });
}

// Test 2: Simple ping test to major servers
function testLatency() {
  return new Promise((resolve) => {
    console.log('\n🏓 Testing Latency to Major Servers...');
    
    const servers = [
      { name: 'Google DNS', host: '*******', port: 53 },
      { name: 'Cloudflare', host: '*******', port: 53 },
      { name: 'YouTube', host: 'youtube.com', port: 443 }
    ];
    
    let completed = 0;
    const results = [];
    
    servers.forEach(server => {
      const start = Date.now();
      const socket = new (require('net')).Socket();
      
      socket.setTimeout(5000);
      
      socket.connect(server.port, server.host, () => {
        const latency = Date.now() - start;
        console.log(`   ✅ ${server.name}: ${latency}ms`);
        results.push(latency);
        socket.destroy();
        completed++;
        if (completed === servers.length) resolve(results);
      });
      
      socket.on('error', () => {
        console.log(`   ❌ ${server.name}: Connection failed`);
        completed++;
        if (completed === servers.length) resolve(results);
      });
      
      socket.on('timeout', () => {
        console.log(`   ⏱️  ${server.name}: Timeout`);
        socket.destroy();
        completed++;
        if (completed === servers.length) resolve(results);
      });
    });
  });
}

// Test 3: Check current system resources
function checkSystemResources() {
  console.log('\n💻 System Resources:');
  
  // Memory usage
  const memUsage = process.memoryUsage();
  console.log(`   🧠 Memory: ${(memUsage.rss / 1024 / 1024).toFixed(2)} MB used`);
  
  // CPU load (approximate)
  const loadavg = require('os').loadavg();
  console.log(`   ⚡ CPU Load: ${loadavg[0].toFixed(2)} (1min avg)`);
  
  // Network interfaces
  const networkInterfaces = require('os').networkInterfaces();
  console.log('   🌐 Network Interfaces:');
  Object.keys(networkInterfaces).forEach(name => {
    const interfaces = networkInterfaces[name];
    interfaces.forEach(iface => {
      if (iface.family === 'IPv4' && !iface.internal) {
        console.log(`      ${name}: ${iface.address}`);
      }
    });
  });
}

// Test 4: Estimate bandwidth requirements
function analyzeBandwidthRequirements(downloadSpeed) {
  console.log('\n🎯 Bandwidth Analysis for Streaming:');
  
  // Estimate upload speed (usually 10-20% of download for most connections)
  const estimatedUpload = downloadSpeed * 0.15; // Conservative estimate
  
  console.log(`   📊 Estimated Upload Speed: ~${estimatedUpload.toFixed(2)} Mbps`);
  console.log('   (Note: This is a rough estimate based on download speed)');
  
  console.log('\n📋 Streaming Recommendations:');
  
  if (estimatedUpload >= 15) {
    console.log('   ✅ EXCELLENT: 10000+ kbps streaming recommended');
    console.log('   🎯 Suggested bitrate: 10000-12000 kbps');
  } else if (estimatedUpload >= 12) {
    console.log('   ✅ GOOD: 10000 kbps streaming should work');
    console.log('   🎯 Suggested bitrate: 8000-10000 kbps');
  } else if (estimatedUpload >= 9) {
    console.log('   ⚠️  MODERATE: 6800 kbps recommended for stability');
    console.log('   🎯 Suggested bitrate: 6800-8000 kbps');
  } else if (estimatedUpload >= 6) {
    console.log('   ⚠️  LIMITED: Lower bitrate recommended');
    console.log('   🎯 Suggested bitrate: 4000-6000 kbps');
  } else {
    console.log('   ❌ POOR: High bitrate streaming not recommended');
    console.log('   🎯 Suggested bitrate: 2500-4000 kbps');
  }
  
  return estimatedUpload;
}

// Test 5: Provide troubleshooting steps
function provideTroubleshootingSteps(estimatedUpload, latencyResults) {
  console.log('\n🔧 TROUBLESHOOTING STEPS:\n');
  
  if (estimatedUpload < 12) {
    console.log('❌ BANDWIDTH ISSUE DETECTED:');
    console.log('   1. Your VPS may not have sufficient upload bandwidth');
    console.log('   2. Try lowering bitrate to 6800 kbps first');
    console.log('   3. Test streaming with lower bitrate');
    console.log('   4. Contact VPS provider about bandwidth limits');
  }
  
  const avgLatency = latencyResults.length > 0 ? 
    latencyResults.reduce((a, b) => a + b, 0) / latencyResults.length : 0;
  
  if (avgLatency > 100) {
    console.log('\n❌ LATENCY ISSUE DETECTED:');
    console.log('   1. High latency may cause streaming instability');
    console.log('   2. Check VPS location vs YouTube servers');
    console.log('   3. Consider VPS in different region');
  }
  
  console.log('\n✅ IMMEDIATE ACTIONS:');
  console.log('   1. Set bitrate to 6800 kbps in StreamOnPod');
  console.log('   2. Test streaming for 5-10 minutes');
  console.log('   3. Check YouTube Studio for received bitrate');
  console.log('   4. If stable, gradually increase bitrate');
  
  console.log('\n📞 VPS PROVIDER QUESTIONS:');
  console.log('   • What is the guaranteed upload bandwidth?');
  console.log('   • Are there any bandwidth limits or throttling?');
  console.log('   • What is the network location/data center?');
  console.log('   • Is the connection shared or dedicated?');
}

// Run all tests
async function runAllTests() {
  try {
    checkSystemResources();
    
    const downloadSpeed = await testDownloadSpeed();
    const latencyResults = await testLatency();
    
    const estimatedUpload = analyzeBandwidthRequirements(downloadSpeed);
    provideTroubleshootingSteps(estimatedUpload, latencyResults);
    
    console.log('\n🎉 TEST COMPLETE!');
    console.log('\nNext: Try streaming with 6800 kbps and monitor YouTube Studio');
    
  } catch (error) {
    console.error('Error running tests:', error.message);
  }
}

runAllTests();
