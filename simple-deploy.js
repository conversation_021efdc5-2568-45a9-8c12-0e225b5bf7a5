#!/usr/bin/env node

/**
 * Simple Deployment Script for StreamOnPod
 * 
 * Simplified deployment process:
 * 1. Basic system checks
 * 2. Deploy to production
 * 3. Start monitoring
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Load environment variables
require('dotenv').config();

console.log('🚀 StreamOnPod Simple Deployment\n');

// Configuration
const CONFIG = {
  LOG_FILE: './logs/simple-deployment.log',
  PRODUCTION_ENV: '.env.production',
  CURRENT_ENV: '.env'
};

// Utility functions
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] [${level}] ${message}`;
  
  console.log(logEntry);
  
  // Ensure log directory exists
  const logDir = path.dirname(CONFIG.LOG_FILE);
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
  
  // Write to log file
  fs.appendFileSync(CONFIG.LOG_FILE, logEntry + '\n');
}

function basicSystemCheck() {
  log('Running basic system checks...', 'INFO');
  
  const checks = [];
  
  // Check essential files
  const essentialFiles = [
    'app.js',
    'package.json',
    'db/database.js',
    'services/streamingService.js'
  ];
  
  essentialFiles.forEach(file => {
    const exists = fs.existsSync(file);
    checks.push({
      name: `File: ${file}`,
      passed: exists,
      message: exists ? 'Found' : 'Missing'
    });
  });
  
  // Check environment variables
  const requiredEnvVars = ['PORT', 'SESSION_SECRET', 'BASE_URL'];
  requiredEnvVars.forEach(varName => {
    const exists = process.env[varName];
    checks.push({
      name: `Env: ${varName}`,
      passed: !!exists,
      message: exists ? 'Set' : 'Missing'
    });
  });
  
  // Display results
  checks.forEach(check => {
    const status = check.passed ? '✅' : '❌';
    log(`${status} ${check.name}: ${check.message}`, check.passed ? 'INFO' : 'WARNING');
  });
  
  const allPassed = checks.every(check => check.passed);
  log(`Basic system check: ${allPassed ? 'PASSED' : 'PASSED WITH WARNINGS'}`, allPassed ? 'SUCCESS' : 'WARNING');
  
  return true; // Don't fail deployment for warnings
}

function createBackup() {
  log('Creating backup...', 'INFO');
  
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = `./backups/simple-deployment-${timestamp}`;
    
    if (!fs.existsSync('./backups')) {
      fs.mkdirSync('./backups', { recursive: true });
    }
    
    fs.mkdirSync(backupDir, { recursive: true });
    
    // Backup essential files
    const filesToBackup = ['app.js', 'package.json', '.env'];
    filesToBackup.forEach(file => {
      if (fs.existsSync(file)) {
        fs.copyFileSync(file, path.join(backupDir, path.basename(file)));
      }
    });
    
    log(`Backup created: ${backupDir}`, 'SUCCESS');
    return backupDir;
  } catch (error) {
    log(`Backup failed: ${error.message}`, 'WARNING');
    return null;
  }
}

function deployToProduction() {
  log('Deploying to production...', 'INFO');
  
  try {
    // Copy production environment if exists
    if (fs.existsSync(CONFIG.PRODUCTION_ENV)) {
      fs.copyFileSync(CONFIG.PRODUCTION_ENV, CONFIG.CURRENT_ENV);
      log('Production environment configured', 'SUCCESS');
    } else {
      log('No production environment file found, using current .env', 'WARNING');
    }
    
    // Install/update dependencies
    log('Installing dependencies...', 'INFO');
    execSync('npm install', { stdio: 'pipe' });
    log('Dependencies installed', 'SUCCESS');
    
    return true;
  } catch (error) {
    log(`Deployment failed: ${error.message}`, 'ERROR');
    return false;
  }
}

function startMonitoring() {
  log('Starting monitoring systems...', 'INFO');
  
  try {
    const { spawn } = require('child_process');
    
    // Start performance monitoring in background
    const monitor = spawn('node', ['scripts/performance-monitor.js'], {
      detached: true,
      stdio: 'ignore'
    });
    monitor.unref();
    
    log('Performance monitoring started in background', 'SUCCESS');
    return true;
  } catch (error) {
    log(`Failed to start monitoring: ${error.message}`, 'WARNING');
    return false;
  }
}

function displayNextSteps() {
  log('\n🎯 DEPLOYMENT COMPLETE - NEXT STEPS:', 'INFO');
  log('=' .repeat(50), 'INFO');
  
  log('1. Start the application:', 'INFO');
  log('   npm run production', 'INFO');
  log('   # OR with PM2:', 'INFO');
  log('   pm2 start scripts/production-start.js --name streamonpod', 'INFO');
  
  log('\n2. Monitor the application:', 'INFO');
  log('   npm run monitor:performance  # Performance monitoring', 'INFO');
  log('   npm run health:check         # Health check', 'INFO');
  log('   npm run logs:tail           # View logs', 'INFO');
  
  log('\n3. Access the application:', 'INFO');
  log(`   ${process.env.BASE_URL || 'http://localhost:7575'}`, 'INFO');
  
  log('\n4. Troubleshooting:', 'INFO');
  log('   npm run logs:errors         # View error logs', 'INFO');
  log('   npm run validate:production # Validate config', 'INFO');
}

// Main deployment function
async function main() {
  log('Starting simple deployment process...', 'INFO');
  
  const results = {
    systemCheck: false,
    backup: false,
    deployment: false,
    monitoring: false
  };
  
  try {
    // Step 1: Basic system check
    results.systemCheck = basicSystemCheck();
    
    // Step 2: Create backup
    const backupPath = createBackup();
    results.backup = !!backupPath;
    
    // Step 3: Deploy to production
    results.deployment = deployToProduction();
    if (!results.deployment) {
      throw new Error('Production deployment failed');
    }
    
    // Step 4: Start monitoring
    results.monitoring = startMonitoring();
    
    // Success summary
    log('\n🎉 DEPLOYMENT SUCCESSFUL!', 'SUCCESS');
    log('✅ System checks completed', 'SUCCESS');
    log(`✅ Backup ${results.backup ? 'created' : 'attempted'}`, 'SUCCESS');
    log('✅ Production deployment completed', 'SUCCESS');
    log(`✅ Monitoring ${results.monitoring ? 'started' : 'attempted'}`, 'SUCCESS');
    
    displayNextSteps();
    
    log(`\n📄 Deployment log: ${CONFIG.LOG_FILE}`, 'INFO');
    
  } catch (error) {
    log(`\n❌ DEPLOYMENT FAILED: ${error.message}`, 'ERROR');
    log('🔄 Check the logs and try again', 'ERROR');
    log(`📄 Log file: ${CONFIG.LOG_FILE}`, 'ERROR');
    
    process.exit(1);
  }
}

// Run deployment if executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Deployment script error:', error);
    process.exit(1);
  });
}

module.exports = { main, basicSystemCheck, deployToProduction };
