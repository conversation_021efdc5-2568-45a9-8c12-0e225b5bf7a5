// Simple test untuk timezone
const { datetimeLocalToUTC } = require('./utils/timezone');

console.log('=== Simple Timezone Test ===');

// Test: 2 Juni 2025 jam 6:00 WIB
const input = '2025-06-02T06:00';
const timezone = 'Asia/Jakarta';

console.log(`Input: ${input} (${timezone})`);

const result = datetimeLocalToUTC(input, timezone);
console.log(`Result: ${result}`);

if (result) {
  const utcDate = new Date(result);
  console.log(`UTC Date: ${utcDate}`);
  
  // Test display
  const displayDate = utcDate.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    timeZone: timezone
  });
  
  const displayTime = utcDate.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
    timeZone: timezone
  });
  
  console.log(`Display: Starts ${displayDate} • ${displayTime}`);
}

console.log('\nExpected:');
console.log('- UTC should be: 2025-06-01T23:00:00.000Z');
console.log('- Display should be: Starts Jun 2, 2025 • 06:00');
