#!/usr/bin/env node

/**
 * Test script untuk debugging RTMP validation via API
 * Menguji endpoint /api/validate-rtmp
 */

const axios = require('axios');

// Konfigurasi untuk testing
const LOCAL_URL = 'http://localhost:7575';
const TUNNEL_URL = 'https://streamonpod.imthe.one';

// Test data
const testData = {
  rtmpUrl: 'rtmp://a.rtmp.youtube.com/live2',
  streamKey: 'test-stream-key-123'
};

const invalidTestData = {
  rtmpUrl: 'http://invalid-url.com/live',
  streamKey: 'test-key'
};

async function testValidationAPI(baseUrl, testName) {
  console.log(`\n🧪 Testing ${testName} (${baseUrl})`);
  console.log('='.repeat(50));
  
  try {
    // Test 1: Valid RTMP URL
    console.log('\n1. Testing valid RTMP URL:');
    console.log(`   RTMP URL: ${testData.rtmpUrl}`);
    console.log(`   Stream Key: ${testData.streamKey}`);
    
    try {
      const response1 = await axios.post(`${baseUrl}/api/validate-rtmp`, testData, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'StreamOnPod-Test/1.0'
        },
        timeout: 10000,
        validateStatus: function (status) {
          return status < 500; // Accept all status codes below 500
        }
      });
      
      console.log(`   Status: ${response1.status}`);
      console.log(`   Response:`, JSON.stringify(response1.data, null, 2));
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
      if (error.response) {
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Response:`, error.response.data);
      }
    }
    
    // Test 2: Invalid RTMP URL
    console.log('\n2. Testing invalid RTMP URL:');
    console.log(`   RTMP URL: ${invalidTestData.rtmpUrl}`);
    console.log(`   Stream Key: ${invalidTestData.streamKey}`);
    
    try {
      const response2 = await axios.post(`${baseUrl}/api/validate-rtmp`, invalidTestData, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'StreamOnPod-Test/1.0'
        },
        timeout: 10000,
        validateStatus: function (status) {
          return status < 500; // Accept all status codes below 500
        }
      });
      
      console.log(`   Status: ${response2.status}`);
      console.log(`   Response:`, JSON.stringify(response2.data, null, 2));
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
      if (error.response) {
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Response:`, error.response.data);
      }
    }
    
    // Test 3: Empty data
    console.log('\n3. Testing empty data:');
    
    try {
      const response3 = await axios.post(`${baseUrl}/api/validate-rtmp`, {
        rtmpUrl: '',
        streamKey: ''
      }, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'StreamOnPod-Test/1.0'
        },
        timeout: 10000,
        validateStatus: function (status) {
          return status < 500; // Accept all status codes below 500
        }
      });
      
      console.log(`   Status: ${response3.status}`);
      console.log(`   Response:`, JSON.stringify(response3.data, null, 2));
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
      if (error.response) {
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Response:`, error.response.data);
      }
    }
    
  } catch (error) {
    console.log(`❌ General error testing ${testName}: ${error.message}`);
  }
}

async function runTests() {
  console.log('🚀 Starting RTMP Validation API Tests');
  console.log('=====================================');
  
  // Test local endpoint
  await testValidationAPI(LOCAL_URL, 'Local Server');
  
  // Test tunnel endpoint
  await testValidationAPI(TUNNEL_URL, 'Tunnel Server');
  
  console.log('\n✅ All tests completed!');
  console.log('\n📝 Summary:');
  console.log('- If both local and tunnel show same results, the issue is not environment-specific');
  console.log('- If tunnel shows different results, there might be proxy/HTTPS related issues');
  console.log('- Check for authentication errors (401/403) which might indicate session issues');
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Run the tests
runTests().catch(error => {
  console.error('❌ Test execution failed:', error.message);
  process.exit(1);
});
