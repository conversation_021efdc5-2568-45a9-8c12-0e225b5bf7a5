#!/usr/bin/env node

/**
 * Test script untuk Google Drive URL extraction dan debugging
 */

const { extractFileId, createDriveService } = require('./utils/googleDriveService');

console.log('🔍 Testing Google Drive URL extraction');
console.log('=====================================');

const testUrls = [
  'https://drive.google.com/file/d/1BWWoHl-A4-3tKHDApjjizo4PTx7Ort6u/view',
  'https://drive.google.com/file/d/1BWWoHl-A4-3tKHDApjjizo4PTx7Ort6u',
  'https://drive.google.com/open?id=1BWWoHl-A4-3tKHDApjjizo4PTx7Ort6u',
  '1BWWoHl-A4-3tKHDApjjizo4PTx7Ort6u'
];

testUrls.forEach((url, index) => {
  try {
    const fileId = extractFileId(url);
    console.log(`✅ Test ${index + 1}: "${url}"`);
    console.log(`   Extracted File ID: ${fileId}`);
  } catch (error) {
    console.log(`❌ Test ${index + 1}: "${url}"`);
    console.log(`   Error: ${error.message}`);
  }
  console.log('');
});

console.log('Expected File ID: 1BWWoHl-A4-3tKHDApjjizo4PTx7Ort6u');
console.log('');

// Test Google Drive API access (if API key is available)
console.log('🔍 Testing Google Drive API access');
console.log('==================================');

async function testGoogleDriveAccess() {
  const fileId = '1BWWoHl-A4-3tKHDApjjizo4PTx7Ort6u';

  // Check if we have an API key in environment
  const apiKey = process.env.GOOGLE_DRIVE_API_KEY;
  if (!apiKey) {
    console.log('❌ No Google Drive API key found in environment variables');
    console.log('   Set GOOGLE_DRIVE_API_KEY to test API access');
    return;
  }

  try {
    console.log(`🔑 Using API key: ${apiKey.substring(0, 10)}...`);
    const drive = createDriveService(apiKey);

    console.log(`📋 Getting metadata for file: ${fileId}`);
    const fileMetadata = await drive.files.get({
      fileId: fileId,
      fields: 'name,mimeType,size,permissions'
    });

    console.log('✅ File metadata retrieved successfully:');
    console.log(`   Name: ${fileMetadata.data.name}`);
    console.log(`   MIME Type: ${fileMetadata.data.mimeType}`);
    console.log(`   Size: ${fileMetadata.data.size} bytes`);

    if (fileMetadata.data.mimeType.includes('video')) {
      console.log('✅ File is a video - download should work');
    } else {
      console.log('❌ File is not a video');
    }

  } catch (error) {
    console.log('❌ Failed to access Google Drive API:');
    console.log(`   Error: ${error.message}`);

    if (error.code === 403) {
      console.log('   💡 This usually means the file is private or API key lacks permission');
    } else if (error.code === 404) {
      console.log('   💡 File not found - check if the file ID is correct');
    } else if (error.code === 401) {
      console.log('   💡 Authentication failed - check your API key');
    }
  }
}

testGoogleDriveAccess().catch(console.error);
