<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Open Graph Preview - StreamOnPod</title>
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="StreamOnPod - Platform Streaming Cloud untuk Siaran Otomatis">
    <meta property="og:description" content="Ubah video Anda menjadi live stream otomatis dengan StreamOnPod. Platform streaming berbasis cloud untuk siaran konten berkelanjutan di berbagai platform dengan infrastruktur tingkat enterprise.">
    <meta property="og:image" content="https://streamonpod.com/images/streamonpod-logotype.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:alt" content="StreamOnPod - Platform Streaming Cloud">
    <meta property="og:url" content="https://streamonpod.com">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="StreamOnPod">
    <meta property="og:locale" content="id_ID">
    <meta property="og:locale:alternate" content="en_US">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="StreamOnPod - Platform Streaming Cloud untuk Siaran Otomatis">
    <meta name="twitter:description" content="Ubah video Anda menjadi live stream otomatis dengan StreamOnPod. Platform streaming berbasis cloud untuk siaran konten berkelanjutan di berbagai platform dengan infrastruktur tingkat enterprise.">
    <meta name="twitter:image" content="https://streamonpod.com/images/streamonpod-logotype.png">
    <meta name="twitter:image:alt" content="StreamOnPod - Platform Streaming Cloud">
    <meta name="twitter:site" content="@streamonpod">
    <meta name="twitter:creator" content="@streamonpod">
    
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .preview-card {
            border: 1px solid #333;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background: #2a2a2a;
        }
        .image-preview {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-links {
            margin: 20px 0;
        }
        .test-links a {
            display: inline-block;
            margin: 5px 10px 5px 0;
            padding: 10px 15px;
            background: #ad6610;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .test-links a:hover {
            background: #8b5208;
        }
    </style>
</head>
<body>
    <h1>StreamOnPod - Open Graph Preview Test</h1>
    
    <div class="preview-card">
        <h2>Current Open Graph Configuration</h2>
        <p><strong>Title:</strong> StreamOnPod - Platform Streaming Cloud untuk Siaran Otomatis</p>
        <p><strong>Description:</strong> Ubah video Anda menjadi live stream otomatis dengan StreamOnPod. Platform streaming berbasis cloud untuk siaran konten berkelanjutan di berbagai platform dengan infrastruktur tingkat enterprise.</p>
        <p><strong>Image URL:</strong> https://streamonpod.com/images/streamonpod-logotype.png</p>
        <p><strong>Site URL:</strong> https://streamonpod.com</p>
        
        <h3>Preview Image:</h3>
        <img src="https://streamonpod.com/images/streamonpod-logotype.png" alt="StreamOnPod Logo" class="image-preview" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
        <div style="display: none; padding: 20px; background: #f44336; color: white; border-radius: 4px;">
            ❌ Image failed to load - check if the image URL is accessible
        </div>
    </div>
    
    <div class="test-links">
        <h2>Test Social Media Preview</h2>
        <p>Use these tools to test how your website appears when shared on social media:</p>
        
        <a href="https://developers.facebook.com/tools/debug/?q=https://streamonpod.com" target="_blank">
            Facebook Debugger
        </a>
        
        <a href="https://cards-dev.twitter.com/validator" target="_blank">
            Twitter Card Validator
        </a>
        
        <a href="https://www.linkedin.com/post-inspector/" target="_blank">
            LinkedIn Post Inspector
        </a>
        
        <a href="https://developers.google.com/search/docs/appearance/structured-data/testing-tool" target="_blank">
            Google Rich Results Test
        </a>
    </div>
    
    <div class="preview-card">
        <h2>Troubleshooting Tips</h2>
        <ul>
            <li>Make sure the image URL is publicly accessible (not behind authentication)</li>
            <li>Image should be at least 1200x630 pixels for best results</li>
            <li>Image file size should be under 8MB</li>
            <li>Use absolute URLs (starting with https://)</li>
            <li>Clear social media cache after making changes</li>
        </ul>
    </div>
    
    <script>
        // Test if the image loads successfully
        const img = new Image();
        img.onload = function() {
            console.log('✅ Open Graph image loaded successfully');
            console.log('Image dimensions:', this.naturalWidth + 'x' + this.naturalHeight);
        };
        img.onerror = function() {
            console.error('❌ Failed to load Open Graph image');
        };
        img.src = 'https://streamonpod.com/images/streamonpod-logotype.png';
    </script>
</body>
</html>
