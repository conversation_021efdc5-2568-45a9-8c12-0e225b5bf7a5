// Quick test dengan fungsi yang sudah diperbaiki
const { datetimeLocalToUTC } = require('./utils/timezone');

console.log('=== Test Final Fix ===');
console.log('System timezone:', Intl.DateTimeFormat().resolvedOptions().timeZone);

// Test case: 2025-06-02T06:00 in Asia/Jakarta
const input = '2025-06-02T06:00';
const timezone = 'Asia/Jakarta';

console.log(`\nInput: ${input} (${timezone})`);

// Test dengan fungsi yang sudah diperbaiki
const result = datetimeLocalToUTC(input, timezone);
console.log(`UTC Result: ${result}`);

if (result) {
  const utcDate = new Date(result);
  console.log(`UTC Date: ${utcDate}`);

  // Test display
  const displayDate = utcDate.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    timeZone: timezone
  });

  const displayTime = utcDate.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
    timeZone: timezone
  });

  console.log(`Display: Starts ${displayDate} • ${displayTime}`);

  // Test edit format
  const formatter = new Intl.DateTimeFormat('sv-SE', {
    timeZone: timezone,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });

  const parts = formatter.formatToParts(utcDate);
  const year = parts.find(p => p.type === 'year').value;
  const month = parts.find(p => p.type === 'month').value;
  const day = parts.find(p => p.type === 'day').value;
  const hour = parts.find(p => p.type === 'hour').value;
  const minute = parts.find(p => p.type === 'minute').value;

  const editFormat = `${year}-${month}-${day}T${hour}:${minute}`;
  console.log(`Edit format: ${editFormat}`);
}

console.log('\n=== Expected ===');
console.log('UTC should be: 2025-06-01T23:00:00.000Z');
console.log('Display should be: Starts Jun 2, 2025 • 06:00');
console.log('Edit should be: 2025-06-02T06:00');
