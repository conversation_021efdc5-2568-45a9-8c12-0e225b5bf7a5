#!/usr/bin/env node

/**
 * Test script untuk debugging Quota API
 * Menguji endpoint /subscription/quota
 */

const axios = require('axios');

// Konfigurasi untuk testing
const LOCAL_URL = 'http://localhost:7575';
const TUNNEL_URL = 'https://streamonpod.imthe.one';

async function testQuotaAPI(baseUrl, testName) {
  console.log(`\n🧪 Testing ${testName} (${baseUrl})`);
  console.log('='.repeat(50));
  
  try {
    // Test endpoint /subscription/quota
    console.log('\n1. Testing /subscription/quota endpoint:');
    
    try {
      const response = await axios.get(`${baseUrl}/subscription/quota`, {
        headers: {
          'User-Agent': 'StreamOnPod-Test/1.0',
          'Accept': 'application/json'
        },
        timeout: 10000,
        validateStatus: function (status) {
          return status < 500; // Accept all status codes below 500
        }
      });
      
      console.log(`   Status: ${response.status}`);
      console.log(`   Headers:`, response.headers);
      console.log(`   Response:`, JSON.stringify(response.data, null, 2));
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
      if (error.response) {
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Response:`, error.response.data);
      }
    }
    
  } catch (error) {
    console.log(`❌ General error testing ${testName}: ${error.message}`);
  }
}

async function runTests() {
  console.log('🚀 Starting Quota API Tests');
  console.log('============================');
  
  // Test local endpoint
  await testQuotaAPI(LOCAL_URL, 'Local Server');
  
  // Test tunnel endpoint
  await testQuotaAPI(TUNNEL_URL, 'Tunnel Server');
  
  console.log('\n✅ All tests completed!');
  console.log('\n📝 Summary:');
  console.log('- Check server logs for detailed request information');
  console.log('- Compare response differences between local and tunnel');
  console.log('- Look for authentication/session issues');
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Run the tests
runTests().catch(error => {
  console.error('❌ Test execution failed:', error.message);
  process.exit(1);
});
