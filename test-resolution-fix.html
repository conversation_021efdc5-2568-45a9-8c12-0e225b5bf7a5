<!DOCTYPE html>
<html>
<head>
    <title>Test Resolution Fix</title>
</head>
<body>
    <h1>Test Resolution Display Fix</h1>
    
    <div>
        <label>Resolution Select:</label>
        <select id="resolutionSelect">
            <option value="720" data-horizontal="1280x720" data-vertical="720x1280">720p HD</option>
            <option value="1080" data-horizontal="1920x1080" data-vertical="1080x1920">1080p Full HD</option>
            <option value="1440" data-horizontal="2560x1440" data-vertical="1440x2560">1440p QHD</option>
        </select>
    </div>
    
    <div>
        <label>Current Resolution:</label>
        <span id="currentResolution">1280x720</span>
    </div>
    
    <div>
        <button onclick="setVideoOrientation('horizontal')">Horizontal</button>
        <button onclick="setVideoOrientation('vertical')">Vertical</button>
    </div>
    
    <div>
        <p>Current Orientation: <span id="orientationDisplay">horizontal</span></p>
    </div>

    <script>
        let currentOrientation = 'horizontal';

        function setVideoOrientation(orientation) {
            currentOrientation = orientation;
            document.getElementById('orientationDisplay').textContent = orientation;
            updateResolutionDisplay();
        }

        function updateResolutionDisplay() {
            const select = document.getElementById('resolutionSelect');
            const currentResolutionSpan = document.getElementById('currentResolution');
            
            if (select && currentResolutionSpan) {
                const selected = select.options[select.selectedIndex];
                const resValue = selected.getAttribute(`data-${currentOrientation}`);
                if (resValue) {
                    currentResolutionSpan.textContent = resValue;
                }
            }
        }

        // Initialize
        document.getElementById('resolutionSelect').addEventListener('change', updateResolutionDisplay);
        updateResolutionDisplay();
    </script>
</body>
</html>
