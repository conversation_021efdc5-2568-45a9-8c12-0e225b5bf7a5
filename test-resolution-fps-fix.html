<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Resolution and FPS Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .test-section {
            background-color: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #1a4a1a;
            border: 1px solid #4a8a4a;
        }
        .error {
            background-color: #4a1a1a;
            border: 1px solid #8a4a4a;
        }
        select, input {
            background-color: #333;
            color: white;
            border: 1px solid #666;
            padding: 8px;
            margin: 5px;
            border-radius: 4px;
        }
        button {
            background-color: #ad6610;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #8a5010;
        }
    </style>
</head>
<body>
    <h1>Resolution and FPS Fix Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Resolution Display Fix</h2>
        <p>Testing the resolution display function to ensure it doesn't show "[object Object]"</p>
        
        <label>Resolution Select:</label>
        <select id="testResolutionSelect">
            <option value="720" data-horizontal="1280x720" data-vertical="720x1280">720p HD</option>
            <option value="1080" data-horizontal="1920x1080" data-vertical="1080x1920">1080p Full HD</option>
            <option value="1440" data-horizontal="2560x1440" data-vertical="1440x2560">1440p QHD</option>
        </select>
        
        <label>Orientation:</label>
        <select id="testOrientation">
            <option value="horizontal">Horizontal</option>
            <option value="vertical">Vertical</option>
        </select>
        
        <button onclick="testResolutionDisplay()">Test Resolution Display</button>
        
        <div>Current Resolution: <span id="testCurrentResolution">1280x720</span></div>
        <div id="resolutionTestResult" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Copy Mode Compatibility</h2>
        <p>Testing FPS limitations based on video compatibility</p>
        
        <label>Video Type:</label>
        <select id="testVideoType">
            <option value="h264">H.264 Compatible</option>
            <option value="hevc">HEVC (Needs Re-encoding)</option>
            <option value="mkv">MKV Container</option>
        </select>
        
        <label>Video FPS:</label>
        <input type="number" id="testVideoFps" value="30" min="1" max="120">
        
        <label>Video Bitrate (kbps):</label>
        <input type="number" id="testVideoBitrate" value="2500" min="500" max="20000">
        
        <button onclick="testCopyModeCompatibility()">Test Compatibility</button>
        
        <div id="compatibilityTestResult" class="test-result"></div>
        
        <div>
            <h3>Available FPS Options:</h3>
            <select id="testFpsOptions" multiple style="height: 100px; width: 200px;">
                <!-- Will be populated by test -->
            </select>
        </div>
    </div>

    <div class="test-section">
        <h2>Test 3: Resolution Options Handling</h2>
        <p>Testing resolution options with different formats (string vs object)</p>
        
        <button onclick="testStringResolutions()">Test String Format</button>
        <button onclick="testObjectResolutions()">Test Object Format</button>
        <button onclick="testMixedResolutions()">Test Mixed Format</button>
        
        <div>
            <h3>Test Resolution Select:</h3>
            <select id="testResolutionOptions">
                <!-- Will be populated by tests -->
            </select>
        </div>
        
        <div id="resolutionOptionsTestResult" class="test-result"></div>
    </div>

    <script>
        let currentTestOrientation = 'horizontal';

        // Mock the resolution display function from dashboard.ejs
        function updateTestResolutionDisplay() {
            const select = document.getElementById('testResolutionSelect');
            const currentResolutionSpan = document.getElementById('testCurrentResolution');
            const orientation = document.getElementById('testOrientation').value;

            if (select && currentResolutionSpan && select.options.length > 0 && select.selectedIndex >= 0) {
                const selected = select.options[select.selectedIndex];
                if (selected) {
                    const resValue = selected.getAttribute(`data-${orientation}`);
                    if (resValue) {
                        currentResolutionSpan.textContent = resValue;
                    } else {
                        // Fallback logic (same as fixed version)
                        const optionText = selected.textContent || selected.value;
                        if (optionText && optionText.includes('x')) {
                            currentResolutionSpan.textContent = optionText;
                        } else if (selected.value && !isNaN(selected.value)) {
                            const height = selected.value;
                            const width = orientation === 'vertical' ? 
                                (height == '720' ? '720' : height == '1080' ? '1080' : height) :
                                (height == '720' ? '1280' : height == '1080' ? '1920' : height == '480' ? '854' : height == '360' ? '640' : height);
                            const resolution = orientation === 'vertical' ? `${height}x${width}` : `${width}x${height}`;
                            currentResolutionSpan.textContent = resolution;
                        } else {
                            currentResolutionSpan.textContent = '1280x720'; // Default fallback
                        }
                    }
                }
            }
        }

        function testResolutionDisplay() {
            try {
                updateTestResolutionDisplay();
                const result = document.getElementById('testCurrentResolution').textContent;
                const resultDiv = document.getElementById('resolutionTestResult');
                
                if (result && result !== '[object Object]' && result.includes('x')) {
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = `✅ SUCCESS: Resolution displays correctly as "${result}"`;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.textContent = `❌ ERROR: Resolution displays as "${result}"`;
                }
            } catch (error) {
                const resultDiv = document.getElementById('resolutionTestResult');
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `❌ ERROR: ${error.message}`;
            }
        }

        function testCopyModeCompatibility() {
            const videoType = document.getElementById('testVideoType').value;
            const videoFps = parseInt(document.getElementById('testVideoFps').value);
            const videoBitrate = parseInt(document.getElementById('testVideoBitrate').value);
            
            let compatible = true;
            let maxFps = videoFps;
            let maxBitrate = videoBitrate;
            let reason = '';

            // Simulate copy mode compatibility logic
            if (videoType === 'hevc') {
                compatible = false;
                reason = 'HEVC codec requires re-encoding';
            } else if (videoType === 'mkv') {
                compatible = false;
                reason = 'MKV container requires re-encoding for RTMP';
            } else {
                // H.264 compatible - calculate limits
                maxFps = Math.floor(videoFps * 1.1);
                maxBitrate = Math.floor(videoBitrate * 1.4);
            }

            // Generate FPS options
            const fpsOptions = [15, 20, 24, 25, 30, 50, 60, 120];
            const availableFps = compatible ? fpsOptions.filter(fps => fps <= maxFps) : [30]; // Default for incompatible

            // Update FPS options display
            const fpsSelect = document.getElementById('testFpsOptions');
            fpsSelect.innerHTML = '';
            availableFps.forEach(fps => {
                const option = document.createElement('option');
                option.value = fps;
                option.textContent = `${fps} FPS`;
                fpsSelect.appendChild(option);
            });

            // Show result
            const resultDiv = document.getElementById('compatibilityTestResult');
            if (compatible) {
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `✅ COMPATIBLE: Copy mode can be used<br>Max FPS: ${maxFps}, Max Bitrate: ${maxBitrate}k<br>Available FPS options: ${availableFps.join(', ')}`;
            } else {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ INCOMPATIBLE: ${reason}<br>Will use re-encoding with default settings<br>Available FPS options: ${availableFps.join(', ')}`;
            }
        }

        function updateTestResolutionOptions(selectElement, resolutions) {
            selectElement.innerHTML = '';

            resolutions.forEach((res, index) => {
                const optionElement = document.createElement('option');
                
                if (typeof res === 'string') {
                    // Simple string format like "1280x720"
                    const resolutionValue = res.match(/(\d+)x(\d+)/)?.[0] || res;
                    const resolutionLabel = res.includes('x') ? res : `${res}p`;
                    optionElement.value = resolutionValue.split('x')[1] || res;
                    optionElement.setAttribute('data-horizontal', resolutionValue);
                    optionElement.setAttribute('data-vertical', resolutionValue.split('x').reverse().join('x'));
                    optionElement.textContent = resolutionLabel;
                } else if (res && res.value && res.label) {
                    // Object format like { value: "1280x720", label: "720p HD" }
                    const resolutionValue = res.label.match(/(\d+)p/)?.[1] || res.value.split('x')[1];
                    optionElement.value = resolutionValue;
                    optionElement.setAttribute('data-horizontal', res.value);
                    optionElement.setAttribute('data-vertical', res.value.split('x').reverse().join('x'));
                    optionElement.textContent = res.label;
                }
                selectElement.appendChild(optionElement);
            });
        }

        function testStringResolutions() {
            const select = document.getElementById('testResolutionOptions');
            const resolutions = ['1280x720', '1920x1080', '854x480'];
            
            try {
                updateTestResolutionOptions(select, resolutions);
                const resultDiv = document.getElementById('resolutionOptionsTestResult');
                resultDiv.className = 'test-result success';
                resultDiv.textContent = `✅ SUCCESS: String format resolutions handled correctly (${select.options.length} options)`;
            } catch (error) {
                const resultDiv = document.getElementById('resolutionOptionsTestResult');
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `❌ ERROR: ${error.message}`;
            }
        }

        function testObjectResolutions() {
            const select = document.getElementById('testResolutionOptions');
            const resolutions = [
                { value: '480x360', width: 480, height: 360, label: '360p (480x360)' },
                { value: '640x480', width: 640, height: 480, label: '480p (640x480)' },
                { value: '854x480', width: 854, height: 480, label: '480p Wide (854x480)' },
                { value: '1280x720', width: 1280, height: 720, label: '720p HD (1280x720)' },
                { value: '1920x1080', width: 1920, height: 1080, label: '1080p FHD (1920x1080)' }
            ];
            
            try {
                updateTestResolutionOptions(select, resolutions);
                const resultDiv = document.getElementById('resolutionOptionsTestResult');
                resultDiv.className = 'test-result success';
                resultDiv.textContent = `✅ SUCCESS: Object format resolutions handled correctly (${select.options.length} options)`;
            } catch (error) {
                const resultDiv = document.getElementById('resolutionOptionsTestResult');
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `❌ ERROR: ${error.message}`;
            }
        }

        function testMixedResolutions() {
            const select = document.getElementById('testResolutionOptions');
            const resolutions = [
                '1280x720',
                { value: '1920x1080', label: '1080p Full HD' },
                '854x480'
            ];
            
            try {
                updateTestResolutionOptions(select, resolutions);
                const resultDiv = document.getElementById('resolutionOptionsTestResult');
                resultDiv.className = 'test-result success';
                resultDiv.textContent = `✅ SUCCESS: Mixed format resolutions handled correctly (${select.options.length} options)`;
            } catch (error) {
                const resultDiv = document.getElementById('resolutionOptionsTestResult');
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `❌ ERROR: ${error.message}`;
            }
        }

        // Event listeners
        document.getElementById('testResolutionSelect').addEventListener('change', updateTestResolutionDisplay);
        document.getElementById('testOrientation').addEventListener('change', updateTestResolutionDisplay);

        // Initialize
        updateTestResolutionDisplay();
    </script>
</body>
</html>
