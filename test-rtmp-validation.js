#!/usr/bin/env node

/**
 * Test script untuk debugging RTMP validation
 * Menguji validasi RTMP URL secara langsung
 */

const StreamKeyValidator = require('./utils/streamKeyValidator');

console.log('🧪 Testing RTMP URL Validation');
console.log('================================');

// Test cases yang umum digunakan
const testCases = [
  {
    name: 'YouTube RTMP URL',
    rtmpUrl: 'rtmp://a.rtmp.youtube.com/live2',
    streamKey: 'abcd-1234-efgh-5678-ijkl-9012'
  },
  {
    name: 'Facebook RTMP URL',
    rtmpUrl: 'rtmps://live-api-s.facebook.com:443/rtmp',
    streamKey: 'FB-1234567890-ABCDEFGH'
  },
  {
    name: 'TikTok RTMP URL',
    rtmpUrl: 'rtmps://ingest.global.live.prod.tiktok.com/live',
    streamKey: 'tiktok-stream-key-123456'
  },
  {
    name: 'Twitch RTMP URL',
    rtmpUrl: 'rtmp://live.twitch.tv/live',
    streamKey: 'live_123456789_abcdefghijklmnop'
  },
  {
    name: 'Custom RTMP URL (HTTP)',
    rtmpUrl: 'rtmp://custom-server.com/live',
    streamKey: 'custom-key-123'
  },
  {
    name: 'Custom RTMPS URL (HTTPS)',
    rtmpUrl: 'rtmps://secure-server.com/live',
    streamKey: 'secure-key-456'
  },
  {
    name: 'Invalid URL (no protocol)',
    rtmpUrl: 'invalid-url.com/live',
    streamKey: 'test-key'
  },
  {
    name: 'Invalid URL (wrong protocol)',
    rtmpUrl: 'http://server.com/live',
    streamKey: 'test-key'
  },
  {
    name: 'Empty RTMP URL',
    rtmpUrl: '',
    streamKey: 'test-key'
  },
  {
    name: 'Empty Stream Key',
    rtmpUrl: 'rtmp://server.com/live',
    streamKey: ''
  }
];

console.log(`Running ${testCases.length} test cases...\n`);

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}: ${testCase.name}`);
  console.log(`RTMP URL: ${testCase.rtmpUrl}`);
  console.log(`Stream Key: ${testCase.streamKey}`);
  
  try {
    const validation = StreamKeyValidator.validateRtmpConfig(testCase.rtmpUrl, testCase.streamKey);
    
    console.log(`✅ Validation Result:`);
    console.log(`   Valid: ${validation.isValid}`);
    console.log(`   Platform: ${validation.platform}`);
    
    if (validation.errors && validation.errors.length > 0) {
      console.log(`   Errors:`);
      validation.errors.forEach(error => {
        console.log(`     - ${error}`);
      });
    }
    
    if (validation.warnings && validation.warnings.length > 0) {
      console.log(`   Warnings:`);
      validation.warnings.forEach(warning => {
        console.log(`     - ${warning}`);
      });
    }
    
    if (validation.suggestions && validation.suggestions.length > 0) {
      console.log(`   Suggestions:`);
      validation.suggestions.forEach(suggestion => {
        console.log(`     - ${suggestion}`);
      });
    }
    
  } catch (error) {
    console.log(`❌ Error during validation: ${error.message}`);
  }
  
  console.log('---\n');
});

console.log('🔍 Testing individual validation functions:');
console.log('==========================================');

// Test RTMP URL validation only
console.log('\n1. Testing RTMP URL validation:');
const rtmpTests = [
  'rtmp://a.rtmp.youtube.com/live2',
  'rtmps://live-api-s.facebook.com:443/rtmp',
  'http://invalid.com/live',
  'invalid-url',
  ''
];

rtmpTests.forEach(url => {
  const result = StreamKeyValidator.validateRtmpUrl(url);
  console.log(`   "${url}" -> Valid: ${result.isValid}, Errors: ${result.errors.join(', ')}`);
});

// Test Stream Key validation only
console.log('\n2. Testing Stream Key validation:');
const keyTests = [
  'valid-stream-key-123',
  'test',
  '123',
  '',
  'live_123456789_abcdefghijklmnop',
  'a'.repeat(101) // Too long
];

keyTests.forEach(key => {
  const result = StreamKeyValidator.validateStreamKey(key);
  console.log(`   "${key}" -> Valid: ${result.isValid}, Errors: ${result.errors.join(', ')}`);
});

console.log('\n✅ Test completed!');
