// Test script to verify schedule time validation issue
const { datetimeLocalToUTC, formatDateTimeWithTimezone, convertToUTC, getTimezoneOffset } = require('./utils/timezone');

console.log('=== Schedule Time Validation Test ===');

// Test current time + 2 minutes in WIB timezone
const now = new Date();
const twoMinutesLater = new Date(now.getTime() + (2 * 60 * 1000));

// Format as datetime-local input (YYYY-MM-DDTHH:mm)
const year = twoMinutesLater.getFullYear();
const month = String(twoMinutesLater.getMonth() + 1).padStart(2, '0');
const day = String(twoMinutesLater.getDate()).padStart(2, '0');
const hours = String(twoMinutesLater.getHours()).padStart(2, '0');
const minutes = String(twoMinutesLater.getMinutes()).padStart(2, '0');

const testInput = `${year}-${month}-${day}T${hours}:${minutes}`;
const testTimezone = 'Asia/Jakarta';

console.log(`Current time: ${now.toISOString()}`);
console.log(`Current time WIB: ${now.toLocaleString('en-US', { timeZone: 'Asia/Jakarta' })}`);
console.log(`Test input (2 min later): ${testInput} (${testTimezone})`);
console.log(`Expected time WIB: ${twoMinutesLater.toLocaleString('en-US', { timeZone: 'Asia/Jakarta' })}`);

// Test the conversion step by step
console.log('\n--- Server-side Validation Logic ---');

// Simulate server validation logic from app.js
const scheduleTimeUTC = datetimeLocalToUTC(testInput, testTimezone);
const serverNow = new Date();
const minimumTime = new Date(serverNow.getTime() + (1 * 60 * 1000)); // 1 minute buffer

console.log(`1. Input datetime-local: ${testInput}`);
console.log(`2. Converted to UTC: ${scheduleTimeUTC}`);
console.log(`3. Server current time: ${serverNow.toISOString()}`);
console.log(`4. Minimum required time: ${minimumTime.toISOString()}`);

const scheduleDate = new Date(scheduleTimeUTC);
const isValid = scheduleDate > minimumTime;

console.log(`5. Schedule date object: ${scheduleDate.toISOString()}`);
console.log(`6. Is valid (schedule > minimum): ${isValid}`);
console.log(`7. Time difference: ${(scheduleDate.getTime() - serverNow.getTime()) / 1000} seconds`);

// Test timezone offset calculation
console.log('\n--- Timezone Analysis ---');
const systemTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
console.log(`System timezone: ${systemTimezone}`);

// Test with different approaches
console.log('\n--- Alternative Conversion Methods ---');

// Method 1: Direct Date constructor (what JavaScript does by default)
const directDate = new Date(testInput);
console.log(`Direct Date(): ${directDate.toISOString()}`);

// Method 2: Using Intl API for proper timezone handling
const intlDate = new Date(testInput + ':00'); // Add seconds
const utcTime = intlDate.toLocaleString('sv-SE', { timeZone: 'UTC' });
const localTime = intlDate.toLocaleString('sv-SE', { timeZone: testTimezone });
console.log(`Intl UTC time: ${utcTime}`);
console.log(`Intl local time: ${localTime}`);

// Method 3: Manual offset calculation
const jakartaOffset = 7 * 60; // +7 hours in minutes
const manualUTC = new Date(directDate.getTime() - (jakartaOffset * 60 * 1000));
console.log(`Manual UTC calculation: ${manualUTC.toISOString()}`);

console.log('\n=== Diagnosis ===');
if (!isValid) {
  console.log('❌ PROBLEM FOUND: Schedule validation is failing');
  console.log('Possible causes:');
  console.log('1. Timezone conversion is not working correctly');
  console.log('2. Server timezone differs from expected timezone');
  console.log('3. Client and server time are not synchronized');
} else {
  console.log('✅ Schedule validation should pass');
}

// Test frontend validation logic (FIXED VERSION)
console.log('\n--- Frontend Validation Logic (FIXED) ---');

// Simulate FIXED frontend validation (from dashboard.ejs)
const frontendInputDate = new Date(testInput);
let frontendScheduleTimeUTC;

if (testTimezone && testTimezone !== 'UTC') {
  // Use proper timezone conversion that matches backend logic
  const systemTimezone = 'Asia/Jakarta'; // Server timezone

  if (testTimezone === systemTimezone) {
    // If target timezone is same as system timezone, no conversion needed
    frontendScheduleTimeUTC = frontendInputDate;
  } else {
    // For other Indonesian timezones, calculate offset difference
    const timezoneOffsets = {
      'Asia/Jakarta': 7,    // UTC+7 (WIB)
      'Asia/Makassar': 8,   // UTC+8 (WITA)
      'Asia/Jayapura': 9    // UTC+9 (WIT)
    };

    const systemOffset = timezoneOffsets[systemTimezone] || 7;
    const targetOffset = timezoneOffsets[testTimezone] || 7;
    const offsetDiff = targetOffset - systemOffset; // Difference in hours

    // Adjust for timezone difference
    frontendScheduleTimeUTC = new Date(frontendInputDate.getTime() - (offsetDiff * 60 * 60 * 1000));
  }
} else {
  // For UTC, treat input as UTC
  frontendScheduleTimeUTC = new Date(testInput);
}

const frontendNow = new Date();
const frontendMinimumTime = new Date(frontendNow.getTime() + (1 * 60 * 1000));
const frontendIsValid = frontendScheduleTimeUTC > frontendMinimumTime;

console.log(`Frontend input date: ${frontendInputDate.toISOString()}`);
console.log(`Frontend schedule UTC: ${frontendScheduleTimeUTC.toISOString()}`);
console.log(`Frontend minimum time: ${frontendMinimumTime.toISOString()}`);
console.log(`Frontend is valid: ${frontendIsValid}`);
console.log(`Frontend time diff: ${(frontendScheduleTimeUTC.getTime() - frontendNow.getTime()) / 1000} seconds`);

console.log('\n=== Comparison ===');
console.log(`Backend UTC: ${scheduleTimeUTC}`);
console.log(`Frontend UTC: ${frontendScheduleTimeUTC.toISOString()}`);
console.log(`Backend valid: ${isValid}`);
console.log(`Frontend valid: ${frontendIsValid}`);

if (scheduleTimeUTC !== frontendScheduleTimeUTC.toISOString()) {
  console.log('❌ MISMATCH: Frontend and backend produce different UTC times!');
  console.log('This explains why validation might fail.');
} else {
  console.log('✅ Frontend and backend produce same UTC time');
}

console.log('\n=== Recommendations ===');
console.log('1. Check server system timezone');
console.log('2. Verify timezone conversion logic');
console.log('3. Add debug logging to schedule validation');
console.log('4. Consider using more robust timezone library like moment-timezone');
console.log('5. Fix frontend/backend timezone conversion mismatch');
