// Test scheduler service
const schedulerService = require('./services/schedulerService');
const streamingService = require('./services/streamingService');

console.log('Testing scheduler service...');

// Mock streaming service for testing
const mockStreamingService = {
  startStream: async (streamId) => {
    console.log(`Mock: Starting stream ${streamId}`);
    return { success: true };
  },
  stopStream: async (streamId) => {
    console.log(`Mock: Stopping stream ${streamId}`);
    return { success: true };
  }
};

// Initialize scheduler with mock service
console.log('Initializing scheduler...');
schedulerService.init(mockStreamingService);

console.log('Scheduler test completed. Check logs above for scheduler activity.');

// Keep process alive for a few minutes to see scheduler logs
setTimeout(() => {
  console.log('Test completed');
  process.exit(0);
}, 5 * 60 * 1000); // 5 minutes
