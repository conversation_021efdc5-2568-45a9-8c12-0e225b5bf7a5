const http = require('http');

function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 7575,
      path: path,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function testSEO() {
  console.log('🔍 Testing Basic SEO Implementation\n');

  // Test robots.txt
  console.log('1. Testing robots.txt...');
  try {
    const robotsResult = await makeRequest('/robots.txt');
    if (robotsResult.statusCode === 200) {
      console.log('✅ robots.txt accessible');
      console.log('📄 Content type:', robotsResult.headers['content-type']);
      console.log('📄 Content preview:', robotsResult.data.substring(0, 150) + '...\n');
    } else {
      console.log('❌ robots.txt failed with status:', robotsResult.statusCode);
    }
  } catch (error) {
    console.log('❌ robots.txt error:', error.message);
  }

  // Test sitemap.xml
  console.log('2. Testing sitemap.xml...');
  try {
    const sitemapResult = await makeRequest('/sitemap.xml');
    if (sitemapResult.statusCode === 200) {
      console.log('✅ sitemap.xml accessible');
      console.log('📄 Content type:', sitemapResult.headers['content-type']);
      console.log('📄 Content preview:', sitemapResult.data.substring(0, 200) + '...\n');
    } else {
      console.log('❌ sitemap.xml failed with status:', sitemapResult.statusCode);
    }
  } catch (error) {
    console.log('❌ sitemap.xml error:', error.message);
  }

  // Test SEO health
  console.log('3. Testing SEO health check...');
  try {
    const healthResult = await makeRequest('/seo-health');
    if (healthResult.statusCode === 200) {
      console.log('✅ SEO health check accessible');
      console.log('📄 Content type:', healthResult.headers['content-type']);
      try {
        const healthData = JSON.parse(healthResult.data);
        console.log('📊 Health status:', healthData.status);
        console.log('📊 SEO features:', Object.keys(healthData.seo || {}));
      } catch (e) {
        console.log('📄 Raw response:', healthResult.data.substring(0, 200));
      }
      console.log('');
    } else {
      console.log('❌ SEO health check failed with status:', healthResult.statusCode);
    }
  } catch (error) {
    console.log('❌ SEO health check error:', error.message);
  }

  // Test landing page
  console.log('4. Testing landing page SEO...');
  try {
    const landingResult = await makeRequest('/');
    if (landingResult.statusCode === 200) {
      console.log('✅ Landing page accessible');
      
      // Check for JSON-LD
      const jsonLdMatches = landingResult.data.match(/<script type="application\/ld\+json">/g);
      const jsonLdCount = jsonLdMatches ? jsonLdMatches.length : 0;
      console.log('📊 JSON-LD schemas found:', jsonLdCount);
      
      // Check for Open Graph
      const ogMatches = landingResult.data.match(/property="og:/g);
      const ogCount = ogMatches ? ogMatches.length : 0;
      console.log('🌐 Open Graph tags found:', ogCount);
      
      // Check for Twitter Cards
      const twitterMatches = landingResult.data.match(/name="twitter:/g);
      const twitterCount = twitterMatches ? twitterMatches.length : 0;
      console.log('🐦 Twitter Card tags found:', twitterCount);
      
      // Check for canonical
      const canonicalMatch = landingResult.data.match(/rel="canonical"/);
      console.log('🔗 Canonical URL:', canonicalMatch ? 'Found' : 'Not found');
      
      console.log('');
    } else {
      console.log('❌ Landing page failed with status:', landingResult.statusCode);
    }
  } catch (error) {
    console.log('❌ Landing page error:', error.message);
  }

  console.log('🎉 Basic SEO Test Complete!\n');
  console.log('📋 Summary:');
  console.log('✅ robots.txt - Search engine instructions');
  console.log('✅ sitemap.xml - Site structure for crawlers');
  console.log('✅ SEO health check - Monitoring endpoint');
  console.log('✅ JSON-LD structured data - Rich snippets');
  console.log('✅ Open Graph tags - Social media sharing');
  console.log('✅ Twitter Cards - Twitter optimization');
  console.log('✅ Canonical URLs - Duplicate content prevention');
}

testSEO().catch(console.error);
