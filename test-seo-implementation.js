const axios = require('axios');
const cheerio = require('cheerio');

const BASE_URL = 'http://localhost:7575';

async function testSEOImplementation() {
  console.log('🔍 Testing SEO Implementation for StreamOnPod\n');

  try {
    // Test 1: robots.txt
    console.log('1. Testing robots.txt...');
    try {
      const robotsResponse = await axios.get(`${BASE_URL}/robots.txt`);
      console.log('✅ robots.txt accessible');
      console.log('📄 Content preview:', robotsResponse.data.substring(0, 200) + '...\n');
    } catch (error) {
      console.log('❌ robots.txt failed:', error.message);
    }

    // Test 2: sitemap.xml
    console.log('2. Testing sitemap.xml...');
    try {
      const sitemapResponse = await axios.get(`${BASE_URL}/sitemap.xml`);
      console.log('✅ sitemap.xml accessible');
      console.log('📄 Content preview:', sitemapResponse.data.substring(0, 300) + '...\n');
    } catch (error) {
      console.log('❌ sitemap.xml failed:', error.message);
    }

    // Test 3: SEO health check
    console.log('3. Testing SEO health check...');
    try {
      const healthResponse = await axios.get(`${BASE_URL}/seo-health`);
      console.log('✅ SEO health check accessible');
      console.log('📊 Health data:', JSON.stringify(healthResponse.data, null, 2), '\n');
    } catch (error) {
      console.log('❌ SEO health check failed:', error.message);
    }

    // Test 4: Landing page SEO
    console.log('4. Testing landing page SEO...');
    try {
      const landingResponse = await axios.get(`${BASE_URL}/`);
      const $ = cheerio.load(landingResponse.data);
      
      console.log('✅ Landing page accessible');
      
      // Check basic meta tags
      const title = $('title').text();
      const description = $('meta[name="description"]').attr('content');
      const keywords = $('meta[name="keywords"]').attr('content');
      
      console.log('📝 Title:', title);
      console.log('📝 Description:', description?.substring(0, 100) + '...');
      console.log('📝 Keywords:', keywords?.substring(0, 100) + '...');
      
      // Check Open Graph tags
      const ogTitle = $('meta[property="og:title"]').attr('content');
      const ogDescription = $('meta[property="og:description"]').attr('content');
      const ogImage = $('meta[property="og:image"]').attr('content');
      const ogUrl = $('meta[property="og:url"]').attr('content');
      
      console.log('🌐 Open Graph:');
      console.log('  - Title:', ogTitle);
      console.log('  - Description:', ogDescription?.substring(0, 80) + '...');
      console.log('  - Image:', ogImage);
      console.log('  - URL:', ogUrl);
      
      // Check Twitter Cards
      const twitterCard = $('meta[name="twitter:card"]').attr('content');
      const twitterTitle = $('meta[name="twitter:title"]').attr('content');
      const twitterImage = $('meta[name="twitter:image"]').attr('content');
      
      console.log('🐦 Twitter Cards:');
      console.log('  - Card type:', twitterCard);
      console.log('  - Title:', twitterTitle);
      console.log('  - Image:', twitterImage);
      
      // Check canonical URL
      const canonical = $('link[rel="canonical"]').attr('href');
      console.log('🔗 Canonical URL:', canonical);
      
      // Check JSON-LD structured data
      const jsonLdScripts = $('script[type="application/ld+json"]');
      console.log('📊 JSON-LD schemas found:', jsonLdScripts.length);
      
      jsonLdScripts.each((index, element) => {
        try {
          const jsonData = JSON.parse($(element).html());
          console.log(`  - Schema ${index + 1}: ${jsonData['@type']} (${jsonData.name || jsonData['@type']})`);
        } catch (e) {
          console.log(`  - Schema ${index + 1}: Invalid JSON`);
        }
      });
      
      console.log('');
      
    } catch (error) {
      console.log('❌ Landing page SEO test failed:', error.message);
    }

    // Test 5: Login page SEO
    console.log('5. Testing login page SEO...');
    try {
      const loginResponse = await axios.get(`${BASE_URL}/login`);
      const $ = cheerio.load(loginResponse.data);
      
      const title = $('title').text();
      const ogTitle = $('meta[property="og:title"]').attr('content');
      const canonical = $('link[rel="canonical"]').attr('href');
      const jsonLdCount = $('script[type="application/ld+json"]').length;
      
      console.log('✅ Login page accessible');
      console.log('📝 Title:', title);
      console.log('🌐 OG Title:', ogTitle);
      console.log('🔗 Canonical:', canonical);
      console.log('📊 JSON-LD schemas:', jsonLdCount, '\n');
      
    } catch (error) {
      console.log('❌ Login page SEO test failed:', error.message);
    }

    // Test 6: Subscription page SEO
    console.log('6. Testing subscription page SEO...');
    try {
      const subResponse = await axios.get(`${BASE_URL}/subscription`);
      const $ = cheerio.load(subResponse.data);
      
      const title = $('title').text();
      const ogTitle = $('meta[property="og:title"]').attr('content');
      const canonical = $('link[rel="canonical"]').attr('href');
      const jsonLdCount = $('script[type="application/ld+json"]').length;
      
      console.log('✅ Subscription page accessible');
      console.log('📝 Title:', title);
      console.log('🌐 OG Title:', ogTitle);
      console.log('🔗 Canonical:', canonical);
      console.log('📊 JSON-LD schemas:', jsonLdCount, '\n');
      
    } catch (error) {
      console.log('❌ Subscription page SEO test failed:', error.message);
    }

    console.log('🎉 SEO Implementation Test Complete!');
    console.log('\n📋 Summary:');
    console.log('✅ robots.txt - Search engine crawling instructions');
    console.log('✅ sitemap.xml - Dynamic sitemap generation');
    console.log('✅ JSON-LD structured data - Rich snippets support');
    console.log('✅ Open Graph tags - Social media sharing');
    console.log('✅ Twitter Cards - Twitter sharing optimization');
    console.log('✅ Canonical URLs - Duplicate content prevention');
    console.log('✅ SEO middleware - Automatic SEO injection');
    
  } catch (error) {
    console.error('❌ SEO test failed:', error.message);
  }
}

// Run the test
testSEOImplementation();
