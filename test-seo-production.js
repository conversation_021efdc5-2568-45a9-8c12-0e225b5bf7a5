const http = require('http');

function makeRequest(path, hostname = 'localhost', port = 7575) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: hostname,
      port: port,
      path: path,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function testSEOProduction() {
  console.log('🔍 Testing SEO Implementation for Production (streamonpod.com)\n');

  // Test robots.txt
  console.log('1. Testing robots.txt...');
  try {
    const robotsResult = await makeRequest('/robots.txt');
    if (robotsResult.statusCode === 200) {
      console.log('✅ robots.txt accessible');
      console.log('📄 Content type:', robotsResult.headers['content-type']);
      
      // Check if it contains the correct domain
      if (robotsResult.data.includes('streamonpod.com')) {
        console.log('✅ Domain correctly set to streamonpod.com');
      } else {
        console.log('❌ Domain not updated to streamonpod.com');
      }
      
      console.log('📄 Content preview:', robotsResult.data.substring(0, 200) + '...\n');
    } else {
      console.log('❌ robots.txt failed with status:', robotsResult.statusCode);
    }
  } catch (error) {
    console.log('❌ robots.txt error:', error.message);
  }

  // Test sitemap.xml
  console.log('2. Testing sitemap.xml...');
  try {
    const sitemapResult = await makeRequest('/sitemap.xml');
    if (sitemapResult.statusCode === 200) {
      console.log('✅ sitemap.xml accessible');
      console.log('📄 Content type:', sitemapResult.headers['content-type']);
      
      // Check if it contains the correct domain
      if (sitemapResult.data.includes('streamonpod.com')) {
        console.log('✅ Domain correctly set to streamonpod.com');
      } else {
        console.log('❌ Domain not updated to streamonpod.com');
      }
      
      console.log('📄 Content preview:', sitemapResult.data.substring(0, 300) + '...\n');
    } else {
      console.log('❌ sitemap.xml failed with status:', sitemapResult.statusCode);
    }
  } catch (error) {
    console.log('❌ sitemap.xml error:', error.message);
  }

  // Test SEO health
  console.log('3. Testing SEO health check...');
  try {
    const healthResult = await makeRequest('/seo-health');
    if (healthResult.statusCode === 200) {
      console.log('✅ SEO health check accessible');
      console.log('📄 Content type:', healthResult.headers['content-type']);
      try {
        const healthData = JSON.parse(healthResult.data);
        console.log('📊 Health status:', healthData.status);
        console.log('📊 SEO features:', Object.keys(healthData.seo || {}));
        
        // Check if domain is correct in health check
        if (JSON.stringify(healthData).includes('streamonpod.com')) {
          console.log('✅ Domain correctly set to streamonpod.com in health check');
        } else {
          console.log('❌ Domain not updated to streamonpod.com in health check');
        }
      } catch (e) {
        console.log('📄 Raw response:', healthResult.data.substring(0, 200));
      }
      console.log('');
    } else {
      console.log('❌ SEO health check failed with status:', healthResult.statusCode);
    }
  } catch (error) {
    console.log('❌ SEO health check error:', error.message);
  }

  // Test landing page
  console.log('4. Testing landing page SEO...');
  try {
    const landingResult = await makeRequest('/');
    if (landingResult.statusCode === 200) {
      console.log('✅ Landing page accessible');
      
      // Check language
      const htmlLangMatch = landingResult.data.match(/html lang="([^"]+)"/);
      const htmlLang = htmlLangMatch ? htmlLangMatch[1] : 'not found';
      console.log('🌐 HTML language:', htmlLang);
      
      if (htmlLang === 'id') {
        console.log('✅ Default language correctly set to Indonesian');
      } else {
        console.log('❌ Default language not set to Indonesian');
      }
      
      // Check for Indonesian content
      if (landingResult.data.includes('Platform streaming berbasis cloud')) {
        console.log('✅ Indonesian content found in meta description');
      } else {
        console.log('❌ Indonesian content not found');
      }
      
      // Check domain in content
      if (landingResult.data.includes('streamonpod.com')) {
        console.log('✅ Domain correctly set to streamonpod.com in content');
      } else {
        console.log('❌ Domain not updated to streamonpod.com in content');
      }
      
      // Check for JSON-LD
      const jsonLdMatches = landingResult.data.match(/<script type="application\/ld\+json">/g);
      const jsonLdCount = jsonLdMatches ? jsonLdMatches.length : 0;
      console.log('📊 JSON-LD schemas found:', jsonLdCount);
      
      // Check for Open Graph
      const ogMatches = landingResult.data.match(/property="og:/g);
      const ogCount = ogMatches ? ogMatches.length : 0;
      console.log('🌐 Open Graph tags found:', ogCount);
      
      // Check for Indonesian locale in OG
      if (landingResult.data.includes('og:locale" content="id_ID"')) {
        console.log('✅ Open Graph locale correctly set to id_ID');
      } else {
        console.log('❌ Open Graph locale not set to id_ID');
      }
      
      // Check for Twitter Cards
      const twitterMatches = landingResult.data.match(/name="twitter:/g);
      const twitterCount = twitterMatches ? twitterMatches.length : 0;
      console.log('🐦 Twitter Card tags found:', twitterCount);
      
      // Check for canonical
      const canonicalMatch = landingResult.data.match(/rel="canonical"/);
      console.log('🔗 Canonical URL:', canonicalMatch ? 'Found' : 'Not found');
      
      console.log('');
    } else {
      console.log('❌ Landing page failed with status:', landingResult.statusCode);
    }
  } catch (error) {
    console.log('❌ Landing page error:', error.message);
  }

  console.log('🎉 Production SEO Test Complete!\n');
  console.log('📋 Summary of Changes:');
  console.log('✅ Domain updated to streamonpod.com');
  console.log('✅ Default language changed to Indonesian (id)');
  console.log('✅ Indonesian meta descriptions and titles');
  console.log('✅ Open Graph locale set to id_ID');
  console.log('✅ JSON-LD schemas with Indonesian content');
  console.log('✅ Breadcrumbs in Indonesian');
  console.log('✅ SEO middleware supports locale detection');
  
  console.log('\n🌟 Production Ready Features:');
  console.log('🎯 robots.txt with streamonpod.com domain');
  console.log('🎯 sitemap.xml with streamonpod.com URLs');
  console.log('🎯 Indonesian-first SEO content');
  console.log('🎯 Bilingual support (Indonesian/English)');
  console.log('🎯 Production domain configuration');
}

testSEOProduction().catch(console.error);
