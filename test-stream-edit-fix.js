/**
 * Test script to verify the stream edit bug fix
 * This script tests the scenario where a live stream is edited and should be restarted
 */

const { db } = require('./db/database');
const Stream = require('./models/Stream');
const streamingService = require('./services/streamingService');

async function testStreamEditFix() {
  console.log('🧪 Testing Stream Edit Fix...\n');

  try {
    // 1. Find a test stream or create one
    console.log('1. Setting up test stream...');
    const testUserId = 'test-user-123';
    
    // Create a test stream
    const testStreamData = {
      title: 'Test Stream for Edit Fix',
      video_id: 'test-video-123',
      rtmp_url: 'rtmp://a.rtmp.youtube.com/live2',
      stream_key: 'test-stream-key-123',
      platform: 'YouTube',
      platform_icon: 'ti-brand-youtube',
      bitrate: 2500,
      resolution: '1920x1080',
      fps: 30,
      orientation: 'horizontal',
      loop_video: true,
      user_id: testUserId
    };

    const testStream = await Stream.create(testStreamData);
    console.log(`✅ Created test stream: ${testStream.id}`);

    // 2. Simulate the stream being live
    console.log('\n2. Simulating live stream...');
    await Stream.updateStatus(testStream.id, 'live', testUserId);
    console.log('✅ Stream status set to live');

    // 3. Test editing with critical settings (should trigger restart)
    console.log('\n3. Testing edit with critical settings (bitrate change)...');
    
    // Simulate the edit request
    const updateData = {
      bitrate: 3500, // Changed from 2500 to 3500
      title: 'Updated Test Stream'
    };

    console.log('Update data:', updateData);
    
    // Check the logic that would be executed
    const stream = await Stream.findById(testStream.id);
    const isCurrentlyLive = stream.status === 'live';
    const criticalSettings = ['bitrate', 'resolution', 'fps', 'video_id', 'rtmp_url', 'stream_key', 'orientation', 'loop_video'];
    const criticalSettingsChanged = criticalSettings.some(setting => updateData.hasOwnProperty(setting));
    
    console.log(`Current status: ${stream.status}`);
    console.log(`Is currently live: ${isCurrentlyLive}`);
    console.log(`Critical settings changed: ${criticalSettingsChanged}`);
    
    if (isCurrentlyLive && criticalSettingsChanged) {
      console.log('✅ Logic correctly identifies that stream should be restarted');
    } else {
      console.log('❌ Logic failed to identify restart requirement');
    }

    // 4. Test editing with non-critical settings (should NOT trigger restart)
    console.log('\n4. Testing edit with non-critical settings...');
    
    const nonCriticalUpdateData = {
      title: 'Just Title Change',
      schedule_timezone: 'Asia/Jakarta'
    };

    const nonCriticalSettingsChanged = criticalSettings.some(setting => nonCriticalUpdateData.hasOwnProperty(setting));
    console.log(`Non-critical update data:`, nonCriticalUpdateData);
    console.log(`Critical settings changed: ${nonCriticalSettingsChanged}`);
    
    if (!nonCriticalSettingsChanged) {
      console.log('✅ Logic correctly identifies that stream should NOT be restarted');
    } else {
      console.log('❌ Logic incorrectly identifies restart requirement for non-critical changes');
    }

    // 5. Test editing when stream is offline (should NOT trigger restart)
    console.log('\n5. Testing edit when stream is offline...');
    
    await Stream.updateStatus(testStream.id, 'offline', testUserId);
    const offlineStream = await Stream.findById(testStream.id);
    const isOffline = offlineStream.status !== 'live';
    
    console.log(`Stream status: ${offlineStream.status}`);
    console.log(`Is offline: ${isOffline}`);
    
    if (isOffline) {
      console.log('✅ Logic correctly identifies that offline stream should NOT be restarted');
    } else {
      console.log('❌ Logic failed - stream should be offline');
    }

    // 6. Cleanup
    console.log('\n6. Cleaning up test data...');
    await Stream.delete(testStream.id, testUserId);
    console.log('✅ Test stream deleted');

    console.log('\n🎉 All tests passed! The stream edit fix should work correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testStreamEditFix().then(() => {
    console.log('\n✅ Test completed');
    process.exit(0);
  }).catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
}

module.exports = { testStreamEditFix };
