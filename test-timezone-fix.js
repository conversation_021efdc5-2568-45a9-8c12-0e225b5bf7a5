// Test script to verify timezone fix for all Indonesian timezones
const { datetimeLocalToUTC } = require('./utils/timezone');

console.log('=== Timezone Fix Verification ===');

// Test all Indonesian timezones
const timezones = [
  'Asia/Jakarta',   // UTC+7 (WIB)
  'Asia/Makassar',  // UTC+8 (WITA)
  'Asia/Jayapura'   // UTC+9 (WIT)
];

// Test current time + 2 minutes for each timezone
const now = new Date();
const twoMinutesLater = new Date(now.getTime() + (2 * 60 * 1000));

// Format as datetime-local input (YYYY-MM-DDTHH:mm)
const year = twoMinutesLater.getFullYear();
const month = String(twoMinutesLater.getMonth() + 1).padStart(2, '0');
const day = String(twoMinutesLater.getDate()).padStart(2, '0');
const hours = String(twoMinutesLater.getHours()).padStart(2, '0');
const minutes = String(twoMinutesLater.getMinutes()).padStart(2, '0');

const testInput = `${year}-${month}-${day}T${hours}:${minutes}`;

console.log(`Current time: ${now.toISOString()}`);
console.log(`Test input (2 min later): ${testInput}`);
console.log(`Expected time: ${twoMinutesLater.toISOString()}`);

timezones.forEach(timezone => {
  console.log(`\n--- Testing ${timezone} ---`);
  
  // Backend conversion
  const backendUTC = datetimeLocalToUTC(testInput, timezone);
  const backendDate = new Date(backendUTC);
  const minimumTime = new Date(now.getTime() + (1 * 60 * 1000));
  const backendValid = backendDate > minimumTime;
  
  // Frontend conversion (FIXED)
  const frontendInputDate = new Date(testInput);
  let frontendScheduleTimeUTC;
  
  if (timezone && timezone !== 'UTC') {
    const systemTimezone = 'Asia/Jakarta'; // Server timezone
    
    if (timezone === systemTimezone) {
      frontendScheduleTimeUTC = frontendInputDate;
    } else {
      const timezoneOffsets = {
        'Asia/Jakarta': 7,    // UTC+7 (WIB)
        'Asia/Makassar': 8,   // UTC+8 (WITA)
        'Asia/Jayapura': 9    // UTC+9 (WIT)
      };

      const systemOffset = timezoneOffsets[systemTimezone] || 7;
      const targetOffset = timezoneOffsets[timezone] || 7;
      const offsetDiff = targetOffset - systemOffset; // Difference in hours

      frontendScheduleTimeUTC = new Date(frontendInputDate.getTime() - (offsetDiff * 60 * 60 * 1000));
    }
  } else {
    frontendScheduleTimeUTC = new Date(testInput);
  }
  
  const frontendValid = frontendScheduleTimeUTC > minimumTime;
  
  console.log(`Backend UTC: ${backendUTC}`);
  console.log(`Frontend UTC: ${frontendScheduleTimeUTC.toISOString()}`);
  console.log(`Backend valid: ${backendValid}`);
  console.log(`Frontend valid: ${frontendValid}`);
  console.log(`Match: ${backendUTC === frontendScheduleTimeUTC.toISOString() ? '✅' : '❌'}`);
  console.log(`Both valid: ${backendValid && frontendValid ? '✅' : '❌'}`);
});

console.log('\n=== Summary ===');
console.log('✅ All timezone conversions should now match between frontend and backend');
console.log('✅ Schedule validation should work correctly for all Indonesian timezones');
console.log('✅ Debug logging has been added for troubleshooting');
