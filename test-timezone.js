// Test script to verify timezone conversion logic
const { datetimeLocalToUTC, formatDateTimeWithTimezone, convertToUTC, getTimezoneOffset } = require('./utils/timezone');

// Test case: June 2, 2025 6:00 WIB (Asia/Jakarta)
const testInput = '2025-06-02T06:00';
const testTimezone = 'Asia/Jakarta';

console.log('=== Timezone Conversion Test ===');
console.log(`Input: ${testInput} (${testTimezone})`);

// Test the conversion step by step
console.log('\n--- Step by step conversion ---');
const inputDate = new Date(testInput);
console.log(`1. Input Date Object: ${inputDate}`);
console.log(`   Input Date ISO: ${inputDate.toISOString()}`);

const offset = getTimezoneOffset(testTimezone, inputDate);
console.log(`2. Timezone Offset: ${offset} minutes`);

const convertedUTC = convertToUTC(testInput, testTimezone);
console.log(`3. Converted to UTC: ${convertedUTC}`);
console.log(`   UTC ISO: ${convertedUTC.toISOString()}`);

// Convert to UTC using our function
const utcResult = datetimeLocalToUTC(testInput, testTimezone);
console.log(`4. UTC Result: ${utcResult}`);

// Parse the UTC result and format it back
if (utcResult) {
  const utcDate = new Date(utcResult);
  console.log(`5. UTC Date Object: ${utcDate}`);

  // Test the correct way to display in target timezone
  console.log('\n--- Display Tests ---');

  // Method 1: Using Intl API directly (this should work correctly)
  const correctDisplayDate = utcDate.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    timeZone: testTimezone
  });

  const correctDisplayTime = utcDate.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
    timeZone: testTimezone
  });

  console.log(`Correct Display Date: ${correctDisplayDate}`);
  console.log(`Correct Display Time: ${correctDisplayTime}`);
  console.log(`Correct Combined: Starts ${correctDisplayDate} • ${correctDisplayTime}`);

  // Method 2: Using our utility function
  const displayResult = formatDateTimeWithTimezone(utcDate, testTimezone);
  console.log(`Utility Display: ${displayResult}`);

  // Method 3: Test the Intl API approach used in the frontend
  const formatter = new Intl.DateTimeFormat('sv-SE', {
    timeZone: testTimezone,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });

  const parts = formatter.formatToParts(utcDate);
  const year = parts.find(p => p.type === 'year').value;
  const month = parts.find(p => p.type === 'month').value;
  const day = parts.find(p => p.type === 'day').value;
  const hour = parts.find(p => p.type === 'hour').value;
  const minute = parts.find(p => p.type === 'minute').value;

  const formattedForEdit = `${year}-${month}-${day}T${hour}:${minute}`;
  console.log(`Frontend Edit Format: ${formattedForEdit}`);
}

console.log('\n=== Expected vs Actual ===');
console.log('Expected: June 2, 2025 6:00 WIB should display as "Starts Jun 2, 2025 • 06:00"');
console.log('Expected: When editing, should show "2025-06-02T06:00" with timezone "Asia/Jakarta"');
console.log('=== Test Complete ===');
