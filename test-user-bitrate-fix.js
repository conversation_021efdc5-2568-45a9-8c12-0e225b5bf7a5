#!/usr/bin/env node

/**
 * Test Script: User Bitrate Fix Verification
 * Tests that user-specified bitrate (10000 kbps) is properly applied
 */

console.log('🎯 StreamOnPod - User Bitrate Fix Test\n');

// Mock video data
const testVideo = {
  id: 1,
  name: 'LOFI COFFEE',
  resolution: '1920x1080',
  bitrate: 5000, // Original video bitrate
  codec: 'h264',
  video_codec: 'h264',
  container: 'mp4'
};

// Mock stream data with user settings
const testStreams = [
  {
    id: 'stream-1',
    title: 'Test Stream - User 10000k',
    bitrate: 10000, // User specified 10000 kbps
    resolution: '1920x1080',
    fps: 30,
    use_advanced_settings: true
  },
  {
    id: 'stream-2', 
    title: 'Test Stream - User 6800k',
    bitrate: 6800, // YouTube recommended
    resolution: '1920x1080',
    fps: 30,
    use_advanced_settings: true
  },
  {
    id: 'stream-3',
    title: 'Test Stream - No User Setting',
    bitrate: null, // No user setting, should use optimal
    resolution: '1920x1080',
    fps: 30,
    use_advanced_settings: false
  },
  {
    id: 'stream-4',
    title: 'Test Stream - Extreme High',
    bitrate: 20000, // Very high bitrate
    resolution: '1920x1080',
    fps: 30,
    use_advanced_settings: true
  }
];

// Copy the fixed bitrate function
function getOptimalCopyModeBitrate(video, userBitrate = null) {
  // For copy mode, we can use higher bitrates since no CPU encoding is involved
  // Prioritize user setting, then fall back to video-based optimization
  
  // If user specified a bitrate, use it (with reasonable caps for bandwidth)
  if (userBitrate && userBitrate > 0) {
    // Allow high bitrates for copy mode, cap at 15Mbps for bandwidth safety
    return Math.min(userBitrate, 15000);
  }
  
  if (!video.resolution) {
    return 4000; // Default high bitrate for copy mode
  }
  
  const [width, height] = video.resolution.split('x').map(Number);
  const totalPixels = width * height;
  
  // Calculate optimal bitrate based on resolution (fallback when no user setting)
  // Higher resolution = higher bitrate (since copy mode doesn't use CPU)
  if (totalPixels >= 1920 * 1080) {
    // 1080p and above - use high bitrate for best quality
    return Math.min(video.bitrate || 6000, 12000); // Increased cap for 1080p+
  } else if (totalPixels >= 1280 * 720) {
    // 720p - use medium-high bitrate
    return Math.min(video.bitrate || 4000, 8000); // Increased cap for 720p
  } else if (totalPixels >= 854 * 480) {
    // 480p - use medium bitrate
    return Math.min(video.bitrate || 2500, 5000); // Increased cap for 480p
  } else {
    // Lower resolutions - use moderate bitrate
    return Math.min(video.bitrate || 1500, 3000); // Increased cap for lower res
  }
}

// Test function to check if video can use copy mode
function canUseCopyMode(video) {
  if (!video.codec) return false;
  
  const codecLower = video.codec.toLowerCase();
  
  // H.264/AVC can use copy mode
  if (codecLower.includes('h264') || codecLower.includes('avc')) {
    return true;
  }
  
  return false;
}

// Simulate FFmpeg command generation
function generateFFmpegCommand(video, stream) {
  const copyMode = canUseCopyMode(video);
  
  if (!copyMode) {
    return {
      mode: 'RE-ENCODE',
      bitrate: stream.bitrate || 2500,
      command: `ffmpeg -i input.mp4 -c:v libx264 -b:v ${stream.bitrate || 2500}k -f flv rtmp://...`
    };
  }
  
  // Copy mode - use user bitrate or optimal
  const userBitrate = stream.bitrate;
  const optimalBitrate = getOptimalCopyModeBitrate(video, userBitrate);
  
  return {
    mode: 'COPY',
    bitrate: optimalBitrate,
    userBitrate: userBitrate,
    command: `ffmpeg -i input.mp4 -c:v copy -c:a copy -f flv rtmp://...`
  };
}

// Run tests
console.log('📊 Testing User Bitrate Fix:\n');

testStreams.forEach(stream => {
  const result = generateFFmpegCommand(testVideo, stream);
  
  console.log(`🎬 ${stream.title}`);
  console.log(`   User Setting: ${stream.bitrate ? stream.bitrate + 'k' : 'None'}`);
  console.log(`   Video Original: ${testVideo.bitrate}k`);
  console.log(`   Mode: ${result.mode === 'COPY' ? '🟢 COPY' : '🔴 RE-ENCODE'}`);
  console.log(`   Final Bitrate: ${result.bitrate}k`);
  
  if (result.mode === 'COPY' && result.userBitrate) {
    const respected = result.bitrate === Math.min(result.userBitrate, 15000);
    console.log(`   User Setting Respected: ${respected ? '✅ YES' : '❌ NO'}`);
    
    if (result.userBitrate === 10000) {
      console.log(`   🎯 YouTube Test: ${result.bitrate >= 6800 ? '✅ PASS' : '❌ FAIL'} (needs ≥6800k)`);
    }
  }
  
  console.log('');
});

// Specific test for the YouTube issue
console.log('🔍 YouTube Issue Analysis:\n');

const youtubeStream = {
  bitrate: 10000, // User set 10000k
  resolution: '1920x1080',
  use_advanced_settings: true
};

const youtubeResult = generateFFmpegCommand(testVideo, youtubeStream);

console.log('📺 YouTube Stream Test:');
console.log(`   User Setting: ${youtubeStream.bitrate}k`);
console.log(`   System Will Use: ${youtubeResult.bitrate}k`);
console.log(`   YouTube Recommended: 6800k`);
console.log(`   YouTube Received: 2209k (BEFORE FIX)`);
console.log(`   Expected After Fix: ${youtubeResult.bitrate}k`);

const improvement = ((youtubeResult.bitrate - 2209) / 2209) * 100;
console.log(`   Improvement: +${Math.round(improvement)}%`);

if (youtubeResult.bitrate >= 6800) {
  console.log('   ✅ FIXED: Will meet YouTube recommendation');
} else {
  console.log('   ❌ ISSUE: Still below YouTube recommendation');
}

// Before vs After comparison
console.log('\n📈 Before vs After Fix:\n');

console.log('❌ BEFORE (Broken):');
console.log('   User sets: 10000k');
console.log('   System uses: getOptimalCopyModeBitrate(video) = 5000k (video bitrate)');
console.log('   YouTube receives: ~2209k (unknown issue)');
console.log('   Result: User setting ignored');

console.log('\n✅ AFTER (Fixed):');
console.log('   User sets: 10000k');
console.log('   System uses: getOptimalCopyModeBitrate(video, 10000) = 10000k');
console.log('   YouTube should receive: 10000k');
console.log('   Result: User setting respected');

// Recommendations
console.log('\n🎯 Recommendations:\n');
console.log('1. ✅ User bitrate setting now properly passed to copy mode');
console.log('2. ✅ Increased bitrate caps for better quality');
console.log('3. ✅ 15Mbps cap prevents bandwidth issues');
console.log('4. 🔍 Monitor actual YouTube bitrate after deployment');
console.log('5. 🔍 Check for other bottlenecks if issue persists');

console.log('\n💡 Additional Checks Needed:');
console.log('• Verify stream.bitrate is correctly saved to database');
console.log('• Check if FFmpeg command is properly executed');
console.log('• Monitor network bandwidth during streaming');
console.log('• Verify RTMP server can handle high bitrates');

console.log('\n✅ User Bitrate Fix Test Complete!');
