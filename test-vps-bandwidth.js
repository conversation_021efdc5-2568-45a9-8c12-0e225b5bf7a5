// VPS Bandwidth Test - StreamOnPod
// Test upload bandwidth to determine if VPS can handle high bitrate streaming

const https = require('https');
const http = require('http');
const { spawn } = require('child_process');

console.log('🌐 Testing VPS Bandwidth for High Bitrate Streaming\n');

// Test 1: Basic Network Info
function getNetworkInfo() {
  return new Promise((resolve) => {
    console.log('📊 Network Interface Information:');
    
    const ifconfig = spawn('ifconfig', [], { stdio: ['ignore', 'pipe', 'pipe'] });
    let output = '';
    
    ifconfig.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    ifconfig.on('close', () => {
      // Extract network interfaces
      const interfaces = output.split('\n').filter(line => 
        line.includes('inet ') && !line.includes('127.0.0.1')
      );
      
      interfaces.forEach(line => {
        const match = line.match(/inet (\d+\.\d+\.\d+\.\d+)/);
        if (match) {
          console.log(`   IP Address: ${match[1]}`);
        }
      });
      
      resolve();
    });
    
    ifconfig.on('error', () => {
      console.log('   Could not get network info');
      resolve();
    });
  });
}

// Test 2: Speed Test using speedtest-cli
function runSpeedTest() {
  return new Promise((resolve) => {
    console.log('\n🚀 Running Speed Test...');
    console.log('   (This may take 30-60 seconds)');
    
    const speedtest = spawn('speedtest-cli', ['--simple'], { stdio: ['ignore', 'pipe', 'pipe'] });
    let output = '';
    
    speedtest.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    speedtest.on('close', (code) => {
      if (code === 0 && output) {
        console.log('\n📈 Speed Test Results:');
        const lines = output.trim().split('\n');
        lines.forEach(line => {
          if (line.includes('Download:')) {
            const speed = line.match(/(\d+\.\d+)/);
            console.log(`   📥 Download: ${speed ? speed[1] : 'Unknown'} Mbps`);
          } else if (line.includes('Upload:')) {
            const speed = line.match(/(\d+\.\d+)/);
            const uploadSpeed = speed ? parseFloat(speed[1]) : 0;
            console.log(`   📤 Upload: ${uploadSpeed} Mbps`);
            
            // Analyze upload speed for streaming
            console.log('\n🎯 Streaming Analysis:');
            if (uploadSpeed >= 15) {
              console.log('   ✅ EXCELLENT: Can handle 10000+ kbps streaming');
            } else if (uploadSpeed >= 12) {
              console.log('   ✅ GOOD: Can handle 10000 kbps streaming');
            } else if (uploadSpeed >= 8) {
              console.log('   ⚠️  MODERATE: Recommended max 6800 kbps');
            } else if (uploadSpeed >= 5) {
              console.log('   ⚠️  LIMITED: Recommended max 4000 kbps');
            } else {
              console.log('   ❌ POOR: High bitrate streaming not recommended');
            }
          } else if (line.includes('Ping:')) {
            const ping = line.match(/(\d+\.\d+)/);
            const pingMs = ping ? parseFloat(ping[1]) : 0;
            console.log(`   🏓 Ping: ${pingMs} ms`);
            
            if (pingMs <= 50) {
              console.log('   ✅ Low latency - Good for streaming');
            } else if (pingMs <= 100) {
              console.log('   ⚠️  Moderate latency - May affect streaming');
            } else {
              console.log('   ❌ High latency - May cause streaming issues');
            }
          }
        });
      } else {
        console.log('   ❌ Speed test failed or speedtest-cli not installed');
        console.log('   💡 Install with: sudo apt install speedtest-cli');
      }
      resolve();
    });
    
    speedtest.on('error', () => {
      console.log('   ❌ speedtest-cli not found');
      console.log('   💡 Install with: sudo apt install speedtest-cli');
      resolve();
    });
  });
}

// Test 3: Test connection to YouTube RTMP servers
function testYouTubeConnection() {
  return new Promise((resolve) => {
    console.log('\n🎥 Testing YouTube RTMP Server Connection...');
    
    const servers = [
      'a.rtmp.youtube.com',
      'b.rtmp.youtube.com', 
      'c.rtmp.youtube.com'
    ];
    
    let completed = 0;
    
    servers.forEach((server, index) => {
      const start = Date.now();
      const req = http.request({
        hostname: server,
        port: 1935,
        timeout: 5000
      });
      
      req.on('connect', () => {
        const latency = Date.now() - start;
        console.log(`   ✅ ${server}: ${latency}ms`);
        req.destroy();
        completed++;
        if (completed === servers.length) resolve();
      });
      
      req.on('error', () => {
        console.log(`   ❌ ${server}: Connection failed`);
        completed++;
        if (completed === servers.length) resolve();
      });
      
      req.on('timeout', () => {
        console.log(`   ⏱️  ${server}: Timeout (>5000ms)`);
        req.destroy();
        completed++;
        if (completed === servers.length) resolve();
      });
      
      req.end();
    });
  });
}

// Test 4: Bandwidth recommendations
function showRecommendations() {
  console.log('\n💡 BANDWIDTH RECOMMENDATIONS:\n');
  
  console.log('📊 Bitrate vs Required Upload Speed:');
  console.log('   • 2500 kbps  → 3.5 Mbps upload minimum');
  console.log('   • 4000 kbps  → 5.5 Mbps upload minimum');
  console.log('   • 6800 kbps  → 9.0 Mbps upload minimum');
  console.log('   • 10000 kbps → 12.0 Mbps upload minimum');
  console.log('   • 15000 kbps → 18.0 Mbps upload minimum');
  
  console.log('\n🎯 YouTube Quality Requirements:');
  console.log('   • 1080p 30fps: 6800 kbps minimum');
  console.log('   • 1080p 60fps: 9000 kbps minimum');
  console.log('   • 1440p 30fps: 9000 kbps minimum');
  console.log('   • 1440p 60fps: 13500 kbps minimum');
  
  console.log('\n⚠️  TROUBLESHOOTING TIPS:');
  console.log('   1. If upload < 12 Mbps → Lower bitrate to 6800k');
  console.log('   2. If ping > 100ms → Check VPS location');
  console.log('   3. If YouTube connection fails → Check firewall');
  console.log('   4. If speed varies → Test at different times');
}

// Run all tests
async function runAllTests() {
  try {
    await getNetworkInfo();
    await runSpeedTest();
    await testYouTubeConnection();
    showRecommendations();
    
    console.log('\n🔧 NEXT STEPS:');
    console.log('   1. Check your VPS upload speed above');
    console.log('   2. If speed < 12 Mbps, lower bitrate to 6800k');
    console.log('   3. Test streaming with lower bitrate first');
    console.log('   4. Monitor YouTube Studio for actual received bitrate');
    
  } catch (error) {
    console.error('Error running tests:', error.message);
  }
}

runAllTests();
