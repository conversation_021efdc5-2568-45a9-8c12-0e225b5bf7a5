<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Chunked Upload</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .progress { width: 100%; height: 20px; background: #f0f0f0; margin: 10px 0; }
        .progress-bar { height: 100%; background: #007bff; width: 0%; transition: width 0.3s; }
        .log { background: #f8f9fa; padding: 10px; margin: 10px 0; border: 1px solid #dee2e6; max-height: 300px; overflow-y: auto; }
        button { padding: 10px 20px; margin: 5px; }
        input[type="file"] { margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Test Chunked Upload</h1>
    
    <input type="file" id="fileInput" accept="video/*">
    <br>
    <button onclick="testChunkedUpload()">Test Chunked Upload</button>
    <button onclick="clearLog()">Clear Log</button>
    
    <div class="progress">
        <div class="progress-bar" id="progressBar"></div>
    </div>
    <div id="progressText">Ready</div>
    
    <div class="log" id="log"></div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function updateProgress(percent, text) {
            document.getElementById('progressBar').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
        }

        async function testChunkedUpload() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                log('❌ Please select a file first');
                return;
            }

            log(`🚀 Starting chunked upload test for: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
            
            const CHUNK_SIZE = 50 * 1024 * 1024; // 50MB
            const totalChunks = Math.ceil(file.size / CHUNK_SIZE);
            
            log(`📊 File will be split into ${totalChunks} chunks of ${CHUNK_SIZE / 1024 / 1024}MB each`);

            try {
                // Step 1: Initialize upload
                log('🔄 Initializing upload...');
                updateProgress(0, 'Initializing...');
                
                const initResponse = await fetch('/api/videos/upload/init', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        filename: file.name,
                        fileSize: file.size,
                        totalChunks: totalChunks
                    })
                });

                const initData = await initResponse.json();
                if (!initData.success) {
                    throw new Error(initData.error || 'Failed to initialize upload');
                }

                const uploadId = initData.uploadId;
                log(`✅ Upload initialized with ID: ${uploadId}`);

                // Step 2: Upload chunks
                for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
                    const start = chunkIndex * CHUNK_SIZE;
                    const end = Math.min(start + CHUNK_SIZE, file.size);
                    const chunk = file.slice(start, end);

                    const chunkProgress = Math.round((chunkIndex / totalChunks) * 90); // Reserve 10% for finalization
                    updateProgress(chunkProgress, `Uploading chunk ${chunkIndex + 1}/${totalChunks}`);
                    log(`📦 Uploading chunk ${chunkIndex + 1}/${totalChunks} (${(chunk.size / 1024 / 1024).toFixed(2)} MB)`);

                    const chunkFormData = new FormData();
                    chunkFormData.append('chunk', chunk);
                    chunkFormData.append('uploadId', uploadId);
                    chunkFormData.append('chunkIndex', chunkIndex.toString());

                    const chunkResponse = await fetch('/api/videos/upload/chunk', {
                        method: 'POST',
                        body: chunkFormData
                    });

                    if (!chunkResponse.ok) {
                        const errorText = await chunkResponse.text();
                        log(`❌ Chunk ${chunkIndex + 1} upload failed: ${chunkResponse.status} ${chunkResponse.statusText}`);
                        log(`❌ Error response: ${errorText}`);
                        throw new Error(`Failed to upload chunk ${chunkIndex + 1}: ${chunkResponse.status} ${chunkResponse.statusText}`);
                    }

                    const chunkData = await chunkResponse.json();
                    if (!chunkData.success) {
                        throw new Error(chunkData.error || `Failed to upload chunk ${chunkIndex + 1}`);
                    }

                    log(`✅ Chunk ${chunkIndex + 1} uploaded successfully (${chunkData.received}/${chunkData.total})`);
                }

                // Step 3: Finalize upload
                log('🔄 Finalizing upload...');
                updateProgress(95, 'Finalizing upload...');

                const finalizeResponse = await fetch('/api/videos/upload/finalize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        uploadId: uploadId
                    })
                });

                if (!finalizeResponse.ok) {
                    const errorText = await finalizeResponse.text();
                    log(`❌ Finalization failed: ${finalizeResponse.status} ${finalizeResponse.statusText}`);
                    log(`❌ Error response: ${errorText}`);
                    throw new Error(`Failed to finalize upload: ${finalizeResponse.status} ${finalizeResponse.statusText}`);
                }

                const finalizeData = await finalizeResponse.json();
                if (!finalizeData.success) {
                    throw new Error(finalizeData.error || 'Failed to finalize upload');
                }

                updateProgress(100, 'Upload completed!');
                log(`🎉 Upload completed successfully!`);
                log(`📹 Video created: ${finalizeData.video.title} (ID: ${finalizeData.video.id})`);

            } catch (error) {
                log(`❌ Upload failed: ${error.message}`);
                updateProgress(0, 'Upload failed');
            }
        }
    </script>
</body>
</html>
