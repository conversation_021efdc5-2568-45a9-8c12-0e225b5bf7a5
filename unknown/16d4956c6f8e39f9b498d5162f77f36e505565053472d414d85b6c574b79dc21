#!/usr/bin/env node

/**
 * Test script for CPU allocation functionality
 * Tests the CPU manager and allocation logic
 */

const cpuManager = require('../utils/cpuManager');
const cpuConfig = require('../config/cpuConfig');

console.log('🧪 Testing CPU Allocation System\n');

// Test 1: Basic CPU Manager functionality
console.log('=== Test 1: CPU Manager Initialization ===');
try {
  const allocation = cpuManager.getAllocationInfo();
  console.log('✅ CPU Manager initialized successfully');
  console.log(`📊 Total cores: ${allocation.totalCores}`);
  console.log(`🔧 Encoding cores: ${allocation.encoding.cores} (${allocation.encoding.range})`);
  console.log(`📺 Streaming cores: ${allocation.streaming.cores} (${allocation.streaming.range})`);
  console.log(`🧵 Encoding threads: ${allocation.encoding.threads}`);
  console.log(`🧵 Streaming threads: ${allocation.streaming.threads}`);
} catch (error) {
  console.error('❌ CPU Manager initialization failed:', error.message);
}

console.log('\n=== Test 2: Platform Support ===');
const isSupported = cpuManager.isSupported();
console.log(`🖥️  Platform: ${process.platform}`);
console.log(`⚙️  CPU Affinity supported: ${isSupported ? '✅ Yes' : '❌ No'}`);

if (isSupported) {
  console.log('🎯 CPU affinity will be applied for optimal performance');
} else {
  console.log('⚠️  CPU affinity not supported, using threading only');
}

console.log('\n=== Test 3: FFmpeg Arguments Modification ===');
try {
  // Test encoding args
  const sampleEncodingArgs = [
    '-hwaccel', 'auto',
    '-i', 'input.mp4',
    '-c:v', 'libx264',
    '-preset', 'medium',
    'output.mp4'
  ];
  
  const encodingArgsWithCPU = cpuManager.addEncodingCPUAllocation([...sampleEncodingArgs]);
  console.log('✅ Encoding args modified successfully');
  console.log('📝 Added CPU parameters:', encodingArgsWithCPU.filter((arg, i) => 
    !sampleEncodingArgs.includes(arg) || 
    (i > 0 && !sampleEncodingArgs.includes(encodingArgsWithCPU[i-1]))
  ));

  // Test streaming args
  const sampleStreamingArgs = [
    '-hwaccel', 'auto',
    '-re',
    '-i', 'input.mp4',
    '-c:v', 'copy',
    '-c:a', 'copy',
    '-f', 'flv',
    'rtmp://example.com/live/key'
  ];
  
  const streamingArgsWithCPU = cpuManager.addStreamingCPUAllocation([...sampleStreamingArgs]);
  console.log('✅ Streaming args modified successfully');
  console.log('📝 Added CPU parameters:', streamingArgsWithCPU.filter((arg, i) => 
    !sampleStreamingArgs.includes(arg) || 
    (i > 0 && !sampleStreamingArgs.includes(streamingArgsWithCPU[i-1]))
  ));
} catch (error) {
  console.error('❌ FFmpeg args modification failed:', error.message);
}

console.log('\n=== Test 4: CPU Configuration ===');
try {
  const config = cpuConfig.getAllocation();
  console.log('✅ CPU Configuration loaded successfully');
  console.log(`📊 Configuration: ${config.encoding.cores} encoding + ${config.streaming.cores} streaming cores`);
  
  const recommendations = cpuConfig.getRecommendations();
  console.log(`🎯 Encoding performance: ${recommendations.recommendations.encoding.performance}`);
  console.log(`🎯 Streaming performance: ${recommendations.recommendations.streaming.performance}`);
  console.log(`🎯 Overall status: ${recommendations.recommendations.overall.status}`);
} catch (error) {
  console.error('❌ CPU Configuration failed:', error.message);
}

console.log('\n=== Test 5: Scaling Scenarios ===');
try {
  const scenarios = cpuConfig.getScenarios();
  console.log('✅ Scaling scenarios generated successfully');
  console.log('\n📈 Server Scaling Examples:');
  
  const testCores = [4, 8, 12, 16, 24];
  testCores.forEach(cores => {
    const scenario = scenarios[`${cores}_cores`];
    if (scenario) {
      console.log(`   ${cores} cores: ${scenario.encoding.cores} encoding (${scenario.encoding.range}) + ${scenario.streaming.cores} streaming (${scenario.streaming.range})`);
    }
  });
} catch (error) {
  console.error('❌ Scaling scenarios failed:', error.message);
}

console.log('\n=== Test 6: CPU Usage Monitoring ===');
cpuManager.getCPUUsage().then(usage => {
  console.log('✅ CPU usage monitoring test completed');
  console.log(`📊 Monitoring supported: ${usage.supported ? '✅ Yes' : '❌ No'}`);
  if (usage.supported) {
    console.log(`📈 Overall CPU usage: ${usage.overall}%`);
  } else {
    console.log('⚠️  CPU monitoring not available on this platform');
  }
}).catch(error => {
  console.error('❌ CPU usage monitoring failed:', error.message);
});

console.log('\n=== Test 7: Process Spawning (Simulation) ===');
try {
  // Simulate process spawning without actually running FFmpeg
  const mockCommand = 'echo';
  const mockArgs = ['CPU allocation test'];
  const mockOptions = { stdio: 'inherit' };
  
  console.log('🔄 Testing process spawn with CPU affinity...');
  
  if (cpuManager.isSupported()) {
    console.log('✅ Would use taskset for CPU affinity on Linux');
    console.log(`📝 Encoding command: taskset -c ${cpuManager.getAllocationInfo().encoding.range} ${mockCommand}`);
    console.log(`📝 Streaming command: taskset -c ${cpuManager.getAllocationInfo().streaming.range} ${mockCommand}`);
  } else {
    console.log('✅ Would use standard spawn (no CPU affinity)');
    console.log(`📝 Command: ${mockCommand} ${mockArgs.join(' ')}`);
  }
} catch (error) {
  console.error('❌ Process spawning simulation failed:', error.message);
}

console.log('\n=== Test Summary ===');
const allocation = cpuManager.getAllocationInfo();
console.log(`🎯 Your server has ${allocation.totalCores} CPU cores`);
console.log(`🔧 Encoding allocation: ${allocation.encoding.cores} cores (${allocation.encoding.range})`);
console.log(`📺 Streaming allocation: ${allocation.streaming.cores} cores (${allocation.streaming.range})`);
console.log(`⚙️  CPU affinity: ${cpuManager.isSupported() ? 'Supported' : 'Not supported'}`);

const recommendations = cpuManager.getRecommendedSettings();
console.log(`\n💡 Recommendations:`);
console.log(`   Encoding: ${recommendations.encoding.recommended}`);
console.log(`   Streaming: ${recommendations.streaming.recommended}`);
console.log(`   Platform: ${recommendations.platform.recommendation}`);

console.log('\n✅ CPU Allocation test completed successfully!');
console.log('\n📚 For more information, see: docs/CPU_ALLOCATION_GUIDE.md');
