const { db } = require('./db/database');

/**
 * Update referral balance for a specific user
 * Usage: node update_balance.js <username> <amount>
 * Example: node update_balance.js kimdogi 50000
 */

async function updateUserBalance(username, amount) {
  try {
    console.log(`🔄 Updating referral balance for user: ${username}`);
    
    // First, check if user exists
    const user = await new Promise((resolve, reject) => {
      db.get(
        `SELECT id, username, referral_balance FROM users WHERE username = ?`,
        [username],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!user) {
      console.error(`❌ User '${username}' not found`);
      process.exit(1);
    }

    console.log(`📋 Current balance for ${username}: Rp ${user.referral_balance.toLocaleString('id-ID')}`);

    // Update the balance
    const result = await new Promise((resolve, reject) => {
      db.run(
        `UPDATE users SET referral_balance = referral_balance + ? WHERE username = ?`,
        [amount, username],
        function(err) {
          if (err) reject(err);
          else resolve(this);
        }
      );
    });

    console.log(`✅ Updated referral balance for ${username}`);
    console.log(`📊 Rows affected: ${result.changes}`);

    // Verify the update
    const updatedUser = await new Promise((resolve, reject) => {
      db.get(
        `SELECT username, referral_balance FROM users WHERE username = ?`,
        [username],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    console.log(`✅ Verification successful:`);
    console.log(`   Username: ${updatedUser.username}`);
    console.log(`   Balance: Rp ${updatedUser.referral_balance.toLocaleString('id-ID')}`);

  } catch (error) {
    console.error('❌ Error updating balance:', error);
    process.exit(1);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length < 2) {
  console.log('Usage: node update_balance.js <username> <amount>');
  console.log('Example: node update_balance.js kimdogi 50000');
  process.exit(1);
}

const username = args[0];
const amount = parseInt(args[1]);

if (isNaN(amount)) {
  console.error('❌ Amount must be a valid number');
  process.exit(1);
}

// Run the update
updateUserBalance(username, amount)
  .then(() => {
    console.log('🔒 Database connection closed');
    db.close();
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    db.close();
    process.exit(1);
  });
