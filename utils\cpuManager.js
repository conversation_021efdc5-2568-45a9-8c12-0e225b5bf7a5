const os = require('os');
const { spawn } = require('child_process');

class CPUManager {
  constructor() {
    this.totalCores = os.cpus().length;
    this.encodingCores = Math.floor(this.totalCores / 2);
    this.streamingCores = this.totalCores - this.encodingCores;
    
    // Core ranges (0-indexed)
    this.encodingCoreRange = this.generateCoreRange(0, this.encodingCores - 1);
    this.streamingCoreRange = this.generateCoreRange(this.encodingCores, this.totalCores - 1);
    
    console.log(`[CPUManager] Initialized with ${this.totalCores} cores:`);
    console.log(`[CPUManager] Encoding cores: ${this.encodingCores} (${this.encodingCoreRange})`);
    console.log(`[CPUManager] Streaming cores: ${this.streamingCores} (${this.streamingCoreRange})`);
  }

  /**
   * Generate core range string for taskset
   * @param {number} start - Start core index
   * @param {number} end - End core index
   * @returns {string} Core range string (e.g., "0-3" or "4-7")
   */
  generateCoreRange(start, end) {
    if (start === end) {
      return start.toString();
    }
    return `${start}-${end}`;
  }

  /**
   * Get CPU allocation configuration
   * @returns {Object} CPU allocation info
   */
  getAllocationInfo() {
    return {
      totalCores: this.totalCores,
      encoding: {
        cores: this.encodingCores,
        range: this.encodingCoreRange,
        threads: this.encodingCores
      },
      streaming: {
        cores: this.streamingCores,
        range: this.streamingCoreRange,
        threads: Math.min(this.streamingCores, 2) // Copy mode doesn't need many threads
      }
    };
  }

  /**
   * Add CPU affinity to FFmpeg arguments for encoding
   * @param {Array} ffmpegArgs - Original FFmpeg arguments
   * @returns {Array} Modified arguments with CPU allocation
   */
  addEncodingCPUAllocation(ffmpegArgs) {
    // Find input and output positions
    const inputIndex = ffmpegArgs.findIndex(arg => arg === '-i');
    const outputIndex = ffmpegArgs.findIndex((arg, index) =>
      index > inputIndex && !arg.startsWith('-') && arg.includes('.mp4')
    );

    if (inputIndex === -1) {
      console.warn('[CPUManager] Could not find input (-i) in FFmpeg args');
      return ffmpegArgs;
    }

    // Add input threading parameters before -i
    const inputCpuArgs = [
      '-thread_queue_size', '1024'
    ];

    // Add output threading parameters after input but before output file
    const outputCpuArgs = [
      '-threads', this.encodingCores.toString(),
      '-filter_threads', this.encodingCores.toString()
    ];

    // Insert input args before -i
    ffmpegArgs.splice(inputIndex, 0, ...inputCpuArgs);

    // Insert output args after input file but before codec options
    const codecIndex = ffmpegArgs.findIndex(arg => arg === '-c:v');
    if (codecIndex !== -1) {
      ffmpegArgs.splice(codecIndex, 0, ...outputCpuArgs);
    } else {
      // Fallback: insert after input file
      ffmpegArgs.splice(inputIndex + 3, 0, ...outputCpuArgs);
    }

    return ffmpegArgs;
  }

  /**
   * Add CPU affinity to FFmpeg arguments for streaming
   * @param {Array} ffmpegArgs - Original FFmpeg arguments
   * @returns {Array} Modified arguments with CPU allocation
   */
  addStreamingCPUAllocation(ffmpegArgs) {
    // Find input position
    const inputIndex = ffmpegArgs.findIndex(arg => arg === '-i');

    if (inputIndex === -1) {
      console.warn('[CPUManager] Could not find input (-i) in FFmpeg args');
      return ffmpegArgs;
    }

    // Add minimal threading for copy mode streaming
    const streamingThreads = Math.min(this.streamingCores, 2);

    // Add input threading parameters before -i
    const inputCpuArgs = [
      '-thread_queue_size', '512'
    ];

    // Add output threading parameters after input file
    const outputCpuArgs = [
      '-threads', streamingThreads.toString()
    ];

    // Insert input args before -i
    ffmpegArgs.splice(inputIndex, 0, ...inputCpuArgs);

    // Insert output args after input file but before codec options
    const codecIndex = ffmpegArgs.findIndex(arg => arg === '-c:v');
    if (codecIndex !== -1) {
      ffmpegArgs.splice(codecIndex, 0, ...outputCpuArgs);
    } else {
      // Fallback: insert after input file
      ffmpegArgs.splice(inputIndex + 3, 0, ...outputCpuArgs);
    }

    return ffmpegArgs;
  }

  /**
   * Spawn process with CPU affinity (Linux only)
   * @param {string} command - Command to execute
   * @param {Array} args - Command arguments
   * @param {Object} options - Spawn options
   * @param {string} processType - 'encoding' or 'streaming'
   * @returns {ChildProcess} Spawned process
   */
  spawnWithCPUAffinity(command, args, options = {}, processType = 'encoding') {
    const isLinux = process.platform === 'linux';
    
    if (isLinux && command.includes('ffmpeg')) {
      // Use taskset for CPU affinity on Linux
      const coreRange = processType === 'encoding' ? this.encodingCoreRange : this.streamingCoreRange;
      const tasksetArgs = ['-c', coreRange, command, ...args];
      
      console.log(`[CPUManager] Starting ${processType} process with CPU affinity: cores ${coreRange}`);
      return spawn('taskset', tasksetArgs, options);
    } else {
      // Fallback for non-Linux systems or non-FFmpeg commands
      console.log(`[CPUManager] Starting ${processType} process without CPU affinity (${process.platform})`);
      return spawn(command, args, options);
    }
  }

  /**
   * Get current CPU usage per core (Linux only)
   * @returns {Promise<Object>} CPU usage statistics
   */
  async getCPUUsage() {
    return new Promise((resolve) => {
      if (process.platform !== 'linux') {
        resolve({
          overall: 0,
          encoding: 0,
          streaming: 0,
          supported: false
        });
        return;
      }

      // Simple CPU usage check using /proc/stat
      const fs = require('fs');
      try {
        const stat = fs.readFileSync('/proc/stat', 'utf8');
        const lines = stat.split('\n');
        const cpuLine = lines[0];
        const values = cpuLine.split(/\s+/).slice(1).map(Number);
        
        const idle = values[3];
        const total = values.reduce((sum, val) => sum + val, 0);
        const usage = Math.round(((total - idle) / total) * 100);
        
        resolve({
          overall: usage,
          encoding: usage, // Simplified - would need more complex monitoring for per-core
          streaming: usage,
          supported: true
        });
      } catch (error) {
        resolve({
          overall: 0,
          encoding: 0,
          streaming: 0,
          supported: false,
          error: error.message
        });
      }
    });
  }

  /**
   * Check if CPU allocation is supported on current platform
   * @returns {boolean} True if CPU affinity is supported
   */
  isSupported() {
    return process.platform === 'linux';
  }

  /**
   * Get recommended settings based on server specs
   * @returns {Object} Recommended configuration
   */
  getRecommendedSettings() {
    const recommendations = {
      totalCores: this.totalCores,
      allocation: '50:50 split',
      encoding: {
        cores: this.encodingCores,
        recommended: this.encodingCores <= 4 ? 'Good for basic encoding' : 'Excellent for high-quality encoding',
        maxConcurrentJobs: Math.max(1, Math.floor(this.encodingCores / 2))
      },
      streaming: {
        cores: this.streamingCores,
        recommended: this.streamingCores >= 2 ? 'Sufficient for copy mode streaming' : 'Minimal but adequate',
        maxConcurrentStreams: Math.max(2, this.streamingCores)
      },
      platform: {
        current: process.platform,
        cpuAffinitySupported: this.isSupported(),
        recommendation: this.isSupported() ? 
          'CPU affinity will be applied for optimal performance' : 
          'Consider using Linux for better CPU management'
      }
    };

    return recommendations;
  }
}

// Create singleton instance
const cpuManager = new CPUManager();

module.exports = cpuManager;
