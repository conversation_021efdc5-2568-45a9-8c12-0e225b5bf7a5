const { v4: uuidv4 } = require('uuid');

/**
 * Centralized Error Handling Utilities
 * Provides consistent error handling, logging, and response formatting
 */

// Error types for consistent categorization
const ERROR_TYPES = {
  VALIDATION: 'VALIDATION_ERROR',
  AUTHENTICATION: 'AUTHENTICATION_ERROR',
  AUTHORIZATION: 'AUTHORIZATION_ERROR',
  NOT_FOUND: 'NOT_FOUND_ERROR',
  CONFLICT: 'CONFLICT_ERROR',
  RATE_LIMIT: 'RATE_LIMIT_ERROR',
  STORAGE: 'STORAGE_ERROR',
  STREAMING: 'STREAMING_ERROR',
  DATABASE: 'DATABASE_ERROR',
  EXTERNAL_API: 'EXTERNAL_API_ERROR',
  INTERNAL: 'INTERNAL_ERROR',
  NETWORK: 'NETWORK_ERROR',
  FILE_SYSTEM: 'FILE_SYSTEM_ERROR'
};

// HTTP status codes mapping
const STATUS_CODES = {
  [ERROR_TYPES.VALIDATION]: 400,
  [ERROR_TYPES.AUTHENTICATION]: 401,
  [ERROR_TYPES.AUTHORIZATION]: 403,
  [ERROR_TYPES.NOT_FOUND]: 404,
  [ERROR_TYPES.CONFLICT]: 409,
  [ERROR_TYPES.RATE_LIMIT]: 429,
  [ERROR_TYPES.STORAGE]: 413,
  [ERROR_TYPES.STREAMING]: 500,
  [ERROR_TYPES.DATABASE]: 500,
  [ERROR_TYPES.EXTERNAL_API]: 502,
  [ERROR_TYPES.INTERNAL]: 500,
  [ERROR_TYPES.NETWORK]: 503,
  [ERROR_TYPES.FILE_SYSTEM]: 500
};

// User-friendly error messages
const USER_MESSAGES = {
  [ERROR_TYPES.VALIDATION]: 'The provided information is invalid. Please check your input and try again.',
  [ERROR_TYPES.AUTHENTICATION]: 'Authentication failed. Please log in and try again.',
  [ERROR_TYPES.AUTHORIZATION]: 'You do not have permission to perform this action.',
  [ERROR_TYPES.NOT_FOUND]: 'The requested resource was not found.',
  [ERROR_TYPES.CONFLICT]: 'This action conflicts with existing data. Please check and try again.',
  [ERROR_TYPES.RATE_LIMIT]: 'Too many requests. Please wait a moment and try again.',
  [ERROR_TYPES.STORAGE]: 'Storage limit exceeded. Please free up space or upgrade your plan.',
  [ERROR_TYPES.STREAMING]: 'Streaming service is temporarily unavailable. Please try again later.',
  [ERROR_TYPES.DATABASE]: 'Database service is temporarily unavailable. Please try again later.',
  [ERROR_TYPES.EXTERNAL_API]: 'External service is temporarily unavailable. Please try again later.',
  [ERROR_TYPES.INTERNAL]: 'An internal error occurred. Please try again later.',
  [ERROR_TYPES.NETWORK]: 'Network connection error. Please check your connection and try again.',
  [ERROR_TYPES.FILE_SYSTEM]: 'File system error. Please try again later.'
};

/**
 * Enhanced Error class with additional context
 */
class AppError extends Error {
  constructor(message, type = ERROR_TYPES.INTERNAL, statusCode = null, details = null, context = {}) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.statusCode = statusCode || STATUS_CODES[type] || 500;
    this.details = details;
    this.context = context;
    this.timestamp = new Date().toISOString();
    this.errorId = uuidv4();
    this.isOperational = true; // Distinguish from programming errors
    
    Error.captureStackTrace(this, AppError);
  }
  
  toJSON() {
    return {
      errorId: this.errorId,
      type: this.type,
      message: this.message,
      statusCode: this.statusCode,
      details: this.details,
      context: this.context,
      timestamp: this.timestamp
    };
  }
}

/**
 * Error logging utility with context
 */
function logError(error, req = null, additionalContext = {}) {
  const isProduction = process.env.NODE_ENV === 'production';
  
  const logContext = {
    errorId: error.errorId || uuidv4(),
    timestamp: new Date().toISOString(),
    type: error.type || ERROR_TYPES.INTERNAL,
    message: error.message,
    statusCode: error.statusCode || 500,
    stack: !isProduction ? error.stack : undefined,
    ...additionalContext
  };
  
  // Add request context if available
  if (req) {
    logContext.request = {
      method: req.method,
      url: req.originalUrl || req.url,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection?.remoteAddress,
      userId: req.session?.userId,
      sessionId: req.sessionID
    };
  }
  
  // Add error details if available
  if (error.details) {
    logContext.details = error.details;
  }
  
  // Add error context if available
  if (error.context) {
    logContext.context = error.context;
  }
  
  // Log based on severity
  if (error.statusCode >= 500) {
    console.error('🚨 [ERROR]', JSON.stringify(logContext, null, 2));
  } else if (error.statusCode >= 400) {
    console.warn('⚠️ [WARNING]', JSON.stringify(logContext, null, 2));
  } else {
    console.info('ℹ️ [INFO]', JSON.stringify(logContext, null, 2));
  }
  
  return logContext.errorId;
}

/**
 * Standardized error response formatter
 */
function formatErrorResponse(error, req = null, includeStack = false) {
  const isProduction = process.env.NODE_ENV === 'production';
  const errorId = logError(error, req);
  
  const response = {
    success: false,
    error: {
      id: errorId,
      type: error.type || ERROR_TYPES.INTERNAL,
      message: error.message || USER_MESSAGES[error.type] || USER_MESSAGES[ERROR_TYPES.INTERNAL],
      timestamp: new Date().toISOString()
    }
  };
  
  // Add user-friendly message if different from technical message
  const userMessage = USER_MESSAGES[error.type];
  if (userMessage && userMessage !== error.message) {
    response.error.userMessage = userMessage;
  }
  
  // Add details if available and not in production
  if (error.details && (!isProduction || error.type === ERROR_TYPES.VALIDATION)) {
    response.error.details = error.details;
  }
  
  // Add stack trace in development
  if (includeStack && !isProduction && error.stack) {
    response.error.stack = error.stack;
  }
  
  return response;
}

/**
 * Validation error helper
 */
function createValidationError(message, details = null, field = null) {
  const context = field ? { field } : {};
  return new AppError(message, ERROR_TYPES.VALIDATION, 400, details, context);
}

/**
 * Database error helper
 */
function createDatabaseError(message, originalError = null, query = null) {
  const context = { originalError: originalError?.message, query };
  return new AppError(message, ERROR_TYPES.DATABASE, 500, null, context);
}

/**
 * Authentication error helper
 */
function createAuthError(message = 'Authentication required') {
  return new AppError(message, ERROR_TYPES.AUTHENTICATION, 401);
}

/**
 * Authorization error helper
 */
function createAuthzError(message = 'Insufficient permissions') {
  return new AppError(message, ERROR_TYPES.AUTHORIZATION, 403);
}

/**
 * Not found error helper
 */
function createNotFoundError(resource = 'Resource', id = null) {
  const message = id ? `${resource} with ID '${id}' not found` : `${resource} not found`;
  const context = { resource, id };
  return new AppError(message, ERROR_TYPES.NOT_FOUND, 404, null, context);
}

/**
 * Conflict error helper
 */
function createConflictError(message, conflictingField = null) {
  const context = conflictingField ? { conflictingField } : {};
  return new AppError(message, ERROR_TYPES.CONFLICT, 409, null, context);
}

/**
 * Storage error helper
 */
function createStorageError(message, currentUsage = null, limit = null) {
  const details = { currentUsage, limit };
  return new AppError(message, ERROR_TYPES.STORAGE, 413, details);
}

/**
 * Streaming error helper
 */
function createStreamingError(message, streamId = null, operation = null) {
  const context = { streamId, operation };
  return new AppError(message, ERROR_TYPES.STREAMING, 500, null, context);
}

/**
 * Express error handler middleware
 */
function errorHandlerMiddleware(error, req, res, next) {
  // Convert non-AppError to AppError
  if (!(error instanceof AppError)) {
    // Handle specific error types
    if (error.name === 'ValidationError') {
      error = createValidationError(error.message, error.details);
    } else if (error.code === 'SQLITE_ERROR' || error.code?.startsWith('SQLITE_')) {
      error = createDatabaseError('Database operation failed', error);
    } else if (error.name === 'MulterError') {
      if (error.code === 'LIMIT_FILE_SIZE') {
        error = createStorageError('File size exceeds limit');
      } else {
        error = createValidationError(`File upload error: ${error.message}`);
      }
    } else {
      // Generic internal error
      error = new AppError(
        error.message || 'An unexpected error occurred',
        ERROR_TYPES.INTERNAL,
        500,
        null,
        { originalError: error.name }
      );
    }
  }

  // Format and send response
  const response = formatErrorResponse(error, req);
  res.status(error.statusCode).json(response);
}

/**
 * Async error wrapper for route handlers
 */
function asyncHandler(fn) {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * Retry mechanism for transient failures
 */
async function retryOperation(operation, maxRetries = 3, delay = 1000, backoff = 2) {
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;

      // Don't retry for non-transient errors
      if (error instanceof AppError &&
          [ERROR_TYPES.VALIDATION, ERROR_TYPES.AUTHENTICATION, ERROR_TYPES.AUTHORIZATION, ERROR_TYPES.NOT_FOUND].includes(error.type)) {
        throw error;
      }

      if (attempt === maxRetries) {
        break;
      }

      // Wait before retry with exponential backoff
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(backoff, attempt - 1)));
    }
  }

  throw lastError;
}

module.exports = {
  ERROR_TYPES,
  STATUS_CODES,
  USER_MESSAGES,
  AppError,
  logError,
  formatErrorResponse,
  createValidationError,
  createDatabaseError,
  createAuthError,
  createAuthzError,
  createNotFoundError,
  createConflictError,
  createStorageError,
  createStreamingError,
  errorHandlerMiddleware,
  asyncHandler,
  retryOperation
};
