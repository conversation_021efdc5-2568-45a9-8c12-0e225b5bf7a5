const fs = require('fs');
const path = require('path');
const { google } = require('googleapis');
const { paths, getUniqueFilename } = require('./storage');

function createDriveService(apiKey) {
  return google.drive({
    version: 'v3',
    auth: apiKey
  });
}

function extractFileId(driveUrl) {

  let match = driveUrl.match(/\/file\/d\/([^\/]+)/);
  if (match) return match[1];

  match = driveUrl.match(/\?id=([^&]+)/);
  if (match) return match[1];

  match = driveUrl.match(/\/d\/([^\/]+)/);
  if (match) return match[1];

  if (/^[a-zA-Z0-9_-]{25,}$/.test(driveUrl.trim())) {
    return driveUrl.trim();
  }

  throw new Error('Invalid Google Drive URL format');
}

async function downloadFile(apiKey, fileId, progressCallback = null) {
  const drive = createDriveService(apiKey);

  try {
    console.log(`[Google Drive] Starting download for file ID: ${fileId}`);

    // First, get file metadata
    let fileMetadata;
    try {
      fileMetadata = await drive.files.get({
        fileId: fileId,
        fields: 'name,mimeType,size,permissions'
      });
    } catch (metadataError) {
      console.error(`[Google Drive] Failed to get file metadata:`, metadataError.message);

      // Provide specific error messages for metadata access
      if (metadataError.code === 403) {
        throw new Error('Access denied. The file may be private or your API key lacks permission to access it. Make sure the file is shared publicly or with your API key.');
      } else if (metadataError.code === 404) {
        throw new Error('File not found. Please check if the Google Drive URL is correct and the file still exists.');
      } else if (metadataError.code === 401) {
        throw new Error('Authentication failed. Please check your Google Drive API key configuration.');
      } else {
        throw new Error(`Failed to access file metadata: ${metadataError.message}`);
      }
    }

    console.log(`[Google Drive] File metadata:`, {
      name: fileMetadata.data.name,
      mimeType: fileMetadata.data.mimeType,
      size: fileMetadata.data.size
    });

    if (!fileMetadata.data.mimeType.includes('video')) {
      throw new Error(`The selected file is not a video. File type: ${fileMetadata.data.mimeType}`);
    }

    // Check if file size is available (some files might not have size metadata)
    if (!fileMetadata.data.size) {
      console.warn(`[Google Drive] File size not available in metadata for ${fileMetadata.data.name}`);
    }

    const originalFilename = fileMetadata.data.name;
    const ext = path.extname(originalFilename) || '.mp4';
    const uniqueFilename = getUniqueFilename(originalFilename);
    const localFilePath = path.join(paths.videos, uniqueFilename);

    console.log(`[Google Drive] Download paths:`, {
      originalFilename,
      uniqueFilename,
      localFilePath
    });

    // Ensure the directory exists
    const dir = path.dirname(localFilePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`[Google Drive] Created directory: ${dir}`);
    }

    const dest = fs.createWriteStream(localFilePath);

    console.log(`[Google Drive] Starting file download stream...`);

    // Try to download the file
    let response;
    try {
      response = await drive.files.get(
        {
          fileId: fileId,
          alt: 'media'
        },
        { responseType: 'stream' }
      );
    } catch (downloadError) {
      console.error(`[Google Drive] Failed to start download:`, downloadError.message);

      // Provide more specific error messages
      if (downloadError.code === 403) {
        throw new Error('Access denied. The file may be private or your API key lacks permission to access it.');
      } else if (downloadError.code === 404) {
        throw new Error('File not found. The file may have been deleted or the link is incorrect.');
      } else if (downloadError.code === 401) {
        throw new Error('Authentication failed. Please check your Google Drive API key.');
      } else {
        throw new Error(`Download failed: ${downloadError.message}`);
      }
    }

    const fileSize = parseInt(fileMetadata.data.size, 10) || 0;
    let downloaded = 0;

    console.log(`[Google Drive] Expected file size: ${fileSize} bytes`);
    console.log(`[Google Drive] Stream response received, starting download...`);

    // Handle case where file size is not available
    if (fileSize === 0) {
      console.warn(`[Google Drive] File size is 0 or not available - progress tracking will be limited`);
    }

    return new Promise((resolve, reject) => {
      let streamClosed = false;

      // Cleanup function to properly close streams and remove files
      const cleanup = () => {
        if (!streamClosed) {
          streamClosed = true;
          try {
            // Properly close the write stream
            if (dest && !dest.destroyed) {
              dest.destroy();
            }
          } catch (e) {
            // Ignore cleanup errors
            console.warn('Warning during stream cleanup:', e.message);
          }
        }
      };

      response.data
        .on('data', chunk => {
          downloaded += chunk.length;
          if (progressCallback) {
            // Calculate progress, handle case where fileSize is 0 or unknown
            let progress = 0;
            if (fileSize > 0) {
              progress = Math.round((downloaded / fileSize) * 100);
              progress = Math.min(progress, 100); // Cap at 100%
            } else {
              // If file size is unknown, show progress based on downloaded bytes
              progress = Math.min(Math.round(downloaded / (1024 * 1024)), 99); // Show MB downloaded, cap at 99%
            }

            progressCallback({
              id: fileId,
              filename: originalFilename,
              progress: progress
            });
          }
        })
        .on('end', () => {
          // Don't cleanup immediately - wait for file verification
          console.log(`[Google Drive] Stream ended for ${originalFilename}`);

          // Add a small delay to ensure file is fully written to disk
          setTimeout(() => {
            try {
              // Verify file was actually written and has correct size
              if (!fs.existsSync(localFilePath)) {
                cleanup();
                reject(new Error(`File was not created: ${localFilePath}`));
                return;
              }

              const actualFileSize = fs.statSync(localFilePath).size;
              console.log(`Downloaded file ${originalFilename} from Google Drive`);
              console.log(`Expected size: ${fileSize} bytes, Actual size: ${actualFileSize} bytes`);

              // Allow some tolerance for size differences (metadata might not be exact)
              if (actualFileSize === 0) {
                cleanup();
                // Clean up the empty file
                try {
                  fs.unlinkSync(localFilePath);
                } catch (cleanupError) {
                  console.error('Error cleaning up empty file:', cleanupError.message);
                }
                reject(new Error(`Downloaded file is empty: ${localFilePath}`));
                return;
              }

              if (fileSize > 0 && Math.abs(actualFileSize - fileSize) > fileSize * 0.1) {
                console.warn(`File size mismatch - Expected: ${fileSize}, Got: ${actualFileSize}`);
              }

              // File verification successful - now cleanup streams
              cleanup();

              resolve({
                filename: uniqueFilename,
                originalFilename: originalFilename,
                localFilePath: localFilePath,
                mimeType: fileMetadata.data.mimeType,
                fileSize: actualFileSize // Use actual file size
              });
            } catch (verificationError) {
              cleanup();
              // Clean up the file if verification fails
              try {
                if (fs.existsSync(localFilePath)) {
                  fs.unlinkSync(localFilePath);
                }
              } catch (cleanupError) {
                console.error('Error cleaning up file after verification failure:', cleanupError.message);
              }
              reject(new Error(`File verification failed: ${verificationError.message}`));
            }
          }, 100); // 100ms delay to ensure file is fully written
        })
        .on('error', err => {
          cleanup();
          // Safe file cleanup with error handling
          try {
            if (fs.existsSync(localFilePath)) {
              fs.unlinkSync(localFilePath);
            }
          } catch (unlinkError) {
            console.error('Error cleaning up file after download error:', unlinkError.message);
          }
          reject(err);
        })
        .pipe(dest);

      // Handle write stream errors separately
      dest.on('error', (err) => {
        cleanup();
        try {
          if (fs.existsSync(localFilePath)) {
            fs.unlinkSync(localFilePath);
          }
        } catch (unlinkError) {
          console.error('Error cleaning up file after write error:', unlinkError.message);
        }
        reject(err);
      });
    });
  } catch (error) {
    console.error('Error downloading file from Google Drive:', error);
    throw error;
  }
}

module.exports = {
  createDriveService,
  extractFileId,
  downloadFile
};