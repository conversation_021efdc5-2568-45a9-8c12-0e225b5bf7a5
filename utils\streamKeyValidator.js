/**
 * Stream Key Validator
 * Validates RTMP URLs and Stream Keys for various platforms
 */

class StreamKeyValidator {
  
  // Platform-specific RTMP URL patterns
  static PLATFORM_PATTERNS = {
    youtube: {
      rtmp: /^rtmp:\/\/a\.rtmp\.youtube\.com\/live2\/?$/,
      streamKeyLength: { min: 20, max: 50 },
      streamKeyPattern: /^[a-zA-Z0-9\-_]{20,50}$/,
      name: 'YouTube Live'
    },
    twitch: {
      rtmp: /^rtmp:\/\/[a-z0-9\-]+\.contribute\.live-video\.net\/live\/?$/,
      streamKeyLength: { min: 20, max: 40 },
      streamKeyPattern: /^live_[0-9]+_[a-zA-Z0-9]{20,}$/,
      name: 'Twitch'
    },
    facebook: {
      rtmp: /^rtmps?:\/\/live-api-s\.facebook\.com\/rtmp\/[a-zA-Z0-9]+\/?$/,
      streamKeyLength: { min: 15, max: 50 },
      streamKeyPattern: /^[a-zA-Z0-9\-_]{15,50}$/,
      name: 'Facebook Live'
    },
    instagram: {
      rtmp: /^rtmps?:\/\/live-upload\.instagram\.com\/rtmp\/[a-zA-Z0-9]+\/?$/,
      streamKeyLength: { min: 15, max: 50 },
      streamKeyPattern: /^[a-zA-Z0-9\-_]{15,50}$/,
      name: 'Instagram Live'
    },
    tiktok: {
      rtmp: /^rtmp:\/\/push-[a-z0-9\-]+\.tiktokcdn\.com\/live\/?$/,
      streamKeyLength: { min: 20, max: 60 },
      streamKeyPattern: /^[a-zA-Z0-9\-_]{20,60}$/,
      name: 'TikTok Live'
    },
    custom: {
      rtmp: /^rtmps?:\/\/.+/,
      streamKeyLength: { min: 4, max: 100 },
      streamKeyPattern: /^[a-zA-Z0-9\-_\.\/\=\+]{4,100}$/,
      name: 'Custom RTMP'
    }
  };

  // Common invalid/placeholder stream keys
  static INVALID_KEYS = [
    '123', 'test', 'your_stream_key', 'stream_key', 'key', 'rtmp_key',
    'your-stream-key', 'example', 'demo', 'placeholder', 'sample',
    'abc123', '123abc', 'testkey', 'demokey', 'samplekey'
  ];

  /**
   * Detect platform from RTMP URL
   */
  static detectPlatform(rtmpUrl) {
    if (!rtmpUrl) return null;
    
    const url = rtmpUrl.toLowerCase();
    
    if (url.includes('youtube.com')) return 'youtube';
    if (url.includes('twitch.tv') || url.includes('contribute.live-video.net')) return 'twitch';
    if (url.includes('facebook.com')) return 'facebook';
    if (url.includes('instagram.com')) return 'instagram';
    if (url.includes('tiktok')) return 'tiktok';
    
    return 'custom';
  }

  /**
   * Validate RTMP URL format
   */
  static validateRtmpUrl(rtmpUrl) {
    const errors = [];
    
    if (!rtmpUrl || rtmpUrl.trim() === '') {
      errors.push('RTMP URL is required');
      return { isValid: false, errors };
    }

    rtmpUrl = rtmpUrl.trim();

    // Basic RTMP protocol check
    if (!rtmpUrl.startsWith('rtmp://') && !rtmpUrl.startsWith('rtmps://')) {
      errors.push('RTMP URL must start with rtmp:// or rtmps://');
    }

    // Check for localhost/local IPs (usually testing)
    if (rtmpUrl.includes('localhost') || rtmpUrl.includes('127.0.0.1') || rtmpUrl.includes('192.168.')) {
      errors.push('Local RTMP URLs are not recommended for production streaming');
    }

    // Check for common placeholder URLs
    const placeholderUrls = [
      'rtmp://your-server.com',
      'rtmp://example.com',
      'rtmp://test.com',
      'rtmp://server.com'
    ];
    
    if (placeholderUrls.some(placeholder => rtmpUrl.toLowerCase().includes(placeholder))) {
      errors.push('Please replace placeholder RTMP URL with actual streaming server');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate stream key
   */
  static validateStreamKey(streamKey, rtmpUrl = null) {
    const errors = [];
    const warnings = [];
    
    if (!streamKey || streamKey.trim() === '') {
      errors.push('Stream key is required');
      return { isValid: false, errors, warnings };
    }

    streamKey = streamKey.trim();

    // Check for common invalid keys
    if (this.INVALID_KEYS.includes(streamKey.toLowerCase())) {
      errors.push(`"${streamKey}" is a placeholder. Please use your actual stream key from your streaming platform.`);
    }

    // Check minimum length
    if (streamKey.length < 4) {
      errors.push('Stream key is too short (minimum 4 characters)');
    }

    // Check maximum length
    if (streamKey.length > 100) {
      errors.push('Stream key is too long (maximum 100 characters)');
    }

    // Platform-specific validation
    if (rtmpUrl) {
      const platform = this.detectPlatform(rtmpUrl);
      const platformConfig = this.PLATFORM_PATTERNS[platform];
      
      if (platformConfig) {
        // Length validation
        const { min, max } = platformConfig.streamKeyLength;
        if (streamKey.length < min || streamKey.length > max) {
          errors.push(`${platformConfig.name} stream keys should be ${min}-${max} characters long`);
        }

        // Pattern validation
        if (!platformConfig.streamKeyPattern.test(streamKey)) {
          if (platform === 'youtube') {
            errors.push('YouTube stream keys should contain only letters, numbers, hyphens, and underscores');
          } else if (platform === 'twitch') {
            errors.push('Twitch stream keys should start with "live_" followed by numbers and letters');
          } else {
            errors.push(`Invalid ${platformConfig.name} stream key format`);
          }
        }
      }
    }

    // Security checks
    if (streamKey.includes(' ')) {
      errors.push('Stream key should not contain spaces');
    }

    // Check for suspicious patterns
    if (/^[0-9]{1,5}$/.test(streamKey)) {
      warnings.push('Stream key appears to be just numbers - make sure this is correct');
    }

    if (streamKey.length < 10 && !streamKey.includes('_') && !streamKey.includes('-')) {
      warnings.push('Stream key seems unusually short - please verify it\'s correct');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate complete RTMP configuration
   */
  static validateRtmpConfig(rtmpUrl, streamKey) {
    const rtmpValidation = this.validateRtmpUrl(rtmpUrl);
    const keyValidation = this.validateStreamKey(streamKey, rtmpUrl);
    
    const errors = [...rtmpValidation.errors, ...keyValidation.errors];
    const warnings = keyValidation.warnings || [];

    // Cross-validation
    if (rtmpValidation.isValid && keyValidation.isValid) {
      const platform = this.detectPlatform(rtmpUrl);
      const platformConfig = this.PLATFORM_PATTERNS[platform];
      
      if (platformConfig && platform !== 'custom') {
        // Check if RTMP URL matches platform pattern
        if (!platformConfig.rtmp.test(rtmpUrl)) {
          errors.push(`RTMP URL format doesn't match ${platformConfig.name} requirements`);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      platform: this.detectPlatform(rtmpUrl),
      suggestions: this.getSuggestions(rtmpUrl, streamKey)
    };
  }

  /**
   * Get helpful suggestions based on detected issues
   */
  static getSuggestions(rtmpUrl, streamKey) {
    const suggestions = [];
    const platform = this.detectPlatform(rtmpUrl);

    if (platform === 'youtube') {
      suggestions.push('YouTube: Go to YouTube Studio → Go Live → Stream → Copy stream URL and key');
    } else if (platform === 'twitch') {
      suggestions.push('Twitch: Go to Creator Dashboard → Settings → Stream → Primary Stream key');
    } else if (platform === 'facebook') {
      suggestions.push('Facebook: Go to Creator Studio → Go Live → Use streaming software');
    }

    if (streamKey && this.INVALID_KEYS.includes(streamKey.toLowerCase())) {
      suggestions.push('Replace placeholder stream key with the actual key from your streaming platform');
    }

    if (streamKey && streamKey.length < 10) {
      suggestions.push('Stream keys are usually longer - double-check you copied the complete key');
    }

    return suggestions;
  }

  /**
   * Get platform-specific help
   */
  static getPlatformHelp(platform) {
    const help = {
      youtube: {
        rtmpUrl: 'rtmp://a.rtmp.youtube.com/live2',
        instructions: 'Go to YouTube Studio → Go Live → Stream tab → Copy Server URL and Stream key',
        keyFormat: 'Usually 20-50 characters with letters, numbers, hyphens, and underscores'
      },
      twitch: {
        rtmpUrl: 'rtmp://live.twitch.tv/live',
        instructions: 'Go to Twitch Creator Dashboard → Settings → Stream → Copy Primary Stream key',
        keyFormat: 'Starts with "live_" followed by numbers and letters'
      },
      facebook: {
        rtmpUrl: 'Varies by region',
        instructions: 'Go to Facebook Creator Studio → Go Live → Use streaming software',
        keyFormat: '15-50 characters with letters, numbers, hyphens, and underscores'
      },
      custom: {
        rtmpUrl: 'Provided by your streaming service',
        instructions: 'Contact your streaming service provider for RTMP details',
        keyFormat: 'Varies by service provider'
      }
    };

    return help[platform] || help.custom;
  }
}

module.exports = StreamKeyValidator;
