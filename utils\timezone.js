/**
 * Timezone Utilities for StreamOnPod
 * 
 * Provides timezone conversion and management functions for scheduled streams
 */

// Common timezone list for Indonesia and international users
const TIMEZONE_LIST = [
  // Indonesia Timezones
  { value: 'Asia/Jakarta', label: 'WIB - Jakarta, Bandung, Surabaya', offset: '+07:00' },
  { value: 'Asia/Makassar', label: 'WITA - Makassar, Denpasar, Balikpapan', offset: '+08:00' },
  { value: 'Asia/Jayapura', label: 'WIT - Jayapura, Manokwari', offset: '+09:00' },
  
  // International Common Timezones
  { value: 'UTC', label: 'UTC - Coordinated Universal Time', offset: '+00:00' },
  { value: 'Asia/Singapore', label: 'SGT - Singapore', offset: '+08:00' },
  { value: 'Asia/Kuala_Lumpur', label: 'MYT - Kuala Lumpur', offset: '+08:00' },
  { value: 'Asia/Bangkok', label: 'ICT - Bangkok', offset: '+07:00' },
  { value: 'Asia/Manila', label: 'PHT - Manila', offset: '+08:00' },
  { value: 'Asia/Tokyo', label: 'JST - Tokyo', offset: '+09:00' },
  { value: 'Asia/Seoul', label: 'KST - Seoul', offset: '+09:00' },
  { value: 'Asia/Shanghai', label: 'CST - Shanghai', offset: '+08:00' },
  { value: 'Asia/Hong_Kong', label: 'HKT - Hong Kong', offset: '+08:00' },
  { value: 'Asia/Kolkata', label: 'IST - Mumbai, Delhi', offset: '+05:30' },
  { value: 'Asia/Dubai', label: 'GST - Dubai', offset: '+04:00' },
  { value: 'Europe/London', label: 'GMT - London', offset: '+00:00' },
  { value: 'Europe/Paris', label: 'CET - Paris, Berlin', offset: '+01:00' },
  { value: 'America/New_York', label: 'EST - New York', offset: '-05:00' },
  { value: 'America/Los_Angeles', label: 'PST - Los Angeles', offset: '-08:00' },
  { value: 'Australia/Sydney', label: 'AEDT - Sydney', offset: '+11:00' }
];

/**
 * Get list of available timezones
 * @returns {Array} Array of timezone objects
 */
function getTimezoneList() {
  return TIMEZONE_LIST;
}

/**
 * Get default timezone (Indonesia WIB)
 * @returns {string} Default timezone identifier
 */
function getDefaultTimezone() {
  return 'Asia/Jakarta';
}

/**
 * Convert local datetime to UTC with timezone
 * @param {string} localDateTime - Local datetime string (YYYY-MM-DDTHH:mm)
 * @param {string} timezone - Timezone identifier (e.g., 'Asia/Jakarta')
 * @returns {Date} UTC Date object
 */
function convertToUTC(localDateTime, timezone = 'UTC') {
  try {
    // IMPORTANT: The server is running in WIB timezone (Asia/Jakarta)
    // When user inputs datetime with Asia/Jakarta timezone, JavaScript already converts correctly!

    const systemTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    if (timezone === 'UTC') {
      return new Date(localDateTime);
    }

    // If the target timezone is the same as system timezone,
    // JavaScript's new Date() already does the correct conversion
    if (timezone === systemTimezone || timezone === 'Asia/Jakarta') {
      return new Date(localDateTime);
    }

    // For other Indonesian timezones, calculate offset difference
    const timezoneOffsets = {
      'Asia/Jakarta': 7,    // UTC+7 (WIB)
      'Asia/Makassar': 8,   // UTC+8 (WITA)
      'Asia/Jayapura': 9    // UTC+9 (WIT)
    };

    const systemOffset = timezoneOffsets['Asia/Jakarta'] || 7; // Server is in WIB
    const targetOffset = timezoneOffsets[timezone] || 7;
    const offsetDiff = targetOffset - systemOffset; // Difference in hours

    // Create date (JavaScript interprets as system timezone)
    const date = new Date(localDateTime);

    // Adjust for timezone difference
    const utcDate = new Date(date.getTime() - (offsetDiff * 60 * 60 * 1000));

    return utcDate;
  } catch (error) {
    console.error('Error converting to UTC:', error);
    return new Date(localDateTime); // Fallback to original date
  }
}

/**
 * Convert UTC datetime to local timezone
 * @param {Date|string} utcDateTime - UTC datetime
 * @param {string} timezone - Target timezone identifier
 * @returns {Date} Local Date object
 */
function convertFromUTC(utcDateTime, timezone = 'UTC') {
  try {
    const utcDate = new Date(utcDateTime);
    
    // Get timezone offset in minutes
    const offsetMinutes = getTimezoneOffset(timezone, utcDate);
    
    // Convert from UTC by adding the offset
    const localDate = new Date(utcDate.getTime() + (offsetMinutes * 60 * 1000));
    
    return localDate;
  } catch (error) {
    console.error('Error converting from UTC:', error);
    return new Date(utcDateTime); // Fallback to original date
  }
}

/**
 * Get timezone offset in minutes for a specific timezone and date
 * @param {string} timezone - Timezone identifier
 * @param {Date} date - Date to get offset for
 * @returns {number} Offset in minutes
 */
function getTimezoneOffset(timezone, date = new Date()) {
  try {
    // Use Intl.DateTimeFormat to get timezone offset
    const utcDate = new Date(date.toLocaleString('en-US', { timeZone: 'UTC' }));
    const localDate = new Date(date.toLocaleString('en-US', { timeZone: timezone }));
    
    return (localDate.getTime() - utcDate.getTime()) / (1000 * 60);
  } catch (error) {
    console.error('Error getting timezone offset:', error);
    return 0; // Fallback to UTC
  }
}

/**
 * Format datetime for display with timezone
 * @param {Date|string} datetime - Datetime to format
 * @param {string} timezone - Timezone for display
 * @param {Object} options - Formatting options
 * @returns {string} Formatted datetime string
 */
function formatDateTimeWithTimezone(datetime, timezone = 'UTC', options = {}) {
  try {
    const date = new Date(datetime);
    
    const defaultOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: timezone,
      timeZoneName: 'short'
    };
    
    const formatOptions = { ...defaultOptions, ...options };
    
    return date.toLocaleString('en-US', formatOptions);
  } catch (error) {
    console.error('Error formatting datetime:', error);
    return datetime.toString();
  }
}

/**
 * Get current time in specific timezone
 * @param {string} timezone - Timezone identifier
 * @returns {Date} Current time in specified timezone
 */
function getCurrentTimeInTimezone(timezone = 'UTC') {
  try {
    const now = new Date();
    return convertFromUTC(now, timezone);
  } catch (error) {
    console.error('Error getting current time in timezone:', error);
    return new Date();
  }
}

/**
 * Validate timezone identifier
 * @param {string} timezone - Timezone to validate
 * @returns {boolean} True if valid timezone
 */
function isValidTimezone(timezone) {
  try {
    Intl.DateTimeFormat(undefined, { timeZone: timezone });
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Get timezone info by identifier
 * @param {string} timezone - Timezone identifier
 * @returns {Object|null} Timezone info object or null if not found
 */
function getTimezoneInfo(timezone) {
  return TIMEZONE_LIST.find(tz => tz.value === timezone) || null;
}

/**
 * Convert datetime-local input to ISO string with timezone
 * @param {string} datetimeLocal - Datetime from HTML datetime-local input
 * @param {string} timezone - Source timezone
 * @returns {string} ISO string in UTC
 */
function datetimeLocalToUTC(datetimeLocal, timezone = 'UTC') {
  try {
    if (!datetimeLocal) return null;
    
    // datetime-local format: YYYY-MM-DDTHH:mm
    const utcDate = convertToUTC(datetimeLocal, timezone);
    return utcDate.toISOString();
  } catch (error) {
    console.error('Error converting datetime-local to UTC:', error);
    return null;
  }
}

/**
 * Convert UTC ISO string to datetime-local format with timezone
 * @param {string} utcISOString - UTC ISO string
 * @param {string} timezone - Target timezone
 * @returns {string} Datetime-local format string
 */
function utcToDatetimeLocal(utcISOString, timezone = 'UTC') {
  try {
    if (!utcISOString) return '';
    
    const localDate = convertFromUTC(utcISOString, timezone);
    
    // Format to YYYY-MM-DDTHH:mm for datetime-local input
    const year = localDate.getFullYear();
    const month = String(localDate.getMonth() + 1).padStart(2, '0');
    const day = String(localDate.getDate()).padStart(2, '0');
    const hours = String(localDate.getHours()).padStart(2, '0');
    const minutes = String(localDate.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  } catch (error) {
    console.error('Error converting UTC to datetime-local:', error);
    return '';
  }
}

module.exports = {
  getTimezoneList,
  getDefaultTimezone,
  convertToUTC,
  convertFromUTC,
  getTimezoneOffset,
  formatDateTimeWithTimezone,
  getCurrentTimeInTimezone,
  isValidTimezone,
  getTimezoneInfo,
  datetimeLocalToUTC,
  utcToDatetimeLocal
};
