const { validationResult } = require('express-validator');
const { createValidationError } = require('./errorHandler');

/**
 * Enhanced Validation Helper
 * Provides better validation error handling and formatting
 */

/**
 * Express-validator error handler middleware
 */
function handleValidationErrors(req, res, next) {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorDetails = errors.array().map(error => ({
      field: error.path || error.param,
      message: error.msg,
      value: error.value,
      location: error.location
    }));
    
    const firstError = errorDetails[0];
    const message = `Validation failed: ${firstError.message}`;
    
    throw createValidationError(message, errorDetails, firstError.field);
  }
  
  next();
}

/**
 * Custom validation functions
 */
const validators = {
  /**
   * Validate stream key format
   */
  isValidStreamKey: (value) => {
    if (!value || typeof value !== 'string') {
      return false;
    }
    
    // Stream key should be alphanumeric with some special characters
    const streamKeyRegex = /^[a-zA-Z0-9_\-\.]{8,64}$/;
    return streamKeyRegex.test(value);
  },
  
  /**
   * Validate RTMP URL format
   */
  isValidRtmpUrl: (value) => {
    if (!value || typeof value !== 'string') {
      return false;
    }
    
    // RTMP URL should start with rtmp:// or rtmps://
    const rtmpRegex = /^rtmps?:\/\/[^\s\/$.?#].[^\s]*$/i;
    return rtmpRegex.test(value);
  },
  
  /**
   * Validate video file format (only MP4 and MOV for optimal performance)
   */
  isValidVideoFormat: (filename) => {
    if (!filename || typeof filename !== 'string') {
      return false;
    }

    const allowedFormats = ['.mp4', '.mov'];
    const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    return allowedFormats.includes(extension);
  },
  
  /**
   * Validate image file format
   */
  isValidImageFormat: (filename) => {
    if (!filename || typeof filename !== 'string') {
      return false;
    }
    
    const allowedFormats = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    return allowedFormats.includes(extension);
  },
  
  /**
   * Validate file size
   */
  isValidFileSize: (size, maxSizeBytes) => {
    if (typeof size !== 'number' || size < 0) {
      return false;
    }
    
    return size <= maxSizeBytes;
  },
  
  /**
   * Validate storage amount
   */
  isValidStorageAmount: (value, unit = 'GB') => {
    if (typeof value !== 'number' || value < 0) {
      return false;
    }
    
    // Maximum reasonable storage limits
    const maxLimits = {
      'MB': 1024 * 1024, // 1TB in MB
      'GB': 1024,        // 1TB in GB
      'TB': 1             // 1TB in TB
    };
    
    return value <= (maxLimits[unit.toUpperCase()] || maxLimits.GB);
  },
  
  /**
   * Validate streaming slots
   */
  isValidStreamingSlots: (value) => {
    if (typeof value !== 'number') {
      return false;
    }
    
    // -1 for unlimited, 0-100 for limited
    return value === -1 || (value >= 0 && value <= 100);
  },
  
  /**
   * Validate plan price
   */
  isValidPlanPrice: (value) => {
    if (typeof value !== 'number' || value < 0) {
      return false;
    }
    
    // Maximum reasonable price (in cents/smallest currency unit)
    const maxPrice = 10000000; // $100,000 or equivalent
    return value <= maxPrice;
  },
  
  /**
   * Validate username format
   */
  isValidUsername: (value) => {
    if (!value || typeof value !== 'string') {
      return false;
    }
    
    // Username: 3-30 characters, alphanumeric and underscore
    const usernameRegex = /^[a-zA-Z0-9_]{3,30}$/;
    return usernameRegex.test(value);
  },
  
  /**
   * Validate password strength
   */
  isValidPassword: (value) => {
    if (!value || typeof value !== 'string') {
      return false;
    }
    
    // Password: at least 8 characters, contains letter and number
    const hasMinLength = value.length >= 8;
    const hasLetter = /[a-zA-Z]/.test(value);
    const hasNumber = /[0-9]/.test(value);
    
    return hasMinLength && hasLetter && hasNumber;
  }
};

/**
 * Sanitization functions
 */
const sanitizers = {
  /**
   * Sanitize string input
   */
  sanitizeString: (value, maxLength = 255) => {
    if (!value || typeof value !== 'string') {
      return '';
    }
    
    return value.trim().substring(0, maxLength);
  },
  
  /**
   * Sanitize HTML content
   */
  sanitizeHtml: (value) => {
    if (!value || typeof value !== 'string') {
      return '';
    }
    
    // Basic HTML sanitization - remove script tags and dangerous attributes
    return value
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/on\w+="[^"]*"/gi, '')
      .replace(/javascript:/gi, '')
      .trim();
  },
  
  /**
   * Sanitize filename
   */
  sanitizeFilename: (value) => {
    if (!value || typeof value !== 'string') {
      return '';
    }
    
    // Remove dangerous characters from filename
    return value
      .replace(/[<>:"/\\|?*]/g, '')
      .replace(/\.\./g, '')
      .trim();
  },
  
  /**
   * Sanitize array of strings
   */
  sanitizeStringArray: (array, maxItems = 50, maxLength = 100) => {
    if (!Array.isArray(array)) {
      return [];
    }
    
    return array
      .filter(item => typeof item === 'string' && item.trim().length > 0)
      .map(item => sanitizers.sanitizeString(item, maxLength))
      .slice(0, maxItems);
  }
};

/**
 * Validation error messages
 */
const ERROR_MESSAGES = {
  REQUIRED: 'This field is required',
  INVALID_FORMAT: 'Invalid format',
  TOO_SHORT: 'Value is too short',
  TOO_LONG: 'Value is too long',
  INVALID_EMAIL: 'Invalid email address',
  INVALID_URL: 'Invalid URL format',
  INVALID_NUMBER: 'Must be a valid number',
  INVALID_INTEGER: 'Must be a valid integer',
  OUT_OF_RANGE: 'Value is out of allowed range',
  INVALID_FILE_TYPE: 'Invalid file type',
  FILE_TOO_LARGE: 'File size exceeds limit',
  INVALID_USERNAME: 'Username must be 3-30 characters, alphanumeric and underscore only',
  WEAK_PASSWORD: 'Password must be at least 8 characters with letters and numbers',
  INVALID_STREAM_KEY: 'Stream key must be 8-64 characters, alphanumeric with dashes, dots, and underscores',
  INVALID_RTMP_URL: 'Invalid RTMP URL format'
};

module.exports = {
  handleValidationErrors,
  validators,
  sanitizers,
  ERROR_MESSAGES
};
