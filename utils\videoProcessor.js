const ffmpeg = require('fluent-ffmpeg');
const ffmpegPath = require('@ffmpeg-installer/ffmpeg').path;
const { getVideoDurationInSeconds } = require('get-video-duration');
const fs = require('fs');
const path = require('path');
const { getUniqueFilename, paths } = require('./storage');
ffmpeg.setFfmpegPath(ffmpegPath);

// Safe frame rate parser to replace dangerous code execution
function parseFrameRate(frameRate) {
  if (!frameRate || typeof frameRate !== 'string') {
    return 30; // Default fallback
  }

  try {
    // Handle fraction format (e.g., "30/1", "25/1", "24000/1001")
    if (frameRate.includes('/')) {
      const parts = frameRate.split('/');
      if (parts.length === 2) {
        const numerator = parseFloat(parts[0]);
        const denominator = parseFloat(parts[1]);

        if (!isNaN(numerator) && !isNaN(denominator) && denominator !== 0) {
          const result = numerator / denominator;
          // Handle zero or negative frame rates
          if (result <= 0) {
            return 30; // Default fallback for invalid rates
          }
          // Clamp to reasonable values (1-120 fps)
          return Math.max(1, Math.min(120, Math.round(result)));
        }
      }
    }

    // Handle decimal format (e.g., "29.97", "30.0")
    const parsed = parseFloat(frameRate);
    if (!isNaN(parsed) && parsed > 0) {
      // Clamp to reasonable values (1-120 fps)
      return Math.max(1, Math.min(120, Math.round(parsed)));
    }
  } catch (error) {
    console.error('Error parsing frame rate:', frameRate, error);
  }

  return 30; // Default fallback
}
const getVideoInfo = async (filepath) => {
  try {
    // Verify file exists before processing
    if (!fs.existsSync(filepath)) {
      throw new Error(`Video file not found: ${filepath}`);
    }

    const stats = fs.statSync(filepath);
    const fileSizeInBytes = stats.size;

    if (fileSizeInBytes === 0) {
      throw new Error(`Video file is empty: ${filepath}`);
    }

    console.log(`[VideoProcessor] Processing video: ${path.basename(filepath)} (${fileSizeInBytes} bytes)`);

    const duration = await getVideoDurationInSeconds(filepath);

    // Get detailed video information including codec
    const videoDetails = await getDetailedVideoInfo(filepath);

    return {
      duration,
      fileSize: fileSizeInBytes,
      ...videoDetails
    };
  } catch (error) {
    console.error('Error getting video info:', error);
    // Add file path to error for better debugging
    if (error.localFilePath === undefined) {
      error.localFilePath = filepath;
    }
    throw error;
  }
};

const getDetailedVideoInfo = (filepath) => {
  return new Promise((resolve, reject) => {
    // Verify file exists before calling ffprobe
    if (!fs.existsSync(filepath)) {
      console.error(`[VideoProcessor] File not found for ffprobe: ${filepath}`);
      resolve({}); // Return empty object instead of rejecting
      return;
    }

    // console.log(`[VideoProcessor] Running ffprobe on: ${filepath}`); // Removed for production
    ffmpeg.ffprobe(filepath, (err, metadata) => {
      if (err) {
        console.error('Error getting video metadata:', err);
        console.error(`[VideoProcessor] ffprobe failed for file: ${filepath}`);

        // Check if it's a "No such file or directory" error
        if (err.message && err.message.includes('No such file or directory')) {
          console.error(`[VideoProcessor] File disappeared during processing: ${filepath}`);
        }

        resolve({}); // Return empty object instead of rejecting
        return;
      }

      try {
        if (!metadata || !metadata.streams) {
          console.warn(`[VideoProcessor] No streams found in metadata for: ${filepath}`);
          resolve({});
          return;
        }

        const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
        const audioStream = metadata.streams.find(stream => stream.codec_type === 'audio');

        const result = {};

        if (videoStream) {
          result.codec = videoStream.codec_name;
          result.resolution = `${videoStream.width}x${videoStream.height}`;
          result.fps = parseFrameRate(videoStream.r_frame_rate) || 30; // Safe frame rate parsing
          result.bitrate = parseInt(videoStream.bit_rate) || null;
        }

        if (audioStream) {
          result.audioCodec = audioStream.codec_name;
          result.audioBitrate = parseInt(audioStream.bit_rate) || null;
        }

        console.log(`[VideoProcessor] Video info for ${path.basename(filepath)}:`, {
          codec: result.codec,
          resolution: result.resolution,
          fps: result.fps,
          bitrate: result.bitrate
        });

        resolve(result);
      } catch (parseError) {
        console.error('Error parsing video metadata:', parseError);
        resolve({});
      }
    });
  });
};
const generateThumbnail = (videoPath, thumbnailName) => {
  return new Promise((resolve, reject) => {
    const thumbnailPath = path.join(paths.thumbnails, thumbnailName);
    const webpThumbnailPath = thumbnailPath.replace(/\.(jpg|jpeg)$/i, '.webp');

    // Generate both JPEG and WebP thumbnails for better performance
    const generateJPEG = () => {
      return new Promise((resolveJPEG, rejectJPEG) => {
        ffmpeg(videoPath)
          .inputOptions([
            '-hwaccel', 'auto', // Try hardware acceleration first
            '-ss', '00:00:05'   // Seek to 5 seconds (faster than 10s)
          ])
          .outputOptions([
            '-vframes', '1',    // Extract only 1 frame
            '-q:v', '3',        // Slightly lower quality for faster processing
            '-vf', 'scale=640:360:force_original_aspect_ratio=decrease,pad=640:360:(ow-iw)/2:(oh-ih)/2' // Smaller size for faster processing
          ])
          .output(thumbnailPath)
          .on('end', () => resolveJPEG(thumbnailPath))
          .on('error', rejectJPEG)
          .run();
      });
    };

    // Generate WebP version for modern browsers
    const generateWebP = () => {
      return new Promise((resolveWebP, rejectWebP) => {
        ffmpeg(videoPath)
          .inputOptions([
            '-hwaccel', 'auto',
            '-ss', '00:00:05'
          ])
          .outputOptions([
            '-vframes', '1',
            '-c:v', 'libwebp',
            '-quality', '80',
            '-vf', 'scale=640:360:force_original_aspect_ratio=decrease,pad=640:360:(ow-iw)/2:(oh-ih)/2'
          ])
          .output(webpThumbnailPath)
          .on('end', () => resolveWebP(webpThumbnailPath))
          .on('error', rejectWebP)
          .run();
      });
    };

    // Generate JPEG first, then WebP
    generateJPEG()
      .then(() => generateWebP())
      .then(() => resolve(thumbnailPath))
      .catch((err) => {
        console.error('Error generating thumbnail:', err);
        reject(err);
      });
  });
};
module.exports = {
  getVideoInfo,
  getDetailedVideoInfo,
  generateThumbnail
};