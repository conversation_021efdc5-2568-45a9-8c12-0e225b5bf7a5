<% layout('layout') -%>

<!-- Header -->
<div class="bg-dark-800 border-b border-gray-700 p-6 -mx-6 -mt-6 mb-6">
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-white"><%= t('admin.performance_monitoring') %></h1>
      <p class="text-gray-400 mt-1"><%= t('admin.performance_monitoring_subtitle') %></p>
    </div>
    <div class="flex items-center space-x-4">
      <button onclick="refreshData()" class="px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors">
        <i class="ti ti-refresh mr-2"></i><%= t('admin.refresh_data') %>
      </button>
      <button onclick="clearCache()" class="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg transition-colors">
        <i class="ti ti-trash mr-2"></i><%= t('admin.clear_cache') %>
      </button>
    </div>
  </div>
</div>

<!-- Performance Overview Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
  <!-- Response Time -->
  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm"><%= t('admin.avg_response_time') %></p>
        <p class="text-2xl font-bold text-white" id="avg-response-time"><%= performanceReport.requests.avgResponseTime %></p>
        <p class="text-xs text-gray-500"><%= t('admin.milliseconds') %></p>
      </div>
      <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-clock text-blue-400 text-xl"></i>
      </div>
    </div>
  </div>

  <!-- Cache Hit Rate -->
  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm"><%= t('admin.cache_hit_rate') %></p>
        <p class="text-2xl font-bold text-white" id="cache-hit-rate"><%= cacheStats.hitRate %></p>
        <p class="text-xs text-gray-500"><%= cacheStats.size %>/<%= cacheStats.maxSize %> <%= t('admin.entries') %></p>
      </div>
      <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-database text-green-400 text-xl"></i>
      </div>
    </div>
  </div>

  <!-- Database Queries -->
  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm"><%= t('admin.db_queries') %></p>
        <p class="text-2xl font-bold text-white" id="db-queries"><%= performanceReport.database.queries %></p>
        <p class="text-xs text-gray-500"><%= performanceReport.database.slowQueries %> <%= t('admin.slow_queries') %></p>
      </div>
      <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-database-search text-purple-400 text-xl"></i>
      </div>
    </div>
  </div>

  <!-- Error Rate -->
  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm"><%= t('admin.error_rate') %></p>
        <p class="text-2xl font-bold text-white" id="error-rate">
          <%= performanceReport.requests.total > 0 ? ((performanceReport.requests.errors / performanceReport.requests.total) * 100).toFixed(2) : 0 %>%
        </p>
        <p class="text-xs text-gray-500"><%= performanceReport.requests.errors %>/<%= performanceReport.requests.total %> <%= t('admin.requests') %></p>
      </div>
      <div class="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-alert-triangle text-red-400 text-xl"></i>
      </div>
    </div>
  </div>
</div>

<!-- Performance Alerts -->
<% if (performanceReport.alerts && performanceReport.alerts.length > 0) { %>
<div class="bg-red-900/20 border border-red-500/30 rounded-lg p-4 mb-8">
  <h3 class="text-red-400 font-semibold mb-3 flex items-center">
    <i class="ti ti-alert-triangle mr-2"></i><%= t('admin.performance_alerts') %>
  </h3>
  <div class="space-y-2">
    <% performanceReport.alerts.forEach(alert => { %>
      <div class="flex items-center justify-between bg-red-900/30 rounded p-3">
        <div>
          <span class="text-red-300 font-medium"><%= alert.type.replace(/_/g, ' ').toUpperCase() %></span>
          <p class="text-red-200 text-sm"><%= alert.message %></p>
        </div>
        <span class="text-xs text-red-400"><%= new Date(alert.timestamp).toLocaleTimeString() %></span>
      </div>
    <% }) %>
  </div>
</div>
<% } %>

<!-- Performance Recommendations -->
<% if (performanceReport.recommendations && performanceReport.recommendations.length > 0) { %>
<div class="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4 mb-8">
  <h3 class="text-yellow-400 font-semibold mb-3 flex items-center">
    <i class="ti ti-lightbulb mr-2"></i><%= t('admin.performance_recommendations') %>
  </h3>
  <div class="space-y-2">
    <% performanceReport.recommendations.forEach(rec => { %>
      <div class="bg-yellow-900/30 rounded p-3">
        <div class="flex items-center justify-between mb-2">
          <span class="text-yellow-300 font-medium"><%= rec.type.replace(/_/g, ' ').toUpperCase() %></span>
          <span class="px-2 py-1 text-xs rounded <%= rec.severity === 'high' ? 'bg-red-600' : rec.severity === 'medium' ? 'bg-yellow-600' : 'bg-blue-600' %> text-white">
            <%= rec.severity.toUpperCase() %>
          </span>
        </div>
        <p class="text-yellow-200 text-sm mb-1"><%= rec.message %></p>
        <p class="text-yellow-300 text-xs"><strong><%= t('admin.suggestion') %>:</strong> <%= rec.suggestion %></p>
      </div>
    <% }) %>
  </div>
</div>
<% } %>

<!-- Detailed Metrics -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
  <!-- System Metrics -->
  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <h3 class="text-xl font-semibold text-white mb-4"><%= t('admin.system_metrics') %></h3>
    
    <div class="space-y-4">
      <div>
        <div class="flex justify-between text-sm mb-1">
          <span class="text-gray-300"><%= t('admin.cpu_usage') %></span>
          <span class="text-white" id="cpu-usage"><%= performanceReport.system.cpuUsage %>%</span>
        </div>
        <div class="w-full bg-gray-700 rounded-full h-2">
          <div class="bg-orange-400 h-2 rounded-full transition-all duration-300" style="width: <%= performanceReport.system.cpuUsage %>%"></div>
        </div>
      </div>
      
      <div>
        <div class="flex justify-between text-sm mb-1">
          <span class="text-gray-300"><%= t('admin.memory_usage') %></span>
          <span class="text-white" id="memory-usage"><%= performanceReport.system.memoryUsage %>%</span>
        </div>
        <div class="w-full bg-gray-700 rounded-full h-2">
          <div class="bg-blue-400 h-2 rounded-full transition-all duration-300" style="width: <%= performanceReport.system.memoryUsage %>%"></div>
        </div>
      </div>
      
      <div class="grid grid-cols-2 gap-4 pt-4">
        <div class="text-center">
          <p class="text-2xl font-bold text-white" id="uptime"><%= Math.floor(performanceReport.system.uptime / 3600) %></p>
          <p class="text-xs text-gray-400"><%= t('admin.hours_uptime') %></p>
        </div>
        <div class="text-center">
          <p class="text-2xl font-bold text-white" id="active-streams"><%= performanceReport.streams.active %></p>
          <p class="text-xs text-gray-400"><%= t('admin.active_streams') %></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Database Performance -->
  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <h3 class="text-xl font-semibold text-white mb-4"><%= t('admin.database_performance') %></h3>
    
    <div class="space-y-4">
      <div class="grid grid-cols-2 gap-4">
        <div class="text-center">
          <p class="text-2xl font-bold text-white"><%= performanceReport.database.avgQueryTime %></p>
          <p class="text-xs text-gray-400"><%= t('admin.avg_query_time') %></p>
        </div>
        <div class="text-center">
          <p class="text-2xl font-bold text-white"><%= dbStats.estimatedSize.toFixed(2) %></p>
          <p class="text-xs text-gray-400"><%= t('admin.database_size') %></p>
        </div>
      </div>
      
      <div class="space-y-2">
        <h4 class="text-sm font-medium text-gray-300"><%= t('admin.tables_indexes') %></h4>
        <% dbStats.tables.forEach(table => { %>
          <div class="flex justify-between text-sm">
            <span class="text-gray-400"><%= table.table_name %></span>
            <span class="text-white"><%= table.index_count %> <%= t('admin.indexes') %></span>
          </div>
        <% }) %>
      </div>
    </div>
  </div>
</div>

<!-- Cache Details -->
<div class="bg-dark-800 rounded-lg p-6 border border-gray-700 mb-8">
  <h3 class="text-xl font-semibold text-white mb-4"><%= t('admin.cache_performance') %></h3>

  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <div class="text-center">
      <p class="text-3xl font-bold text-green-400" id="cache-hits"><%= cacheStats.hits %></p>
      <p class="text-sm text-gray-400"><%= t('admin.cache_hits') %></p>
    </div>
    <div class="text-center">
      <p class="text-3xl font-bold text-red-400" id="cache-misses"><%= cacheStats.misses %></p>
      <p class="text-sm text-gray-400"><%= t('admin.cache_misses') %></p>
    </div>
    <div class="text-center">
      <p class="text-3xl font-bold text-blue-400" id="cache-size"><%= cacheStats.size %></p>
      <p class="text-sm text-gray-400"><%= t('admin.cache_entries') %></p>
    </div>
  </div>
  
  <div class="mt-6">
    <div class="flex justify-between text-sm mb-2">
      <span class="text-gray-300"><%= t('admin.cache_usage') %></span>
      <span class="text-white"><%= cacheStats.size %>/<%= cacheStats.maxSize %></span>
    </div>
    <div class="w-full bg-gray-700 rounded-full h-2">
      <div class="bg-green-400 h-2 rounded-full transition-all duration-300" style="width: <%= (cacheStats.size / cacheStats.maxSize * 100) %>%"></div>
    </div>
  </div>
</div>

<!-- Recent Performance History -->
<div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
  <h3 class="text-xl font-semibold text-white mb-4"><%= t('admin.recent_performance_history') %></h3>
  
  <div class="space-y-4">
    <% if (performanceReport.requests.responseTimeHistory && performanceReport.requests.responseTimeHistory.length > 0) { %>
      <div>
        <h4 class="text-sm font-medium text-gray-300 mb-2"><%= t('admin.response_times_last_10') %></h4>
        <div class="grid grid-cols-5 gap-2">
          <% performanceReport.requests.responseTimeHistory.slice(-10).forEach(entry => { %>
            <div class="text-center">
              <div class="h-8 bg-blue-600 rounded-t" style="height: <%= Math.min(entry.time / 10, 32) %>px"></div>
              <p class="text-xs text-gray-400 mt-1"><%= entry.time %>ms</p>
            </div>
          <% }) %>
        </div>
      </div>
    <% } %>
    
    <% if (performanceReport.database.queryTimeHistory && performanceReport.database.queryTimeHistory.length > 0) { %>
      <div>
        <h4 class="text-sm font-medium text-gray-300 mb-2"><%= t('admin.database_query_times_last_10') %></h4>
        <div class="space-y-1">
          <% performanceReport.database.queryTimeHistory.slice(-5).forEach(entry => { %>
            <div class="flex justify-between text-sm">
              <span class="text-gray-400 truncate"><%= entry.query %>...</span>
              <span class="text-white"><%= entry.time %>ms</span>
            </div>
          <% }) %>
        </div>
      </div>
    <% } %>
  </div>
</div>

<script>
// Translation variables for JavaScript
const translations = {
  clearCacheConfirm: '<%= t("admin.clear_cache_confirm") %>',
  cacheCleared: '<%= t("admin.cache_cleared_success") %>',
  failedClearCache: '<%= t("admin.failed_clear_cache") %>'
};

// Auto-refresh data every 30 seconds
setInterval(refreshData, 30000);

async function refreshData() {
  try {
    const response = await fetch('/api/performance/summary');
    const data = await response.json();
    
    // Update metrics
    document.getElementById('avg-response-time').textContent = data.requests.avgResponseTime;
    document.getElementById('db-queries').textContent = data.database.queries;
    document.getElementById('cpu-usage').textContent = data.system.cpuUsage + '%';
    document.getElementById('memory-usage').textContent = data.system.memoryUsage + '%';
    document.getElementById('uptime').textContent = Math.floor(data.system.uptime / 3600);
    document.getElementById('active-streams').textContent = data.streams.active;
    
    // Update cache stats
    const cacheResponse = await fetch('/api/cache/stats');
    const cacheData = await cacheResponse.json();
    
    document.getElementById('cache-hit-rate').textContent = cacheData.hitRate;
    document.getElementById('cache-hits').textContent = cacheData.hits;
    document.getElementById('cache-misses').textContent = cacheData.misses;
    document.getElementById('cache-size').textContent = cacheData.size;
    
    console.log('[Performance] Data refreshed');
  } catch (error) {
    console.error('[Performance] Failed to refresh data:', error);
  }
}

async function clearCache() {
  if (!confirm(translations.clearCacheConfirm)) {
    return;
  }

  try {
    const response = await fetch('/api/cache/clear', { method: 'POST' });
    const result = await response.json();

    if (result.success) {
      alert(translations.cacheCleared);
      refreshData();
    } else {
      alert(translations.failedClearCache + ': ' + result.error);
    }
  } catch (error) {
    alert(translations.failedClearCache + ': ' + error.message);
  }
}
</script>
