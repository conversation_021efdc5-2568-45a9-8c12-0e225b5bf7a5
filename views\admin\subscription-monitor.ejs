<% layout('admin/layout') -%>

<div class="container-fluid">
  <!-- Header -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h1 class="h3 mb-0 text-gray-800">🔍 Subscription Monitor</h1>
      <p class="text-muted">Monitor and manage subscription expiry and slot limits</p>
    </div>
    <div>
      <button class="btn btn-primary" onclick="forceCheck()">
        <i class="fas fa-sync-alt"></i> Force Check
      </button>
      <button class="btn btn-success" onclick="toggleMonitor('start')" id="startBtn" 
              style="<%= stats.isRunning ? 'display: none;' : '' %>">
        <i class="fas fa-play"></i> Start Monitor
      </button>
      <button class="btn btn-warning" onclick="toggleMonitor('stop')" id="stopBtn"
              style="<%= !stats.isRunning ? 'display: none;' : '' %>">
        <i class="fas fa-stop"></i> Stop Monitor
      </button>
    </div>
  </div>

  <!-- Status Cards -->
  <div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-<%= stats.isRunning ? 'success' : 'danger' %> shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-<%= stats.isRunning ? 'success' : 'danger' %> text-uppercase mb-1">
                Monitor Status
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                <%= stats.isRunning ? 'Running' : 'Stopped' %>
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-<%= stats.isRunning ? 'play-circle' : 'stop-circle' %> fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-info shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                Total Checks
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                <%= stats.totalChecks %>
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-search fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-warning shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                Expired Subscriptions
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                <%= stats.expiredSubscriptionsProcessed %>
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-calendar-times fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-danger shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                Streams Stopped
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                <%= stats.streamsStoppedDueToExpiry + stats.streamsStoppedDueToSlotLimit %>
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-stop fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Monitor Configuration -->
  <div class="row mb-4">
    <div class="col-lg-6">
      <div class="card shadow">
        <div class="card-header py-3">
          <h6 class="m-0 font-weight-bold text-primary">⚙️ Monitor Configuration</h6>
        </div>
        <div class="card-body">
          <div class="form-group">
            <label for="checkInterval">Check Interval (minutes)</label>
            <div class="input-group">
              <input type="number" class="form-control" id="checkInterval" 
                     value="<%= Math.round(stats.checkInterval / 60000) %>" min="1" max="60">
              <div class="input-group-append">
                <button class="btn btn-outline-secondary" onclick="updateInterval()">Update</button>
              </div>
            </div>
            <small class="form-text text-muted">How often to check for expired subscriptions and slot limits</small>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>Last Check</label>
                <div class="form-control-plaintext">
                  <%= stats.lastCheck ? new Date(stats.lastCheck).toLocaleString() : 'Never' %>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>Next Check</label>
                <div class="form-control-plaintext">
                  <%= stats.nextCheck ? new Date(stats.nextCheck).toLocaleString() : 'N/A' %>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-6">
      <div class="card shadow">
        <div class="card-header py-3">
          <h6 class="m-0 font-weight-bold text-primary">📊 Activity Summary</h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>Last Expiry Processing</label>
                <div class="form-control-plaintext">
                  <%= stats.lastProcessedExpiry ? new Date(stats.lastProcessedExpiry).toLocaleString() : 'Never' %>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>Last Slot Enforcement</label>
                <div class="form-control-plaintext">
                  <%= stats.lastSlotEnforcement ? new Date(stats.lastSlotEnforcement).toLocaleString() : 'Never' %>
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>Streams Stopped (Expiry)</label>
                <div class="form-control-plaintext text-warning">
                  <%= stats.streamsStoppedDueToExpiry %>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>Streams Stopped (Slot Limit)</label>
                <div class="form-control-plaintext text-danger">
                  <%= stats.streamsStoppedDueToSlotLimit %>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Emergency Actions -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow">
        <div class="card-header py-3">
          <h6 class="m-0 font-weight-bold text-danger">🚨 Emergency Actions</h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="emergencyUserId">User ID for Emergency Stop</label>
                <div class="input-group">
                  <input type="text" class="form-control" id="emergencyUserId" 
                         placeholder="Enter user ID">
                  <div class="input-group-append">
                    <button class="btn btn-danger" onclick="emergencyStopUser()">
                      <i class="fas fa-stop"></i> Emergency Stop
                    </button>
                  </div>
                </div>
                <small class="form-text text-muted">Stop all streams for a specific user immediately</small>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="emergencyReason">Reason (optional)</label>
                <input type="text" class="form-control" id="emergencyReason" 
                       placeholder="Emergency stop reason">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Real-time Log -->
  <div class="row">
    <div class="col-12">
      <div class="card shadow">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
          <h6 class="m-0 font-weight-bold text-primary">📝 Monitor Activity Log</h6>
          <button class="btn btn-sm btn-outline-secondary" onclick="refreshStats()">
            <i class="fas fa-sync-alt"></i> Refresh
          </button>
        </div>
        <div class="card-body">
          <div id="activityLog" class="bg-dark text-light p-3 rounded" style="height: 300px; overflow-y: auto; font-family: monospace;">
            <div class="text-success">[<%= new Date().toLocaleString() %>] Subscription Monitor Dashboard Loaded</div>
            <div class="text-info">[<%= new Date().toLocaleString() %>] Monitor Status: <%= stats.isRunning ? 'Running' : 'Stopped' %></div>
            <div class="text-info">[<%= new Date().toLocaleString() %>] Total Checks: <%= stats.totalChecks %></div>
            <div class="text-warning">[<%= new Date().toLocaleString() %>] Expired Subscriptions Processed: <%= stats.expiredSubscriptionsProcessed %></div>
            <div class="text-danger">[<%= new Date().toLocaleString() %>] Total Streams Stopped: <%= stats.streamsStoppedDueToExpiry + stats.streamsStoppedDueToSlotLimit %></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Auto-refresh stats every 30 seconds
setInterval(refreshStats, 30000);

async function refreshStats() {
  try {
    const response = await fetch('/admin/api/subscription-monitor/stats');
    const data = await response.json();
    
    if (data.success) {
      // Update status indicators
      updateStatusDisplay(data.stats);
      addLogEntry(`Stats refreshed - Running: ${data.stats.isRunning}`, 'info');
    }
  } catch (error) {
    console.error('Error refreshing stats:', error);
    addLogEntry('Error refreshing stats: ' + error.message, 'error');
  }
}

function updateStatusDisplay(stats) {
  // Update cards and status indicators
  location.reload(); // Simple approach - reload page to update all stats
}

async function forceCheck() {
  try {
    addLogEntry('Forcing subscription monitor check...', 'info');
    const response = await fetch('/admin/api/subscription-monitor/force-check', {
      method: 'POST'
    });
    const data = await response.json();
    
    if (data.success) {
      addLogEntry('Force check completed successfully', 'success');
      setTimeout(refreshStats, 2000);
    } else {
      addLogEntry('Force check failed: ' + data.error, 'error');
    }
  } catch (error) {
    console.error('Error forcing check:', error);
    addLogEntry('Error forcing check: ' + error.message, 'error');
  }
}

async function toggleMonitor(action) {
  try {
    addLogEntry(`${action === 'start' ? 'Starting' : 'Stopping'} subscription monitor...`, 'info');
    const response = await fetch('/admin/api/subscription-monitor/toggle', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action })
    });
    const data = await response.json();
    
    if (data.success) {
      addLogEntry(data.message, 'success');
      
      // Toggle button visibility
      if (action === 'start') {
        document.getElementById('startBtn').style.display = 'none';
        document.getElementById('stopBtn').style.display = 'inline-block';
      } else {
        document.getElementById('stopBtn').style.display = 'none';
        document.getElementById('startBtn').style.display = 'inline-block';
      }
      
      setTimeout(refreshStats, 1000);
    } else {
      addLogEntry('Toggle failed: ' + data.error, 'error');
    }
  } catch (error) {
    console.error('Error toggling monitor:', error);
    addLogEntry('Error toggling monitor: ' + error.message, 'error');
  }
}

async function updateInterval() {
  try {
    const minutes = document.getElementById('checkInterval').value;
    if (!minutes || minutes < 1) {
      addLogEntry('Invalid interval. Must be at least 1 minute.', 'error');
      return;
    }
    
    addLogEntry(`Updating check interval to ${minutes} minutes...`, 'info');
    const response = await fetch('/admin/api/subscription-monitor/interval', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ minutes: parseInt(minutes) })
    });
    const data = await response.json();
    
    if (data.success) {
      addLogEntry(data.message, 'success');
    } else {
      addLogEntry('Update interval failed: ' + data.error, 'error');
    }
  } catch (error) {
    console.error('Error updating interval:', error);
    addLogEntry('Error updating interval: ' + error.message, 'error');
  }
}

async function emergencyStopUser() {
  try {
    const userId = document.getElementById('emergencyUserId').value;
    const reason = document.getElementById('emergencyReason').value;
    
    if (!userId) {
      addLogEntry('User ID is required for emergency stop', 'error');
      return;
    }
    
    if (!confirm(`Are you sure you want to emergency stop all streams for user ${userId}?`)) {
      return;
    }
    
    addLogEntry(`Emergency stopping streams for user ${userId}...`, 'warning');
    const response = await fetch(`/admin/api/subscription-monitor/emergency-stop/${userId}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ reason })
    });
    const data = await response.json();
    
    if (data.success) {
      addLogEntry(data.message, 'success');
      document.getElementById('emergencyUserId').value = '';
      document.getElementById('emergencyReason').value = '';
    } else {
      addLogEntry('Emergency stop failed: ' + data.error, 'error');
    }
  } catch (error) {
    console.error('Error emergency stopping user:', error);
    addLogEntry('Error emergency stopping user: ' + error.message, 'error');
  }
}

function addLogEntry(message, type = 'info') {
  const log = document.getElementById('activityLog');
  const timestamp = new Date().toLocaleString();
  const colorClass = {
    'info': 'text-info',
    'success': 'text-success', 
    'warning': 'text-warning',
    'error': 'text-danger'
  }[type] || 'text-light';
  
  const entry = document.createElement('div');
  entry.className = colorClass;
  entry.textContent = `[${timestamp}] ${message}`;
  
  log.appendChild(entry);
  log.scrollTop = log.scrollHeight;
  
  // Keep only last 50 entries
  while (log.children.length > 50) {
    log.removeChild(log.firstChild);
  }
}
</script>
