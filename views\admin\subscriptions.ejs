<% layout('layout') -%>

<!-- jQuery and DataTables -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css">
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>

<!-- Header -->
<div class="bg-dark-800 border-b border-gray-700 p-6 -mx-6 -mt-6 mb-6">
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-white"><%= t('admin.subscription_management') %></h1>
      <p class="text-gray-400 mt-1"><%= t('admin.subscription_management_desc') %></p>
    </div>
    <div>
      <button onclick="showCreateSubscriptionModal()" class="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg transition-colors">
        <i class="ti ti-plus mr-2"></i><%= t('admin.create_subscription') %>
      </button>
    </div>
  </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm">Total <%= t('admin.subscriptions') %></p>
        <p class="text-2xl font-bold text-white"><%= totalSubscriptions %></p>
      </div>
      <div class="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-credit-card text-primary text-xl"></i>
      </div>
    </div>
  </div>

  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm"><%= t('admin.active') %> <%= t('admin.subscriptions') %></p>
        <p class="text-2xl font-bold text-white">
          <%= subscriptions.filter(s => s.status === 'active').length %>
        </p>
      </div>
      <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-check text-green-400 text-xl"></i>
      </div>
    </div>
  </div>

  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm"><%= t('admin.expired') %> <%= t('admin.subscriptions') %></p>
        <p class="text-2xl font-bold text-white">
          <%= subscriptions.filter(s => s.status === 'expired').length %>
        </p>
      </div>
      <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-alert-triangle text-yellow-400 text-xl"></i>
      </div>
    </div>
  </div>

  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm"><%= t('admin.cancelled') %> <%= t('admin.subscriptions') %></p>
        <p class="text-2xl font-bold text-white">
          <%= subscriptions.filter(s => s.status === 'cancelled').length %>
        </p>
      </div>
      <div class="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-x text-red-400 text-xl"></i>
      </div>
    </div>
  </div>
</div>

<!-- Subscriptions Table -->
<div class="bg-dark-800 rounded-lg border border-gray-700">
  <div class="p-6 border-b border-gray-700">
    <h3 class="text-lg font-semibold text-white"><%= t('admin.subscriptions') %></h3>
  </div>
  <div class="overflow-x-auto">
    <table class="min-w-full" id="subscriptionsTable">
      <thead class="bg-gray-700">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"><%= t('admin.user') %></th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"><%= t('admin.plan') %></th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"><%= t('admin.subscription_status') %></th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"><%= t('admin.start_date') %></th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"><%= t('admin.end_date') %></th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"><%= t('admin.payment_method') %></th>
          <th class="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider"><%= t('admin.actions') %></th>
        </tr>
      </thead>
      <tbody class="divide-y divide-gray-700">
        <% subscriptions.forEach(function(subscription) { %>
          <tr class="hover:bg-dark-700/50 transition-colors">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                  <i class="ti ti-user text-white"></i>
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-white"><%= subscription.username %></div>
                  <div class="text-sm text-gray-400"><%= subscription.email %></div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"><%= subscription.plan_name %></span>
              <% if (subscription.plan_price > 0) { %>
                <div class="text-sm text-gray-400 mt-1">
                  <%= subscription.plan_currency === 'IDR' ? 'Rp' : '$' %><%= subscription.plan_price.toLocaleString() %>
                </div>
              <% } %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <%
                let statusClass = 'bg-gray-100 text-gray-800';
                if (subscription.status === 'active') statusClass = 'bg-green-100 text-green-800';
                else if (subscription.status === 'expired') statusClass = 'bg-yellow-100 text-yellow-800';
                else if (subscription.status === 'cancelled') statusClass = 'bg-red-100 text-red-800';
              %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= statusClass %>">
                <%= t('admin.' + subscription.status) %>
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
              <%= new Date(subscription.start_date).toLocaleDateString() %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
              <%= new Date(subscription.end_date).toLocaleDateString() %>
              <% if (new Date(subscription.end_date) < new Date() && subscription.status === 'active') { %>
                <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 ml-1">Expired</span>
              <% } %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"><%= subscription.payment_method %></span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex items-center justify-end space-x-2">
                <button onclick="viewUserHistory('<%= subscription.user_id %>', '<%= subscription.username %>')" class="text-blue-400 hover:text-blue-300" title="<%= t('admin.view_history') %>">
                  <i class="ti ti-history"></i>
                </button>
                <% if (subscription.status === 'active') { %>
                  <button onclick="showExtendModal('<%= subscription.id %>', '<%= subscription.username %>', '<%= subscription.end_date %>')" class="text-green-400 hover:text-green-300" title="<%= t('admin.extend') %>">
                    <i class="ti ti-calendar-plus"></i>
                  </button>
                  <button onclick="cancelSubscription('<%= subscription.id %>', '<%= subscription.username %>')" class="text-red-400 hover:text-red-300" title="<%= t('admin.cancel') %>">
                    <i class="ti ti-x"></i>
                  </button>
                <% } %>
              </div>
            </td>
          </tr>
        <% }); %>
      </tbody>
    </table>
  </div>

  <!-- Pagination -->
  <% if (totalPages > 1) { %>
    <div class="p-6 border-t border-gray-700">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-400">
          Showing <%= ((currentPage - 1) * limit) + 1 %> to <%= Math.min(currentPage * limit, totalSubscriptions) %> of <%= totalSubscriptions %> subscriptions
        </div>

        <div class="flex items-center space-x-2">
          <!-- Previous Button -->
          <% if (hasPrevPage) { %>
            <a href="?page=<%= prevPage %>"
               class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 hover:bg-dark-600 text-gray-400 hover:text-white transition-colors">
              <i class="ti ti-chevron-left"></i>
            </a>
          <% } else { %>
            <span class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 text-gray-500 cursor-not-allowed">
              <i class="ti ti-chevron-left"></i>
            </span>
          <% } %>

          <!-- Page Numbers -->
          <% for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) { %>
            <% if (i === currentPage) { %>
              <span class="w-9 h-9 flex items-center justify-center rounded-lg bg-primary text-white font-medium">
                <%= i %>
              </span>
            <% } else { %>
              <a href="?page=<%= i %>"
                 class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 hover:bg-dark-600 text-gray-400 hover:text-white transition-colors">
                <%= i %>
              </a>
            <% } %>
          <% } %>

          <!-- Next Button -->
          <% if (hasNextPage) { %>
            <a href="?page=<%= nextPage %>"
               class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 hover:bg-dark-600 text-gray-400 hover:text-white transition-colors">
              <i class="ti ti-chevron-right"></i>
            </a>
          <% } else { %>
            <span class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 text-gray-500 cursor-not-allowed">
              <i class="ti ti-chevron-right"></i>
            </span>
          <% } %>
        </div>
      </div>
    </div>
  <% } %>
</div>

<!-- Create Subscription Modal -->
<div id="createSubscriptionModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
  <div class="bg-dark-800 rounded-lg border border-gray-700 w-full max-w-md mx-4">
    <div class="flex items-center justify-between p-6 border-b border-gray-700">
      <h3 class="text-lg font-semibold text-white"><%= t('admin.create_subscription') %></h3>
      <button type="button" onclick="hideCreateSubscriptionModal()" class="text-gray-400 hover:text-white">
        <i class="ti ti-x text-xl"></i>
      </button>
    </div>

    <form id="createSubscriptionForm">
      <div class="p-6 space-y-4">
        <div>
          <label for="createUserId" class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.user') %></label>
          <input type="text" id="createUserId" placeholder="Enter User ID or Username" required
                 class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
          <p class="text-xs text-gray-400 mt-1">You can enter either the user ID (UUID) or username</p>
        </div>

        <div>
          <label for="createPlanId" class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.plan') %></label>
          <select id="createPlanId" required
                  class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
            <option value="">Select Plan</option>
            <% plans.forEach(function(plan) { %>
              <option value="<%= plan.id %>"><%= plan.name %> - <%= plan.currency === 'IDR' ? 'Rp' : '$' %><%= plan.price.toLocaleString() %></option>
            <% }); %>
          </select>
        </div>

        <div>
          <label for="createStartDate" class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.start_date') %></label>
          <input type="datetime-local" id="createStartDate"
                 class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
          <p class="text-xs text-gray-400 mt-1">Leave empty for current date/time</p>
        </div>

        <div>
          <label for="createEndDate" class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.end_date') %></label>
          <input type="datetime-local" id="createEndDate" required
                 class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
        </div>

        <div>
          <label for="createPaymentMethod" class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.payment_method') %></label>
          <select id="createPaymentMethod"
                  class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
            <option value="admin_manual">Admin Manual</option>
            <option value="midtrans">Midtrans</option>
            <option value="free">Free</option>
          </select>
        </div>
      </div>

      <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-700">
        <button type="button" onclick="hideCreateSubscriptionModal()"
                class="px-4 py-2 text-gray-400 hover:text-white transition-colors">
          Cancel
        </button>
        <button type="submit"
                class="px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors">
          Create Subscription
        </button>
      </div>
    </form>
  </div>
</div>

<!-- Extend Subscription Modal -->
<div id="extendSubscriptionModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
  <div class="bg-dark-800 rounded-lg border border-gray-700 w-full max-w-md mx-4">
    <div class="flex items-center justify-between p-6 border-b border-gray-700">
      <h3 class="text-lg font-semibold text-white"><%= t('admin.extend_subscription') %></h3>
      <button type="button" onclick="hideExtendSubscriptionModal()" class="text-gray-400 hover:text-white">
        <i class="ti ti-x text-xl"></i>
      </button>
    </div>

    <form id="extendSubscriptionForm">
      <div class="p-6 space-y-4">
        <input type="hidden" id="extendSubscriptionId">

        <div class="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4">
          <div class="text-sm text-blue-300">
            <div><strong>User:</strong> <span id="extendUsername" class="text-white"></span></div>
            <div><strong>Current End Date:</strong> <span id="extendCurrentEndDate" class="text-white"></span></div>
          </div>
        </div>

        <div>
          <label for="extendDays" class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.extension_days') %></label>
          <input type="number" id="extendDays" min="1" max="365" placeholder="30"
                 class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
          <p class="text-xs text-gray-400 mt-1">Number of days to extend from current end date</p>
        </div>

        <div class="text-center py-2">
          <span class="text-gray-400 font-medium">OR</span>
        </div>

        <div>
          <label for="extendNewEndDate" class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.custom_end_date') %></label>
          <input type="datetime-local" id="extendNewEndDate"
                 class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
          <p class="text-xs text-gray-400 mt-1">Set specific end date (overrides extension days)</p>
        </div>
      </div>

      <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-700">
        <button type="button" onclick="hideExtendSubscriptionModal()"
                class="px-4 py-2 text-gray-400 hover:text-white transition-colors">
          Cancel
        </button>
        <button type="submit"
                class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
          Extend Subscription
        </button>
      </div>
    </form>
  </div>
</div>

<!-- User History Modal -->
<div id="userHistoryModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
  <div class="bg-dark-800 rounded-lg border border-gray-700 w-full max-w-4xl mx-4 max-h-[90vh] flex flex-col">
    <div class="flex items-center justify-between p-6 border-b border-gray-700">
      <h3 class="text-lg font-semibold text-white"><%= t('admin.subscription_history') %></h3>
      <button type="button" onclick="hideUserHistoryModal()" class="text-gray-400 hover:text-white">
        <i class="ti ti-x text-xl"></i>
      </button>
    </div>

    <div class="p-6 flex-1 overflow-y-auto">
      <div class="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4 mb-6">
        <div class="text-sm text-blue-300">
          <strong>User:</strong> <span id="historyUsername" class="text-white"></span>
        </div>
      </div>

      <div id="historyContent">
        <div class="text-center py-8">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p class="text-gray-400 mt-2">Loading...</p>
        </div>
      </div>
    </div>

    <div class="flex items-center justify-end p-6 border-t border-gray-700">
      <button type="button" onclick="hideUserHistoryModal()"
              class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
        Close
      </button>
    </div>
  </div>
</div>

<script>
// Initialize DataTable
$(document).ready(function() {
  $('#subscriptionsTable').DataTable({
    "pageLength": 10,
    "order": [[ 3, "desc" ]], // Sort by start date descending
    "columnDefs": [
      { "orderable": false, "targets": 6 } // Disable sorting on actions column
    ]
  });
});

// Show create subscription modal
function showCreateSubscriptionModal() {
  // Set default end date to 30 days from now
  const defaultEndDate = new Date();
  defaultEndDate.setDate(defaultEndDate.getDate() + 30);
  document.getElementById('createEndDate').value = defaultEndDate.toISOString().slice(0, 16);

  document.getElementById('createSubscriptionModal').classList.remove('hidden');
}

// Hide create subscription modal
function hideCreateSubscriptionModal() {
  document.getElementById('createSubscriptionModal').classList.add('hidden');
}

// Handle create subscription form
console.log('Setting up create subscription form event listener...');
const createForm = document.getElementById('createSubscriptionForm');
console.log('Create form element:', createForm);

if (createForm) {
  createForm.addEventListener('submit', async function(e) {
    e.preventDefault();
    console.log('Create subscription form submitted');

    const formData = {
    userId: document.getElementById('createUserId').value,
    planId: document.getElementById('createPlanId').value,
    startDate: document.getElementById('createStartDate').value || null,
    endDate: document.getElementById('createEndDate').value,
    paymentMethod: document.getElementById('createPaymentMethod').value
  };

  console.log('Form data:', formData);

  try {
    console.log('Sending request to create subscription...');
    const response = await fetch('/admin/subscriptions/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': '<%= csrfToken %>'
      },
      body: JSON.stringify(formData)
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers);

    const result = await response.json();
    console.log('Response result:', result);

    if (result.success) {
      alert('<%= t("admin.subscription_created") %>');
      location.reload();
    } else {
      alert('Error: ' + result.error);
    }
  } catch (error) {
    console.error('Error creating subscription:', error);
    alert('Error: ' + error.message);
  }
  });
} else {
  console.error('Create subscription form not found!');
}

// Show extend subscription modal
function showExtendModal(subscriptionId, username, currentEndDate) {
  document.getElementById('extendSubscriptionId').value = subscriptionId;
  document.getElementById('extendUsername').textContent = username;
  document.getElementById('extendCurrentEndDate').textContent = new Date(currentEndDate).toLocaleString();

  // Clear form
  document.getElementById('extendDays').value = '';
  document.getElementById('extendNewEndDate').value = '';

  document.getElementById('extendSubscriptionModal').classList.remove('hidden');
}

// Hide extend subscription modal
function hideExtendSubscriptionModal() {
  document.getElementById('extendSubscriptionModal').classList.add('hidden');
}

// Handle extend subscription form
document.getElementById('extendSubscriptionForm').addEventListener('submit', async function(e) {
  e.preventDefault();

  const subscriptionId = document.getElementById('extendSubscriptionId').value;
  const extensionDays = document.getElementById('extendDays').value;
  const newEndDate = document.getElementById('extendNewEndDate').value;

  if (!extensionDays && !newEndDate) {
    alert('Please specify extension days or new end date');
    return;
  }

  const formData = {
    extensionDays: extensionDays || null,
    newEndDate: newEndDate || null
  };

  try {
    const response = await fetch(`/admin/subscriptions/${subscriptionId}/extend`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': '<%= csrfToken %>'
      },
      body: JSON.stringify(formData)
    });

    const result = await response.json();

    if (result.success) {
      alert('<%= t("admin.subscription_extended") %>');
      location.reload();
    } else {
      alert('Error: ' + result.error);
    }
  } catch (error) {
    alert('Error: ' + error.message);
  }
});

// Cancel subscription
async function cancelSubscription(subscriptionId, username) {
  if (!confirm(`Are you sure you want to cancel subscription for ${username}?`)) {
    return;
  }

  try {
    const response = await fetch(`/admin/subscriptions/${subscriptionId}/cancel`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': '<%= csrfToken %>'
      }
    });

    const result = await response.json();

    if (result.success) {
      alert('<%= t("admin.subscription_cancelled") %>');
      location.reload();
    } else {
      alert('Error: ' + result.error);
    }
  } catch (error) {
    alert('Error: ' + error.message);
  }
}

// View user subscription history
async function viewUserHistory(userId, username) {
  document.getElementById('historyUsername').textContent = username;
  document.getElementById('historyContent').innerHTML = `
    <div class="text-center py-8">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      <p class="text-gray-400 mt-2">Loading...</p>
    </div>
  `;

  document.getElementById('userHistoryModal').classList.remove('hidden');

  try {
    const response = await fetch(`/admin/users/${userId}/subscriptions`);
    const result = await response.json();

    if (result.success) {
      let historyHtml = '';

      if (result.subscriptions.length === 0) {
        historyHtml = '<div class="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4 text-blue-300">No subscription history found.</div>';
      } else {
        historyHtml = `
          <div class="overflow-x-auto">
            <table class="min-w-full">
              <thead class="bg-gray-700">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Plan</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Start Date</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">End Date</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Payment Method</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-700">
        `;

        result.subscriptions.forEach(sub => {
          let statusClass = 'bg-gray-100 text-gray-800';
          if (sub.status === 'active') statusClass = 'bg-green-100 text-green-800';
          else if (sub.status === 'expired') statusClass = 'bg-yellow-100 text-yellow-800';
          else if (sub.status === 'cancelled') statusClass = 'bg-red-100 text-red-800';

          historyHtml += `
            <tr class="hover:bg-dark-700/50 transition-colors">
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">${sub.plan_name}</span>
                ${sub.plan_price > 0 ? `<div class="text-sm text-gray-400 mt-1">${sub.plan_currency === 'IDR' ? 'Rp' : '$'}${sub.plan_price.toLocaleString()}</div>` : ''}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClass}">${sub.status}</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">${new Date(sub.start_date).toLocaleDateString()}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">${new Date(sub.end_date).toLocaleDateString()}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">${sub.payment_method}</span>
              </td>
            </tr>
          `;
        });

        historyHtml += '</tbody></table></div>';
      }

      document.getElementById('historyContent').innerHTML = historyHtml;
    } else {
      document.getElementById('historyContent').innerHTML = `
        <div class="bg-red-500/20 border border-red-500/30 rounded-lg p-4 text-red-300">Error: ${result.error}</div>
      `;
    }
  } catch (error) {
    document.getElementById('historyContent').innerHTML = `
      <div class="bg-red-500/20 border border-red-500/30 rounded-lg p-4 text-red-300">Error: ${error.message}</div>
    `;
  }
}

// Hide user history modal
function hideUserHistoryModal() {
  document.getElementById('userHistoryModal').classList.add('hidden');
}
</script>
