<% layout('layout') -%>
  <% function formatFileSize(bytes) { if (bytes < 1024) return bytes + ' B' ; else if (bytes < 1048576) return (bytes /
    1024).toFixed(1) + ' KB' ; else if (bytes < 1073741824) return (bytes / 1048576).toFixed(1) + ' MB' ; else return
    (bytes / 1073741824).toFixed(1) + ' GB' ; } function formatDate(dateString) { const date=new Date(dateString); const
    options={ day: 'numeric' , month: 'short' , year: 'numeric' }; return date.toLocaleDateString('en-US', options); }
    function formatDuration(seconds) { if (!seconds) return '00:00' ; const minutes=Math.floor(seconds / 60); const
    remainingSeconds=Math.floor(seconds % 60); return `${minutes.toString().padStart(2, '0'
    )}:${remainingSeconds.toString().padStart(2, '0' )}`; } %>

    <div class="flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4">
      <div class="text-center md:text-left">
        <h2 class="text-2xl font-bold"><%= t('gallery.title') %></h2>
        <p class="text-gray-400 text-sm mt-1"><%= t('gallery.subtitle') %></p>
      </div>
      <div class="flex flex-col sm:flex-row flex-wrap gap-3">
        <button onclick="openUploadModal()"
          class="flex items-center justify-center gap-2 bg-primary hover:bg-secondary text-white px-4 py-2 rounded-lg transition-colors">
          <i class="ti ti-upload"></i>
          <span><%= t('gallery.upload_video') %></span>
        </button>

        <%
        // Check if user's plan allows Google Drive import (price >= 49000)
        const planPrice = quota && quota.plan ? parseFloat(quota.plan.price) || 0 : 0;
        const hasGoogleDriveAccess = planPrice >= 49000;
        %>

        <% if (hasGoogleDriveAccess) { %>
          <button onclick="openGDriveModal()"
            class="flex items-center justify-center gap-2 bg-[#4285F4]/20 border border-[#4285F4]/50 hover:bg-[#4285F4]/30 text-white px-4 py-2 rounded-lg transition-colors">
            <i class="ti ti-brand-google-drive text-[#4285F4]"></i>
            <span class="hidden sm:inline"><%= t('gallery.import_from_drive') %></span>
            <span class="sm:hidden">Import</span>
          </button>
        <% } else { %>
          <button onclick="showGoogleDriveUpgradePrompt()"
            class="flex items-center justify-center gap-2 bg-gray-600/20 border border-gray-500/50 text-gray-400 px-4 py-2 rounded-lg transition-colors cursor-not-allowed relative group">
            <i class="ti ti-brand-google-drive text-gray-500"></i>
            <span class="hidden sm:inline"><%= t('gallery.import_from_drive') %></span>
            <span class="sm:hidden">Import</span>
            <i class="ti ti-lock text-xs ml-1"></i>

            <!-- Tooltip -->
            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-dark-900 text-white text-sm rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-10">
              Import Google Drive hanya tersedia di plan premium
              <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-dark-900"></div>
            </div>
          </button>
        <% } %>
      </div>
    </div>

    <!-- Gallery Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 mb-6">
      <!-- Total Videos Card -->
      <div class="card-enhanced p-6">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold"><%= t('gallery.total_videos') %></h3>
          <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
            <i class="ti ti-video text-xl text-white"></i>
          </div>
        </div>
        <p class="text-3xl font-bold mt-2">
          <span class="gradient-text"><%= videos ? videos.length : 0 %></span>
        </p>
        <p class="text-sm text-gray-400 mt-2">
          <i class="ti ti-info-circle mr-1"></i>
          <%= t('gallery.total_videos_desc') %>
        </p>
      </div>

      <!-- Storage Information Card -->
      <div class="card-enhanced p-6">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold"><%= t('dashboard.storage_used') %></h3>
          <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
            <i class="ti ti-database text-xl text-white"></i>
          </div>
        </div>
        <p class="text-3xl font-bold mt-2">
          <span id="storage-used" class="gradient-text"><%= quota.storage.current %><%= quota.storage.unit %></span><span class="text-sm text-gray-400"> / <%= quota.storage.max %><%= quota.storage.unit %></span>
        </p>
        <div class="w-full bg-gray-700 rounded-full h-2.5 mt-2">
          <div id="storage-bar" class="bg-gradient-to-r from-purple-500 to-purple-600 h-2.5 rounded-full transition-all duration-300" style="width: <%= quota.storage.percentage %>%"></div>
        </div>
        <p class="text-sm text-gray-400 mt-2">
          <i class="ti ti-info-circle mr-1"></i>
          <%= t('dashboard.storage_used_desc') %>
        </p>
      </div>
    </div>

    <div class="bg-gray-800 rounded-lg p-4 mb-6">
      <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">

        <div class="relative flex-1 sm:max-w-80 md:max-w-96">
          <input type="text" placeholder="<%= t('gallery.search_videos') %>"
            class="w-full bg-dark-700 text-white pl-9 pr-4 py-2.5 border border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
          <i class="ti ti-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
        </div>

        <div class="w-full sm:w-32">
          <select
            class="w-full bg-dark-700 border border-gray-600 text-white px-3 py-2.5 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
            <option selected><%= t('gallery.newest') %></option>
            <option><%= t('gallery.oldest') %></option>
          </select>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5">
      <% if (videos && videos.length> 0) { %>
        <% videos.forEach(function(video) { %>
          <div class="bg-gray-800 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
            <div class="aspect-video relative group">
              <% if (video.thumbnail_path) { %>
                <img src="<%= video.thumbnail_path %>" alt="<%= video.title %>" class="w-full h-full object-cover"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                     loading="lazy">
                <div class="w-full h-full bg-dark-700 flex items-center justify-center" style="display: none;">
                  <div class="text-center">
                    <i class="ti ti-video text-4xl text-gray-500 mb-2"></i>
                    <p class="text-gray-500 text-sm">No Thumbnail</p>
                  </div>
                </div>
              <% } else { %>
                <div class="w-full h-full bg-dark-700 flex items-center justify-center">
                  <div class="text-center">
                    <i class="ti ti-video text-4xl text-gray-500 mb-2"></i>
                    <p class="text-gray-500 text-sm">No Thumbnail</p>
                  </div>
                </div>
              <% } %>

              <div
                class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  class="play-button bg-black/50 hover:bg-primary/80 w-12 h-12 rounded-full flex items-center justify-center transition-colors transform hover:scale-110"
                  data-video-id="<%= video.id %>" data-video-title="<%= video.title %>"
                  onclick="playVideo('<%= video.id %>', '<%= video.title %>')">
                  <i class="ti ti-player-play-filled text-2xl text-white"></i>
                </button>
              </div>

              <span class="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded">
                <%= typeof formatDuration==='function' ? formatDuration(video.duration) : (video.duration ?
                  Math.floor(video.duration / 60) + ':' + String(Math.floor(video.duration % 60)).padStart(2, '0' )
                  : '0:00' ) %>
              </span>

              <!-- Processing Status Indicator -->
              <% if (video.processing_status && video.processing_status !== 'completed') { %>
                <div class="absolute top-2 left-2 flex items-center gap-1 bg-black/70 text-white text-xs px-2 py-1 rounded">
                  <% if (video.processing_status === 'pending') { %>
                    <i class="ti ti-clock text-yellow-400"></i>
                    <span class="text-yellow-400">Pending</span>
                  <% } else if (video.processing_status === 'processing') { %>
                    <i class="ti ti-loader animate-spin text-blue-400"></i>
                    <span class="text-blue-400 processing-status" data-video-id="<%= video.id %>">Processing</span>
                  <% } else if (video.processing_status === 'failed') { %>
                    <i class="ti ti-alert-triangle text-red-400"></i>
                    <span class="text-red-400">Failed</span>
                  <% } %>
                </div>

                <!-- Progress Bar for Processing Videos -->
                <% if (video.processing_status === 'processing') { %>
                  <div class="absolute bottom-0 left-0 right-0 bg-black/70 p-2">
                    <div class="w-full bg-gray-600 rounded-full h-1.5">
                      <div class="bg-blue-400 h-1.5 rounded-full transition-all duration-300 processing-progress"
                           data-video-id="<%= video.id %>" style="width: 0%"></div>
                    </div>
                    <div class="text-xs text-white mt-1 processing-message" data-video-id="<%= video.id %>">
                      Starting processing...
                    </div>
                  </div>
                <% } %>
              <% } else if (video.streaming_ready_path) { %>
                <div class="absolute top-2 left-2 flex items-center gap-1 bg-black/70 text-white text-xs px-2 py-1 rounded">
                  <i class="ti ti-check text-green-400"></i>
                  <span class="text-green-400">Ready</span>
                </div>
              <% } %>
            </div>

            <div class="p-3">
              <h3 class="font-medium truncate mb-0.5">
                <%= video.title %>
              </h3>
              <div class="flex items-center justify-between">
                <div class="text-xs text-gray-400 flex items-center">
                  <span>
                    <%= new Date(video.upload_date).toLocaleDateString() %>
                  </span>
                  <span class="mx-1">•</span>
                  <span>
                    <%= typeof formatFileSize==='function' ? formatFileSize(video.file_size) : (video.file_size / (1024
                      * 1024)).toFixed(1) + ' MB' %>
                  </span>
                </div>

                <div class="flex">
                  <button class="text-gray-400 hover:text-white p-1"
                    onclick="showRenameDialog('<%= video.id %>', '<%= video.title %>')">
                    <i class="ti ti-edit text-sm"></i>
                  </button>
                  <% if (!video.thumbnail_path) { %>
                  <button class="text-gray-400 hover:text-blue-400 p-1 ml-1"
                    onclick="regenerateThumbnail('<%= video.id %>')" title="Generate Thumbnail">
                    <i class="ti ti-photo text-sm"></i>
                  </button>
                  <% } %>
                  <% if (video.processing_status === 'failed') { %>
                  <button class="text-gray-400 hover:text-yellow-400 p-1 ml-1"
                    onclick="reprocessVideo('<%= video.id %>')" title="Reprocess Video">
                    <i class="ti ti-refresh text-sm"></i>
                  </button>
                  <% } %>
                  <button class="text-gray-400 hover:text-red-400 p-1 ml-1"
                    onclick="showDeleteDialog('<%= video.id %>', '<%= video.title %>')">
                    <i class="ti ti-trash text-sm"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
          <% }); %>
            <% } else { %>
              <div class="col-span-full text-center py-12">
                <div class="text-gray-500 mb-3">
                  <i class="ti ti-file-video text-6xl"></i>
                </div>
                <h3 class="text-xl font-medium text-gray-300 mb-1"><%= t('gallery.no_videos_yet') %></h3>
                <p class="text-gray-500 mb-4"><%= t('gallery.upload_first_video') %></p>
              </div>
              <% } %>
    </div>

    <div class="mt-6 flex flex-col sm:flex-row items-center justify-between gap-4">
      <p class="text-sm text-gray-400 text-center sm:text-left">Showing 1-4 of 12 videos</p>
      <div class="flex items-center gap-2 flex-wrap justify-center">
        <button class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 text-gray-400 hover:bg-dark-600 hover:text-white transition-colors">
          <i class="ti ti-chevron-left"></i>
        </button>
        <button class="w-9 h-9 flex items-center justify-center rounded-lg bg-primary text-white">
          1
        </button>
        <button
          class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 hover:bg-dark-600 text-gray-400 hover:text-white transition-colors">
          2
        </button>
        <button
          class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 hover:bg-dark-600 text-gray-400 hover:text-white transition-colors">
          3
        </button>
        <button
          class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 hover:bg-dark-600 text-gray-400 hover:text-white transition-colors">
          <i class="ti ti-chevron-right"></i>
        </button>
      </div>
    </div>


    <div id="uploadModal" class="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 hidden transition-opacity">
      <div class="flex items-center justify-center min-h-screen p-4">
        <div
          class="bg-dark-800 rounded-lg shadow-xl w-full max-w-lg border border-gray-600/50 transform transition-all opacity-0 scale-95"
          id="uploadModalContent">

          <div class="flex items-center justify-between p-4 border-b border-gray-600/50">
            <div class="flex items-center">
              <h3 class="text-lg font-medium"><%= t('gallery.upload_video') %></h3>
            </div>
            <button onclick="closeUploadModal()"
              class="rounded-full w-8 h-8 flex items-center justify-center text-gray-400 hover:text-white hover:bg-gray-700/50 transition-colors">
              <i class="ti ti-x"></i>
            </button>
          </div>
          <div class="p-6">
            <form id="videoUploadForm" enctype="multipart/form-data">
              <input type="hidden" name="_csrf" value="<%= csrfToken %>">

              <div id="uploadDropzone"
                class="border-2 border-dashed border-gray-600 hover:border-primary/70 rounded-lg p-8 text-center transition-all cursor-pointer"
                data-state="idle">
                <div class="flex flex-col items-center justify-center">

                  <div class="mb-3 transition-all" id="dropzoneIconContainer">
                    <i class="ti ti-upload text-4xl text-gray-500"></i>
                  </div>
                  <p class="text-gray-300 mb-2"><%= t('gallery.drag_drop_files') %></p>
                  <p class="text-gray-500 text-sm mb-4"><%= t('gallery.click_to_browse') %></p>
                  <div class="bg-primary hover:bg-secondary text-white px-4 py-2 rounded-lg transition-colors inline-block cursor-pointer">
                    <span><%= t('gallery.select_files') %></span>
                  </div>
                  <input type="file" name="video" accept="video/mp4,video/quicktime" class="hidden"
                    id="videoFileInput">
                  <p class="text-gray-500 text-xs mt-4"><%= t('gallery.supported_formats_short') %></p>
                </div>
              </div>

              <div id="selectedFileInfo" class="mt-4 hidden">
                <div class="bg-dark-700/50 p-4 rounded-lg border border-gray-600/50">
                  <div class="flex items-start">
                    <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-3 shrink-0">
                      <i class="ti ti-file-video text-primary"></i>
                    </div>
                    <div class="flex-1">
                      <p id="selectedFileName" class="font-medium truncate"></p>
                      <p id="selectedFileSize" class="text-xs text-gray-400 mt-1"></p>
                    </div>
                    <button type="button" id="clearFileButton" class="text-gray-400 hover:text-white ml-2">
                      <i class="ti ti-x"></i>
                    </button>
                  </div>
                </div>
              </div>

              <div class="mt-6 hidden" id="uploadProgress">
                <div class="flex justify-between text-sm mb-1.5">
                  <span id="uploadProgressFilename" class="truncate mr-2 max-w-[80%] font-medium"><%= t('gallery.uploading') %></span>
                  <span id="uploadProgressPercent" class="text-primary font-semibold">0%</span>
                </div>
                <div class="w-full bg-dark-700 rounded-full h-3 overflow-hidden border border-gray-600/50">
                  <div id="uploadProgressBar" class="bg-primary h-full rounded-full transition-all duration-300 relative"
                    style="width: 0%">
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                  </div>
                </div>
                <div class="flex items-center justify-center mt-2">
                  <div class="flex space-x-1">
                    <div class="w-2 h-2 bg-primary rounded-full animate-bounce" style="animation-delay: 0ms"></div>
                    <div class="w-2 h-2 bg-primary rounded-full animate-bounce" style="animation-delay: 150ms"></div>
                    <div class="w-2 h-2 bg-primary rounded-full animate-bounce" style="animation-delay: 300ms"></div>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="flex justify-end p-4 border-t border-gray-600/50">
            <button onclick="closeUploadModal()"
              class="px-4 py-2 bg-dark-700 hover:bg-dark-600 text-gray-300 rounded-lg transition-colors flex items-center mr-3">
              <i class="ti ti-x mr-1.5"></i>
              <span><%= t('common.cancel') %></span>
            </button>
            <button id="uploadButton"
              class="px-4 py-2 bg-primary hover:bg-secondary text-white rounded-lg transition-colors flex items-center disabled:bg-gray-600 disabled:cursor-not-allowed"
              disabled>
              <i class="ti ti-upload mr-1.5"></i>
              <span><%= t('common.upload') %></span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <div id="toast"
      class="fixed top-16 right-4 bg-dark-800 text-white px-4 py-3 rounded-lg shadow-lg z-50 hidden flex items-center">
      <i id="toast-icon" class="mr-2"></i>
      <span id="toast-message"></span>
    </div>

    <div id="gDriveModal" class="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 hidden transition-opacity">
      <div class="flex items-center justify-center min-h-screen p-4">
        <div id="gdriveModalContent"
          class="bg-dark-800 rounded-lg shadow-xl w-full max-w-md border border-gray-600/50 transform transition-all opacity-0 scale-95">

          <div class="flex items-center justify-between p-4 border-b border-gray-600/50">
            <h3 class="text-lg font-medium"><%= t('gallery.google_drive_import') %></h3>
            <button id="closeGDriveBtn"
              class="rounded-full w-8 h-8 flex items-center justify-center text-gray-400 hover:text-white hover:bg-gray-700/50 transition-colors">
              <i class="ti ti-x"></i>
            </button>
          </div>

          <div id="gdriveModalBody"></div>
        </div>
      </div>
    </div>

    <div id="videoPreviewModal" class="fixed inset-0 bg-black bg-opacity-80 z-50 hidden">
      <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-dark-800 rounded-lg shadow-xl w-full max-w-4xl">

          <div class="flex items-center justify-between p-4 border-b border-gray-600">
            <div>
              <h3 class="text-lg font-semibold video-title">Video Title</h3>
            </div>
            <div class="flex items-center gap-4">
              <button onclick="closeVideoPreviewModal()" class="text-gray-400 hover:text-white">
                <i class="ti ti-x text-xl"></i>
              </button>
            </div>
          </div>

          <div class="relative bg-black aspect-video">
            <video id="previewPlayer" class="w-full h-full" controls>
              <source src="" type="video/mp4" id="videoSource">
              Your browser does not support the video tag.
            </video>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Translation variables for JavaScript
      const translations = {
        noVideosFound: '<%= t("gallery.no_videos_search") %>',
        adjustSearch: '<%= t("gallery.adjust_search") %>',
        showingVideos: '<%= t("gallery.showing_videos") %>',
        noVideos: '<%= t("gallery.no_videos") %>',
        processing: '<%= t("gallery.processing_import") %>',
        importVideo: '<%= t("gallery.import_video") %>',
        uploading: '<%= t("gallery.uploading") %>',
        enterDriveLink: '<%= t("gallery.enter_drive_link") %>',
        makeSureShared: '<%= t("gallery.make_sure_shared") %>',
        anyoneWithLink: '<%= t("gallery.anyone_with_link") %>',
        connectGoogleDrive: '<%= t("gallery.connect_google_drive") %>',
        apiKeyNeeded: '<%= t("gallery.api_key_needed") %>',
        enterApiKey: '<%= t("gallery.enter_api_key") %>',
        getApiKey: '<%= t("gallery.get_api_key") %>',
        googleCloudConsole: '<%= t("gallery.google_cloud_console") %>',
        tutorial: '<%= t("gallery.tutorial") %>',
        saveApiKey: '<%= t("gallery.save_api_key") %>',
        importFromGoogleDrive: '<%= t("gallery.import_from_google_drive") %>'
      };

      function formatDuration(seconds) {
        if (!seconds) return '0:00';
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        if (hours > 0) {
          return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
      }
      function formatDate(dateString) {
        if (!dateString) return 'Unknown date';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      }
      function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      }
      function showToast(type, message) {
        const toast = document.getElementById('toast');
        const toastIcon = document.getElementById('toast-icon');
        const toastMessage = document.getElementById('toast-message');
        if (type === 'success') {
          toastIcon.className = 'ti ti-check text-green-400 mr-2';
          toast.classList.add('border-l-4', 'border-green-400');
          toast.classList.remove('border-l-4', 'border-red-400', 'border-yellow-400');
        } else if (type === 'error') {
          toastIcon.className = 'ti ti-x text-red-400 mr-2';
          toast.classList.add('border-l-4', 'border-red-400');
          toast.classList.remove('border-l-4', 'border-green-400', 'border-yellow-400');
        } else if (type === 'warning') {
          toastIcon.className = 'ti ti-alert-triangle text-yellow-400 mr-2';
          toast.classList.add('border-l-4', 'border-yellow-400');
          toast.classList.remove('border-l-4', 'border-green-400', 'border-red-400', 'border-blue-400');
        } else if (type === 'info') {
          toastIcon.className = 'ti ti-info-circle text-blue-400 mr-2';
          toast.classList.add('border-l-4', 'border-blue-400');
          toast.classList.remove('border-l-4', 'border-green-400', 'border-red-400', 'border-yellow-400');
        }
        toastMessage.textContent = message;
        toast.classList.remove('hidden');
        setTimeout(() => {
          toast.classList.add('hidden');
        }, 3000);
      }
      function openUploadModal() {
        try {
          const modal = document.getElementById('uploadModal');
          const modalContent = document.getElementById('uploadModalContent');

          if (!modal || !modalContent) {
            showToast('error', 'Upload modal elements not found');
            return;
          }

          modal.classList.remove('hidden');
          setTimeout(() => {
            modalContent.classList.remove('opacity-0', 'scale-95');
            modalContent.classList.add('opacity-100', 'scale-100');
          }, 10);
          document.body.classList.add('overflow-hidden');
          resetUploadForm();
        } catch (error) {
          showToast('error', 'Error opening upload modal: ' + error.message);
        }
      }

      // Show upgrade prompt for Google Drive import
      async function showGoogleDriveUpgradePrompt() {
        const planPrice = <%= quota && quota.plan ? parseFloat(quota.plan.price) || 0 : 0 %>;
        const currentPlanName = '<%= quota && quota.plan ? quota.plan.name : "Preview" %>';

        try {
          // Get eligible plans for Google Drive
          const response = await fetch('/api/plans/google-drive-eligible');
          const data = await response.json();

          let message = `Import Google Drive hanya tersedia di plan premium. Plan Anda saat ini: ${currentPlanName} (Rp. ${planPrice.toLocaleString('id-ID')}).`;

          if (data.success && data.plans && data.plans.length > 0) {
            const planNames = data.plans.map(plan => plan.name).join(', ');
            message += ` Silakan upgrade ke plan ${planNames} untuk menggunakan fitur ini.`;
          } else {
            message += ` Silakan upgrade ke plan premium untuk menggunakan fitur ini.`;
          }

          showToast('warning', message);
        } catch (error) {
          showToast('warning', `Import Google Drive hanya tersedia di plan premium. Plan Anda saat ini: ${currentPlanName} (Rp. ${planPrice.toLocaleString('id-ID')}). Silakan upgrade ke plan premium untuk menggunakan fitur ini.`);
        }

        // Redirect to plans page after a short delay
        setTimeout(() => {
          window.location.href = '/subscription/plans';
        }, 3000);
      }

      // Ensure functions are available globally
      window.openUploadModal = openUploadModal;
      window.closeUploadModal = closeUploadModal;
      window.openGDriveModal = openGDriveModal;
      window.closeGDriveModal = closeGDriveModal;
      window.showGoogleDriveUpgradePrompt = showGoogleDriveUpgradePrompt;

      function closeUploadModal() {
        const modal = document.getElementById('uploadModal');
        const modalContent = document.getElementById('uploadModalContent');
        modalContent.classList.remove('opacity-100', 'scale-100');
        modalContent.classList.add('opacity-0', 'scale-95');
        setTimeout(() => {
          modal.classList.add('hidden');
          document.body.classList.remove('overflow-hidden');
        }, 200);
        resetUploadForm();
      }
      function resetUploadForm() {
        // console.log('🔄 Resetting upload form'); // Removed for production
        document.getElementById('videoUploadForm').reset();
        document.getElementById('selectedFileInfo').classList.add('hidden');
        document.getElementById('uploadDropzone').classList.remove('hidden');
        document.getElementById('uploadProgress').classList.add('hidden');
        document.getElementById('uploadButton').disabled = true;

        // Reset progress bar
        const progressBar = document.getElementById('uploadProgressBar');
        const progressPercent = document.getElementById('uploadProgressPercent');
        const uploadButton = document.getElementById('uploadButton');

        progressBar.style.width = '0%';
        progressBar.style.transition = '';
        progressBar.classList.remove('bg-green-500', 'animate-pulse');
        progressBar.classList.add('bg-primary');

        progressPercent.textContent = '0%';
        progressPercent.classList.remove('text-yellow-400', 'text-primary');
        progressPercent.classList.add('text-primary');

        // Reset upload button
        uploadButton.innerHTML = `
          <i class="ti ti-upload mr-1.5"></i>
          <span><%= t('common.upload') %></span>
        `;

        // Reset dropzone
        const dropzone = document.getElementById('uploadDropzone');
        dropzone.setAttribute('data-state', 'idle');
        dropzone.classList.remove('border-primary', 'bg-primary/5');
        dropzone.classList.add('border-gray-600');
        document.querySelector('#dropzoneIconContainer i').className = 'ti ti-upload text-4xl text-gray-500';
      }
      function openGDriveModal() {
        const modal = document.getElementById('gDriveModal');
        const modalContent = document.getElementById('gdriveModalContent');
        if (!modal || !modalContent) {
          // console.error('GDrive modal elements not found'); // Cleaned for production
          return;
        }
        modal.classList.remove('hidden');
        void modal.offsetWidth;
        setTimeout(() => {
          modalContent.classList.remove('opacity-0', 'scale-95');
          modalContent.classList.add('opacity-100', 'scale-100');
        }, 10);
        document.body.classList.add('overflow-hidden');
        checkGDriveAPIKey();
        document.getElementById('closeGDriveBtn').addEventListener('click', closeGDriveModal);
      }
      function closeGDriveModal() {
        const modal = document.getElementById('gDriveModal');
        const modalContent = document.getElementById('gdriveModalContent');
        if (!modal || !modalContent) {
          // console.error('GDrive modal elements not found for closing'); // Cleaned for production
          return;
        }
        // console.log('Closing GDrive modal'); // Cleaned for production
        modalContent.classList.remove('opacity-100', 'scale-100');
        modalContent.classList.add('opacity-0', 'scale-95');
        setTimeout(() => {
          modal.classList.add('hidden');
          document.body.classList.remove('overflow-hidden');
        }, 200);
      }
      async function checkGDriveAPIKey() {
        try {
          const response = await fetch('/api/settings/gdrive-status');
          const data = await response.json();
          const modalContent = document.getElementById('gdriveModalBody');
          if (data.hasApiKey) {
            modalContent.innerHTML = `
        <div class="p-6">

          <div class="text-center mb-5">
            <div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-3">
              <i class="ti ti-brand-google-drive text-3xl text-primary"></i>
            </div>
            <h4 class="text-lg font-medium mb-1">${translations.importFromGoogleDrive}</h4>
            <p class="text-gray-400 text-sm">${translations.enterDriveLink}</p>
          </div>

          <div class="relative mb-2">
            <input
              type="text"
              id="gdrive-link"
              class="bg-dark-700 text-white pl-10 pr-4 py-2.5 rounded-lg block w-full focus:outline-none focus:ring-1 focus:ring-primary border border-gray-600"
              placeholder="${translations.enterDriveLink}"
            >
            <i class="ti ti-link absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
          </div>
          <p class="text-xs text-gray-500 mb-4">${translations.makeSureShared} <strong>${translations.anyoneWithLink}</strong></p>

          <button id="import-drive-button" class="w-full bg-primary hover:bg-primary/90 text-white py-2.5 px-4 rounded-lg font-medium transition-colors flex items-center justify-center">
            <i class="ti ti-download mr-2"></i>
            <span>${translations.importVideo}</span>
          </button>
        </div>
      `;
            document.getElementById('import-drive-button').addEventListener('click', async function () {
              const link = document.getElementById('gdrive-link').value;
              if (!link) {
                showToast('error', 'Please enter a Google Drive link');
                return;
              }
              this.disabled = true;
              this.innerHTML = `
          <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          ${translations.processing}
        `;
              try {
                const response = await fetch('/api/videos/import-drive', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': document.querySelector('input[name="_csrf"]').value
                  },
                  body: JSON.stringify({ driveUrl: link })
                });
                const result = await response.json();
                if (result.success) {
                  showImportProgressModal(result.jobId);
                  closeGDriveModal();
                } else {
                  showToast('error', result.error || 'Failed to import video');
                  this.disabled = false;
                  this.innerHTML = `<i class="ti ti-download mr-2"></i><span>${translations.importVideo}</span>`;
                }
              } catch (error) {
                showToast('error', 'An error occurred while importing the video');
                this.disabled = false;
                this.innerHTML = `<i class="ti ti-download mr-2"></i><span>${translations.importVideo}</span>`;
              }
            });
          } else {
            modalContent.innerHTML = `
        <div class="p-6">

          <div class="text-center mb-5">
            <div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-3">
              <i class="ti ti-brand-google-drive text-3xl text-primary"></i>
            </div>
            <h4 class="text-lg font-medium mb-1">${translations.connectGoogleDrive}</h4>
            <p class="text-gray-400 text-sm">${translations.apiKeyNeeded}</p>
          </div>

          <div class="relative mb-2">
            <input
              type="password"
              id="gdrive-api-key-setup"
              class="bg-dark-700 text-white pl-10 pr-10 py-2.5 rounded-lg block w-full focus:outline-none focus:ring-1 focus:ring-primary border border-gray-600"
              placeholder="${translations.enterApiKey}"
            >
            <i class="ti ti-key absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
            <button type="button" id="toggle-api-key-visibility" class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-white">
              <i class="ti ti-eye"></i>
            </button>
          </div>
          <p class="text-xs text-gray-500 mb-4 flex items-center justify-between">
            <span>${translations.getApiKey} <a href="https://console.cloud.google.com/" class="text-primary hover:underline" target="_blank">${translations.googleCloudConsole}</a>.</span>
          </p>

          <button id="save-api-key-button" class="w-full bg-primary hover:bg-primary/90 text-white py-2.5 px-4 rounded-lg font-medium transition-colors flex items-center justify-center">
            <i class="ti ti-device-floppy mr-2"></i>
            <span>${translations.saveApiKey}</span>
          </button>
        </div>
      `;
            document.getElementById('toggle-api-key-visibility').addEventListener('click', function () {
              const apiKeyField = document.getElementById('gdrive-api-key-setup');
              const toggleBtn = this.querySelector('i');
              if (apiKeyField.type === 'password') {
                apiKeyField.type = 'text';
                toggleBtn.classList.remove('ti-eye');
                toggleBtn.classList.add('ti-eye-off');
              } else {
                apiKeyField.type = 'password';
                toggleBtn.classList.remove('ti-eye-off');
                toggleBtn.classList.add('ti-eye');
              }
            });
            document.getElementById('save-api-key-button').addEventListener('click', async function () {
              const apiKey = document.getElementById('gdrive-api-key-setup').value;
              if (!apiKey) {
                showToast('error', 'Please enter a Google Drive API key');
                return;
              }
              this.disabled = true;
              this.innerHTML = `
          <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Saving...
        `;
              try {
                const response = await fetch('/api/settings/gdrive-api-key', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': document.querySelector('input[name="_csrf"]').value
                  },
                  body: JSON.stringify({ apiKey })
                });
                const result = await response.json();
                if (result.success) {
                  showToast('success', 'API key saved successfully');
                  setTimeout(() => {
                    closeGDriveModal();
                    setTimeout(() => openGDriveModal(), 300);
                  }, 1000);
                } else {
                  showToast('error', result.error || 'Failed to save API key');
                  this.disabled = false;
                  this.innerHTML = `<i class="ti ti-device-floppy mr-2"></i><span>${translations.saveApiKey}</span>`;
                }
              } catch (error) {
                showToast('error', 'An error occurred while saving the API key');
                this.disabled = false;
                this.innerHTML = `<i class="ti ti-device-floppy mr-2"></i><span>${translations.saveApiKey}</span>`;
              }
            });
          }
        } catch (error) {
          // console.error('Error checking Google Drive API key status:', error); // Cleaned for production
          showToast('error', 'Failed to check API key status');
        }
      }
      function openVideoPreviewModal(title, videoSrc, details) {
        document.querySelector('#videoPreviewModal .video-title').textContent = title;
        const videoSource = document.getElementById('videoSource');
        videoSource.src = videoSrc;
        const videoPlayer = document.getElementById('previewPlayer');
        videoPlayer.load();
        document.getElementById('videoPreviewModal').classList.remove('hidden');
        document.body.style.overflow = 'hidden';
      }
      function closeVideoPreviewModal() {
        const videoPlayer = document.getElementById('previewPlayer');
        videoPlayer.pause();
        document.getElementById('videoPreviewModal').classList.add('hidden');
        document.body.style.overflow = 'auto';
      }

      function playVideo(videoId, videoTitle) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm transition-opacity';
        modal.id = 'video-player-modal';
        const displayTitle = videoTitle || 'Now Playing';
        modal.innerHTML = `
        <div class="relative w-full max-w-4xl mx-auto">
          <div class="bg-dark-800 rounded-lg overflow-hidden shadow-xl">

            <div class="flex items-center justify-between p-4 border-b border-gray-600">
              <div class="flex items-center">
                <h3 class="text-lg font-medium">${displayTitle}</h3>
              </div>
              <button id="close-player-btn" class="rounded-full w-8 h-8 flex items-center justify-center text-gray-400 hover:text-white hover:bg-gray-700 transition-colors">
                <i class="ti ti-x"></i>
              </button>
            </div>

            <div class="bg-dark-700 flex justify-center" style="height: 53vh; max-height: 600px;">
              <video
                id="native-player"
                class="h-full max-h-full max-w-full object-contain"
                controls
                preload="auto"
                autoplay
              >
                <source src="/stream/${videoId}" type="video/mp4">
                Your browser does not support HTML5 video.
              </video>
            </div>
          </div>
        </div>
      `;
        document.body.appendChild(modal);
        document.body.classList.add('overflow-hidden');
        document.getElementById('close-player-btn').addEventListener('click', function () {
          const videoElement = document.getElementById('native-player');
          if (videoElement) videoElement.pause();
          document.getElementById('video-player-modal').remove();
          document.body.classList.remove('overflow-hidden');
        });
        document.addEventListener('keydown', function escapeHandler(e) {
          if (e.key === 'Escape') {
            const videoElement = document.getElementById('native-player');
            if (videoElement) videoElement.pause();
            document.getElementById('video-player-modal').remove();
            document.body.classList.remove('overflow-hidden');
            document.removeEventListener('keydown', escapeHandler);
          }
        });
        modal.addEventListener('click', function (e) {
          if (e.target === modal) {
            const videoElement = document.getElementById('native-player');
            if (videoElement) videoElement.pause();
            document.getElementById('video-player-modal').remove();
            document.body.classList.remove('overflow-hidden');
          }
        });
      }

      function deleteVideo(videoId, videoTitle) {
        if (confirm(`Are you sure you want to delete "${videoTitle}"?`)) {
          fetch(`/api/videos/${videoId}`, {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json'
            }
          })
            .then(response => response.json())
            .then(data => {
              if (data.success) {
                showToast('success', 'Video deleted successfully');
                setTimeout(() => window.location.reload(), 1000);
              } else {
                showToast('error', data.error || 'Failed to delete video');
              }
            })
            .catch(error => {
              // console.error('Error:', error); // Cleaned for production
              showToast('error', 'An error occurred while deleting the video');
            });
        }
      }

      async function regenerateThumbnail(videoId) {
        try {
          showToast('info', 'Generating thumbnail...');

          const response = await fetch(`/api/videos/${videoId}/regenerate-thumbnail`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-Token': document.querySelector('input[name="_csrf"]').value
            }
          });

          const result = await response.json();

          if (result.success) {
            showToast('success', 'Thumbnail generated successfully!');
            // Reload the page to show the new thumbnail
            setTimeout(() => window.location.reload(), 1000);
          } else {
            showToast('error', result.error || 'Failed to generate thumbnail');
          }
        } catch (error) {
          // console.error('Error regenerating thumbnail:', error); // Cleaned for production
          showToast('error', 'An error occurred while generating thumbnail');
        }
      }

      function renameVideo(videoId, currentTitle) {
        const newTitle = prompt('Enter a new title for the video:', currentTitle);
        if (newTitle && newTitle !== currentTitle) {
          fetch(`/api/videos/${videoId}/rename`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ title: newTitle })
          })
            .then(response => response.json())
            .then(data => {
              if (data.success) {
                showToast('success', 'Video renamed successfully');
                setTimeout(() => window.location.reload(), 1000);
              } else {
                showToast('error', data.error || 'Failed to rename video');
              }
            })
            .catch(error => {
              // console.error('Error:', error); // Cleaned for production
              showToast('error', 'An error occurred while renaming the video');
            });
        }
      }
      // Upload functionality will be initialized in the main DOMContentLoaded event listener


      function createModalDialog(options) {
        const dialog = document.createElement('div');
        dialog.id = 'custom-modal';
        dialog.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm transition-all duration-300';
        const themes = {
          'info': {
            icon: options.icon || 'ti-info-circle',
            color: 'text-primary',
            bg: 'bg-primary/10',
            border: 'border-gray-600/50',
            button: 'bg-primary hover:bg-secondary',
            buttonIcon: 'ti-check'
          },
          'danger': {
            icon: options.icon || 'ti-alert-triangle',
            color: 'text-red-400',
            bg: 'bg-red-500/10',
            border: 'border-gray-600/50',
            button: 'bg-red-500 hover:bg-red-600',
            buttonIcon: 'ti-trash'
          },
          'warning': {
            icon: options.icon || 'ti-alert-triangle',
            color: 'text-yellow-400',
            bg: 'bg-yellow-500/10',
            border: 'border-yellow-500/50',
            button: 'bg-yellow-500 hover:bg-yellow-600',
            buttonIcon: 'ti-alert-circle'
          },
          'success': {
            icon: options.icon || 'ti-check-circle',
            color: 'text-green-400',
            bg: 'bg-green-500/10',
            border: 'border-green-500/50',
            button: 'bg-green-500 hover:bg-green-600',
            buttonIcon: 'ti-check'
          }
        };
        const theme = themes[options.type || 'info'];
        const confirmClass = options.confirmClass || theme.button;
        const buttonIcon = theme.buttonIcon || 'ti-check';
        const cancelText = options.cancelText || 'Cancel';
        const confirmText = options.confirmText || 'Confirm';
        const inputValue = options.inputValue || '';
        const videoTitle = options.videoTitle || 'Now Playing';

        dialog.innerHTML = `
      <div class="transform transition-all duration-300 opacity-0 scale-95 modal-content max-w-md w-full mx-4">
        <div class="bg-dark-800 rounded-lg shadow-xl border ${theme.border} overflow-hidden">

          <div class="px-6 py-5 flex items-center">
            <div class="w-12 h-12 rounded-full ${theme.bg} flex items-center justify-center mr-4 shrink-0">
              <i class="ti ${theme.icon} ${theme.color} text-2xl"></i>
            </div>
            <div class="flex-1">
              <h3 class="text-lg font-medium text-white">${options.title}</h3>
              <p class="text-gray-400 text-sm mt-1">${options.message}</p>
            </div>
          </div>

          ${options.hasInput ? `
          <div class="px-6 pb-4">
            <input
              type="text"
              id="modal-input"
              class="bg-dark-900 text-white px-4 py-3 rounded-lg block w-full focus:outline-none focus:ring-1 focus:ring-primary border border-gray-600"
              value="${inputValue}"
              placeholder="Enter new title..."
              autofocus
            >
          </div>
          ` : ''}

          <div class="px-6 py-4 flex justify-end space-x-3 border-t border-gray-600/50">
            <button id="modal-cancel-btn" class="px-4 py-2.5 bg-dark-700 hover:bg-dark-600 text-gray-300 rounded-lg transition-colors text-sm font-medium flex items-center">
              <i class="ti ti-x mr-1.5"></i>
              ${cancelText}
            </button>
            <button id="modal-confirm-btn" class="${confirmClass} px-4 py-2.5 text-white rounded-lg transition-colors text-sm font-medium flex items-center">
              <i class="ti ${buttonIcon} mr-1.5"></i>
              ${confirmText}
            </button>
          </div>
        </div>
      </div>
    `;
        document.body.appendChild(dialog);
        document.body.classList.add('overflow-hidden');
        setTimeout(() => {
          const modalContent = dialog.querySelector('.modal-content');
          if (modalContent) {
            modalContent.classList.replace('opacity-0', 'opacity-100');
            modalContent.classList.replace('scale-95', 'scale-100');
          }
        }, 10);
        return new Promise((resolve) => {
          document.getElementById('modal-confirm-btn').addEventListener('click', () => {
            const inputValue = options.hasInput ? document.getElementById('modal-input').value : null;
            resolve({ confirmed: true, value: inputValue, closeNow: false });
          });
          document.getElementById('modal-cancel-btn').addEventListener('click', () => {
            closeModalWithAnimation();
          });
          document.addEventListener('keydown', function escapeHandler(e) {
            if (e.key === 'Escape') {
              closeModalWithAnimation();
              document.removeEventListener('keydown', escapeHandler);
            }
          });
          dialog.addEventListener('click', function (e) {
            if (e.target === dialog) {
              closeModalWithAnimation();
            }
          });
          if (options.hasInput) {
            const input = document.getElementById('modal-input');
            input.focus();
            input.select();
            input.addEventListener('keydown', function (e) {
              if (e.key === 'Enter') {
                e.preventDefault();
                closeModalWithAnimation(true, input.value);
              }
            });
          }
          function closeModalWithAnimation(confirmed = false, value = null) {
            const modalContent = dialog.querySelector('.modal-content');
            if (modalContent) {
              modalContent.classList.replace('opacity-100', 'opacity-0');
              modalContent.classList.replace('scale-100', 'scale-95');
            }
            setTimeout(() => {
              document.body.classList.remove('overflow-hidden');
              dialog.remove();
              resolve({ confirmed, value, closeNow: true });
            }, 200);
          }
        });
      }
      async function showRenameDialog(videoId, currentTitle) {
        const result = await createModalDialog({
          type: 'info',
          icon: 'ti-pencil',
          title: 'Rename Video',
          message: 'Enter a new title for your video:',
          hasInput: true,
          inputValue: currentTitle,
          confirmText: 'Save',
          cancelText: 'Cancel'
        });
        if (result.confirmed && result.value && result.value !== currentTitle) {
          setModalButtonState('modal-confirm-btn', 'loading', 'Saving...');
          const headerEl = document.querySelector('#custom-modal .flex.items-center');
          const originalHeader = headerEl.innerHTML;
          try {
            const response = await fetch(`/api/videos/${videoId}/rename`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({ title: result.value })
            });
            const data = await response.json();
            if (data.success) {
              setModalButtonState('modal-confirm-btn', 'success', 'Saved!');
              headerEl.innerHTML = `
            <div class="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mr-4 shrink-0 animate-pulse">
              <i class="ti ti-check text-primary text-2xl"></i>
            </div>
            <div class="flex-1">
              <h3 class="text-lg font-medium text-white">Rename Successful</h3>
              <p class="text-gray-400 text-sm mt-1">Your video has been renamed</p>
            </div>`;
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            } else {
              setModalButtonState('modal-confirm-btn', 'normal', 'Try Again');
              showToast('error', data.error || 'Failed to rename video');
            }
          } catch (error) {
            setModalButtonState('modal-confirm-btn', 'normal', 'Try Again');
            showToast('error', 'An error occurred while renaming the video');
          }
          return false;
        }
        return true;
      }
      async function showDeleteDialog(videoId, videoTitle) {
        const result = await createModalDialog({
          type: 'danger',
          icon: 'ti-alert-triangle',
          title: 'Delete Video',
          message: `Are you sure you want to delete "${videoTitle}"? This action cannot be undone.`,
          confirmText: 'Delete',
          cancelText: 'Cancel',
          confirmClass: 'bg-red-500 hover:bg-red-600'
        });
        if (result.confirmed) {
          try {
            const response = await fetch(`/api/videos/${videoId}`, {
              method: 'DELETE'
            });
            const data = await response.json();
            if (data.success) {
              showToast('success', 'Video deleted successfully');
              setTimeout(() => window.location.reload(), 1000);
            } else {
              showToast('error', data.error || 'Failed to delete video');
            }
          } catch (error) {
            showToast('error', 'An error occurred while deleting the video');
          }
        }
      }
      function setModalButtonState(buttonId, state, text) {
        const button = document.getElementById(buttonId);
        if (state === 'loading') {
          button.disabled = true;
          button.innerHTML = `<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg> ${text || 'Processing...'}`;
        } else if (state === 'success') {
          button.disabled = false;
          button.innerHTML = `<i class="ti ti-check mr-1.5"></i> ${text || 'Done'}`;
          button.classList.remove('bg-primary', 'hover:bg-secondary');
          button.classList.add('bg-primary', 'hover:bg-secondary');
        } else {
          button.disabled = false;
          button.innerHTML = `<i class="ti ti-check mr-1.5"></i> ${text || 'Save'}`;
        }
      }
      // Dropzone event listeners will be initialized in the main DOMContentLoaded event listener
      function showImportProgressModal(jobId) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm';
        modal.id = 'import-progress-modal';
        modal.innerHTML = `
      <div class="bg-dark-800 rounded-lg p-6 w-full max-w-md">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium">Importing Video</h3>
          <button id="close-import-progress" class="text-gray-400 hover:text-white">
            <i class="ti ti-x"></i>
          </button>
        </div>
        <div class="text-center mb-4">
          <div class="w-16 h-16 mb-4 rounded-full bg-primary/10 mx-auto flex items-center justify-center">
            <i id="progress-icon" class="ti ti-download text-primary text-2xl"></i>
          </div>
          <p id="progress-status" class="text-sm text-gray-300 mb-2">Starting download...</p>
        </div>
        <div class="w-full bg-dark-700 rounded-full h-2 mb-4">
          <div id="progress-bar" class="bg-primary h-2 rounded-full transition-all" style="width: 0%"></div>
        </div>
        <p class="text-xs text-gray-500">
          <i class="ti ti-info-circle mr-1"></i>
          You can close this window and the import will continue in the background
        </p>
      </div>
    `;
        document.body.appendChild(modal);
        document.getElementById('close-import-progress').addEventListener('click', function () {
          document.getElementById('import-progress-modal').remove();
        });
        monitorImportProgress(jobId);
      }
      function monitorImportProgress(jobId) {
        const progressInterval = setInterval(async () => {
          try {
            const response = await fetch(`/api/videos/import-status/${jobId}`);
            if (!response.ok) {
              clearInterval(progressInterval);
              return;
            }
            const data = await response.json();
            if (!data.success) {
              clearInterval(progressInterval);
              return;
            }
            const status = data.status;
            const progressBar = document.getElementById('progress-bar');
            const progressStatus = document.getElementById('progress-status');
            const progressIcon = document.getElementById('progress-icon');
            if (progressBar && progressStatus) {
              progressBar.style.width = `${status.progress}%`;
              progressStatus.textContent = status.message;
              if (status.status === 'downloading') {
                progressIcon.className = 'ti ti-download text-primary text-2xl';
              } else if (status.status === 'processing') {
                progressIcon.className = 'ti ti-settings text-primary text-2xl animate-spin';
              } else if (status.status === 'complete') {
                progressIcon.className = 'ti ti-check text-green-400 text-2xl';
                progressBar.classList.remove('bg-primary');
                progressBar.classList.add('bg-green-500');
                showToast('success', 'Video imported successfully');
                setTimeout(() => {
                  window.location.reload();
                }, 2000);
                clearInterval(progressInterval);
              } else if (status.status === 'failed') {
                progressIcon.className = 'ti ti-alert-triangle text-red-400 text-2xl';
                progressBar.classList.remove('bg-primary');
                progressBar.classList.add('bg-red-500');
                showToast('error', status.message || 'Failed to import video');
                clearInterval(progressInterval);
              }
            }
          } catch (error) {
            // console.error('Error monitoring import progress:', error); // Cleaned for production
          }
        }, 1000);
      }
      document.addEventListener('DOMContentLoaded', function () {
        // Initialize upload functionality
        function formatFileSize(bytes) {
          if (bytes === 0) return '0 Bytes';
          const k = 1024;
          const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
          const i = Math.floor(Math.log(bytes) / Math.log(k));
          return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        const videoFileInput = document.getElementById('videoFileInput');
        const selectedFileInfo = document.getElementById('selectedFileInfo');
        const selectedFileName = document.getElementById('selectedFileName');
        const selectedFileSize = document.getElementById('selectedFileSize');
        const clearFileButton = document.getElementById('clearFileButton');
        const uploadDropzone = document.getElementById('uploadDropzone');
        const uploadButton = document.getElementById('uploadButton');
        const uploadProgress = document.getElementById('uploadProgress');
        const uploadProgressBar = document.getElementById('uploadProgressBar');
        const uploadProgressPercent = document.getElementById('uploadProgressPercent');
        const uploadProgressFilename = document.getElementById('uploadProgressFilename');

        // Upload form event listeners
        if (videoFileInput) {
          videoFileInput.addEventListener('change', function () {
            if (this.files.length > 0) {
              const file = this.files[0];
              selectedFileName.textContent = file.name;
              selectedFileSize.textContent = formatFileSize(file.size);
              uploadDropzone.classList.add('hidden');
              selectedFileInfo.classList.remove('hidden');
              uploadButton.disabled = false;
            }
          });
        }

        if (uploadButton) {
          uploadButton.addEventListener('click', function () {
            if (videoFileInput.files.length === 0) {
              return;
            }
            const file = videoFileInput.files[0];

            // Hide upload form and show progress
            uploadDropzone.classList.add('hidden');
            selectedFileInfo.classList.add('hidden');
            uploadProgress.classList.remove('hidden');
            uploadProgressFilename.textContent = file.name;

            // Update upload button to show loading state
            uploadButton.disabled = true;
            uploadButton.innerHTML = `
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span>Uploading...</span>
            `;

            // Determine upload method based on file size
            const CHUNK_THRESHOLD = 50 * 1024 * 1024; // 50MB threshold for chunked upload

            if (file.size > CHUNK_THRESHOLD) {
              // Use chunked upload for large files
              startChunkedUpload(file);
            } else {
              // Use regular upload for small files
              startRegularUpload(file);
            }
          });
        }

        // Dropzone event listeners
        if (uploadDropzone && videoFileInput) {
          uploadDropzone.addEventListener('click', () => {
            videoFileInput.click();
          });

          uploadDropzone.addEventListener('dragenter', (e) => {
            e.preventDefault();
            e.stopPropagation();
            uploadDropzone.setAttribute('data-state', 'active');
            uploadDropzone.classList.remove('border-gray-600');
            uploadDropzone.classList.add('border-primary', 'bg-primary/5');
            document.querySelector('#dropzoneIconContainer i').className = 'ti ti-upload text-4xl text-primary';
          });

          uploadDropzone.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.stopPropagation();
            if (uploadDropzone.getAttribute('data-state') !== 'active') {
              uploadDropzone.setAttribute('data-state', 'active');
              uploadDropzone.classList.remove('border-gray-600');
              uploadDropzone.classList.add('border-primary', 'bg-primary/5');
              document.querySelector('#dropzoneIconContainer i').className = 'ti ti-upload text-4xl text-primary';
            }
          });

          uploadDropzone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            e.stopPropagation();
            if (e.currentTarget === e.target) {
              uploadDropzone.setAttribute('data-state', 'idle');
              uploadDropzone.classList.remove('border-primary', 'bg-primary/5');
              uploadDropzone.classList.add('border-gray-600');
              document.querySelector('#dropzoneIconContainer i').className = 'ti ti-upload text-4xl text-gray-500';
            }
          });

          uploadDropzone.addEventListener('drop', (e) => {
            e.preventDefault();
            e.stopPropagation();
            uploadDropzone.setAttribute('data-state', 'idle');
            uploadDropzone.classList.remove('border-primary', 'bg-primary/5');
            uploadDropzone.classList.add('border-gray-600');
            const dt = e.dataTransfer;
            const files = dt.files;
            if (files.length > 0) {
              const file = files[0];
              const validTypes = ['video/mp4', 'video/quicktime'];
              if (validTypes.includes(file.type)) {
                videoFileInput.files = files;
                const event = new Event('change', { bubbles: true });
                videoFileInput.dispatchEvent(event);
              } else {
                showToast('error', 'Please upload a valid video file (MP4, MOV only for optimal performance)');
              }
            }
          });
        }

        if (clearFileButton) {
          clearFileButton.addEventListener('click', function () {
            resetUploadForm();
          });
        }

        // Regular upload function for small files
        function startRegularUpload(file) {
          const formData = new FormData();
          formData.append('video', file);

          const xhr = new XMLHttpRequest();

          // Initialize progress tracking
          let progressStarted = false;
          let simulatedProgress = 0;
          let progressInterval = null;

          // Start simulated progress for immediate feedback
          const startSimulatedProgress = () => {
            if (progressInterval) clearInterval(progressInterval);

            progressInterval = setInterval(() => {
              if (!progressStarted && simulatedProgress < 10) {
                simulatedProgress += 2;
                uploadProgressBar.style.width = simulatedProgress + '%';
                uploadProgressPercent.textContent = simulatedProgress + '%';
              }
            }, 100);
          };

          // Start simulated progress immediately
          startSimulatedProgress();

          // Upload progress handler
          xhr.upload.addEventListener('progress', (event) => {
            // Stop simulated progress when real progress starts
            if (!progressStarted) {
              progressStarted = true;
              if (progressInterval) {
                clearInterval(progressInterval);
                progressInterval = null;
              }
            }

            if (event.lengthComputable) {
              const percentComplete = Math.round((event.loaded / event.total) * 100);

              // Force update progress bar with immediate DOM manipulation
              requestAnimationFrame(() => {
                try {
                  // Update progress bar width
                  uploadProgressBar.style.width = percentComplete + '%';
                  uploadProgressBar.style.transition = 'width 0.3s ease';

                  // Update percentage text
                  uploadProgressPercent.textContent = percentComplete + '%';

                  // Add visual feedback for progress
                  if (percentComplete > 0 && percentComplete < 100) {
                    uploadProgressBar.classList.add('animate-pulse');
                    uploadProgressPercent.classList.remove('text-yellow-400');
                    uploadProgressPercent.classList.add('text-primary');
                  }

                  if (percentComplete >= 100) {
                    uploadProgressBar.classList.remove('animate-pulse');
                    uploadProgressPercent.textContent = 'Processing...';
                    uploadProgressPercent.classList.remove('text-primary');
                    uploadProgressPercent.classList.add('text-yellow-400');
                  }
                } catch (error) {
                  console.error('Error updating progress UI:', error);
                }
              });
            }
          });

          // Upload completion handler
          xhr.addEventListener('load', function () {
            // Clean up progress interval
            if (progressInterval) {
              clearInterval(progressInterval);
              progressInterval = null;
            }

            if (xhr.status === 200) {
              try {
                const response = JSON.parse(xhr.responseText);
                if (response.success) {
                  // Show completion state
                  uploadProgressBar.style.width = '100%';
                  uploadProgressPercent.textContent = 'Complete!';
                  uploadProgressBar.classList.remove('bg-primary');
                  uploadProgressBar.classList.add('bg-green-500');

                  showToast('success', 'Video uploaded successfully');

                  // Update storage display if we're on dashboard
                  if (typeof updateQuotaDisplay === 'function') {
                    updateQuotaDisplay();
                  }

                  // Trigger storage update event for other pages
                  window.dispatchEvent(new CustomEvent('storageUpdated'));

                  setTimeout(() => {
                    window.location.reload();
                  }, 1000);
                } else {
                  showToast('error', response.error || 'Upload failed');
                  resetUploadForm();
                }
              } catch (error) {
                showToast('error', 'Failed to parse server response');
                resetUploadForm();
              }
            } else {
              // Handle error responses with detailed messages
              try {
                const errorResponse = JSON.parse(xhr.responseText);
                if (xhr.status === 413 && errorResponse.details && errorResponse.details.message) {
                  // Storage quota exceeded - show user-friendly message
                  showToast('error', errorResponse.details.message);
                } else if (errorResponse.error) {
                  // Other errors with detailed messages
                  showToast('error', errorResponse.error);
                } else {
                  // Fallback to generic message
                  showToast('error', 'Upload failed with status: ' + xhr.status);
                }
              } catch (parseError) {
                // If response is not JSON, show generic error
                if (xhr.status === 413) {
                  showToast('error', 'Upload failed: File too large or storage quota exceeded');
                } else {
                  showToast('error', 'Upload failed with status: ' + xhr.status);
                }
              }
              resetUploadForm();
            }
          });

          xhr.addEventListener('error', function () {
            // Clean up progress interval
            if (progressInterval) {
              clearInterval(progressInterval);
              progressInterval = null;
            }
            showToast('error', 'An error occurred during upload');
            resetUploadForm();
          });

          xhr.addEventListener('abort', function () {
            // Clean up progress interval
            if (progressInterval) {
              clearInterval(progressInterval);
              progressInterval = null;
            }
            showToast('warning', 'Upload aborted');
            resetUploadForm();
          });

          // Start the upload
          xhr.open('POST', '/api/videos/upload', true);
          xhr.send(formData);

          // Fallback progress for very fast uploads
          setTimeout(() => {
            if (!progressStarted) {
              let fallbackProgress = 10;
              const fallbackInterval = setInterval(() => {
                if (!progressStarted && fallbackProgress < 90) {
                  fallbackProgress += 10;
                  uploadProgressBar.style.width = fallbackProgress + '%';
                  uploadProgressPercent.textContent = fallbackProgress + '%';
                } else {
                  clearInterval(fallbackInterval);
                }
              }, 200);
            }
          }, 500);
        }

        // Chunked upload function for large files
        async function startChunkedUpload(file) {
          const CHUNK_SIZE = 50 * 1024 * 1024; // 50MB chunks
          const totalChunks = Math.ceil(file.size / CHUNK_SIZE);

          try {
            // Initialize chunked upload
            uploadProgressPercent.textContent = 'Initializing...';

            const initResponse = await fetch('/api/videos/upload/init', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                filename: file.name,
                fileSize: file.size,
                totalChunks: totalChunks
              })
            });

            const initData = await initResponse.json();
            if (!initData.success) {
              throw new Error(initData.error || 'Failed to initialize upload');
            }

            const uploadId = initData.uploadId;
            let uploadedChunks = 0;

            // Upload chunks sequentially
            for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
              const start = chunkIndex * CHUNK_SIZE;
              const end = Math.min(start + CHUNK_SIZE, file.size);
              const chunk = file.slice(start, end);

              // Update progress
              const chunkProgress = Math.round((chunkIndex / totalChunks) * 100);
              uploadProgressBar.style.width = chunkProgress + '%';
              uploadProgressPercent.textContent = `Uploading chunk ${chunkIndex + 1}/${totalChunks} (${chunkProgress}%)`;

              // Upload chunk with retry logic
              let retryCount = 0;
              const maxRetries = 3;

              while (retryCount <= maxRetries) {
                try {
                  const chunkFormData = new FormData();
                  chunkFormData.append('chunk', chunk);
                  chunkFormData.append('uploadId', uploadId);
                  chunkFormData.append('chunkIndex', chunkIndex.toString());

                  const chunkResponse = await fetch('/api/videos/upload/chunk', {
                    method: 'POST',
                    body: chunkFormData
                  });

                  const chunkData = await chunkResponse.json();
                  if (!chunkData.success) {
                    throw new Error(chunkData.error || 'Failed to upload chunk');
                  }

                  uploadedChunks++;
                  break; // Success, exit retry loop

                } catch (error) {
                  retryCount++;
                  if (retryCount > maxRetries) {
                    throw new Error(`Failed to upload chunk ${chunkIndex + 1} after ${maxRetries} retries: ${error.message}`);
                  }

                  // Wait before retry
                  await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
                  uploadProgressPercent.textContent = `Retrying chunk ${chunkIndex + 1}/${totalChunks} (attempt ${retryCount + 1})`;
                }
              }
            }

            // Finalize upload
            uploadProgressBar.style.width = '100%';
            uploadProgressPercent.textContent = 'Finalizing upload...';

            const finalizeResponse = await fetch('/api/videos/upload/finalize', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                uploadId: uploadId
              })
            });

            const finalizeData = await finalizeResponse.json();
            if (!finalizeData.success) {
              throw new Error(finalizeData.error || 'Failed to finalize upload');
            }

            // Show completion state
            uploadProgressBar.classList.remove('bg-primary');
            uploadProgressBar.classList.add('bg-green-500');
            uploadProgressPercent.textContent = 'Complete!';

            showToast('success', 'Large file uploaded successfully using chunked upload');

            // Update storage display if we're on dashboard
            if (typeof updateQuotaDisplay === 'function') {
              updateQuotaDisplay();
            }

            // Trigger storage update event for other pages
            window.dispatchEvent(new CustomEvent('storageUpdated'));

            setTimeout(() => {
              window.location.reload();
            }, 1000);

          } catch (error) {
            console.error('Chunked upload error:', error);
            showToast('error', error.message || 'Chunked upload failed');
            resetUploadForm();
          }
        }

        // Initialize search and pagination functionality
        const searchInput = document.querySelector('input[placeholder="Search videos..."]');
        const sortSelect = document.querySelector('select');
        const videoGrid = document.querySelector('.grid');
        const allVideosElements = Array.from(document.querySelectorAll('.grid > div:not(.col-span-full)'));
        const paginationContainer = document.querySelector('.mt-6.flex.items-center.justify-between');
        const paginationButtons = document.querySelectorAll('.mt-6.flex.items-center.justify-between button');

        const itemsPerPage = 12;
        let currentPage = 1;
        let totalPages = 1;

        if (allVideosElements.length === 0) return;

        window.allVideosData = allVideosElements.map(videoEl => {
          const dateText = videoEl.querySelector('.text-xs.text-gray-400 span:first-child').textContent.trim();
          let dateObj;
          try {
            const parts = dateText.split(/[\/,\s-]/);
            if (parts.length === 3) {
              const day = parseInt(parts[0], 10);
              const month = parseInt(parts[1], 10);
              const year = parseInt(parts[2], 10);

              if (!isNaN(day) && !isNaN(month) && !isNaN(year) &&
                year >= 1000 && year <= 9999 &&
                month >= 1 && month <= 12 &&
                day >= 1 && day <= 31) {
                dateObj = new Date(year, month - 1, day);
              } else {
                dateObj = new Date(0);
              }
            } else {
              dateObj = new Date(0);
            }

            if (!dateObj || isNaN(dateObj.getTime())) {
              dateObj = new Date(0);
            }
          } catch (e) {
            dateObj = new Date(0);
          }

          return {
            element: videoEl,
            title: videoEl.querySelector('h3').textContent.toLowerCase(),
            date: dateObj,
            size: videoEl.querySelector('.text-xs.text-gray-400 span:last-child').textContent
          };
        });

        const initialSortOrder = sortSelect.value;
        filterAndDisplayVideos('', initialSortOrder, 1);

        searchInput.addEventListener('input', function () {
          const searchTerm = this.value.toLowerCase().trim();
          currentPage = 1;
          filterAndDisplayVideos(searchTerm, sortSelect.value, currentPage);
        });

        sortSelect.addEventListener('change', function () {
          currentPage = 1;
          filterAndDisplayVideos(searchInput.value.toLowerCase().trim(), this.value, currentPage);
        });

        function setupPaginationControls(filteredVideos) {
          if (!paginationContainer) return;

          const paginationButtonsContainer = paginationContainer.querySelector('div.flex.items-center.gap-2');
          if (!paginationButtonsContainer) return;

          paginationButtonsContainer.innerHTML = '';

          const prevButton = document.createElement('button');
          const prevButtonClass = currentPage === 1
            ? 'bg-dark-700 text-gray-400 cursor-not-allowed'
            : 'bg-dark-700 hover:bg-dark-600 text-gray-400 hover:text-white transition-colors';
          prevButton.className = `w-9 h-9 flex items-center justify-center rounded-lg ${prevButtonClass}`;
          prevButton.innerHTML = '<i class="ti ti-chevron-left"></i>';
          prevButton.disabled = currentPage === 1;
          prevButton.addEventListener('click', () => {
            if (currentPage > 1) {
              currentPage--;
              filterAndDisplayVideos(searchInput.value.toLowerCase().trim(), sortSelect.value, currentPage);
            }
          });
          paginationButtonsContainer.appendChild(prevButton);

          totalPages = Math.ceil(filteredVideos.length / itemsPerPage);

          let startPage = Math.max(1, currentPage - 2);
          let endPage = Math.min(totalPages, startPage + 4);

          if (endPage - startPage < 4 && startPage > 1) {
            startPage = Math.max(1, endPage - 4);
          }

          for (let i = startPage; i <= endPage; i++) {
            const pageButton = document.createElement('button');
            const pageButtonClass = i === currentPage
              ? 'bg-primary text-white'
              : 'bg-dark-700 hover:bg-dark-600 text-gray-400 hover:text-white transition-colors';
            pageButton.className = `w-9 h-9 flex items-center justify-center rounded-lg ${pageButtonClass}`;
            pageButton.textContent = i;
            pageButton.addEventListener('click', () => {
              if (i !== currentPage) {
                currentPage = i;
                filterAndDisplayVideos(searchInput.value.toLowerCase().trim(), sortSelect.value, currentPage);
              }
            });
            paginationButtonsContainer.appendChild(pageButton);
          }

          const nextButton = document.createElement('button');
          const nextButtonClass = currentPage === totalPages
            ? 'bg-dark-700 text-gray-400 cursor-not-allowed'
            : 'bg-dark-700 hover:bg-dark-600 text-gray-400 hover:text-white transition-colors';
          nextButton.className = `w-9 h-9 flex items-center justify-center rounded-lg ${nextButtonClass}`;
          nextButton.innerHTML = '<i class="ti ti-chevron-right"></i>';
          nextButton.disabled = currentPage === totalPages;
          nextButton.addEventListener('click', () => {
            if (currentPage < totalPages) {
              currentPage++;
              filterAndDisplayVideos(searchInput.value.toLowerCase().trim(), sortSelect.value, currentPage);
            }
          });
          paginationButtonsContainer.appendChild(nextButton);
        }

        function filterAndDisplayVideos(searchTerm, sortOrder, page) {
          let filteredVideos = window.allVideosData.filter(video =>
            video.title.includes(searchTerm)
          );

          filteredVideos.forEach(video => {
            if (!(video.date instanceof Date) || isNaN(video.date.getTime())) {
              video.date = new Date(0);
            }
          });

          if (sortOrder === 'Oldest') {
            filteredVideos.sort((a, b) => a.date.getTime() - b.date.getTime());
          } else {
            filteredVideos.sort((a, b) => b.date.getTime() - a.date.getTime());
          }

          videoGrid.innerHTML = '';

          if (filteredVideos.length === 0) {
            videoGrid.innerHTML = `
              <div class="col-span-full text-center py-12">
                <div class="text-gray-500 mb-3">
                  <i class="ti ti-search text-6xl"></i>
                </div>
                <h3 class="text-xl font-medium text-gray-300 mb-1">${translations.noVideosFound}</h3>
                <p class="text-gray-500 mb-4">${translations.adjustSearch}</p>
              </div>
            `;
          } else {
            totalPages = Math.ceil(filteredVideos.length / itemsPerPage);
            currentPage = Math.min(page, totalPages);

            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = Math.min(startIndex + itemsPerPage, filteredVideos.length);
            const currentPageVideos = filteredVideos.slice(startIndex, endIndex);

            currentPageVideos.forEach(video => {
              videoGrid.appendChild(video.element.cloneNode(true));
            });

            videoGrid.querySelectorAll('.play-button').forEach(button => {
              const videoId = button.getAttribute('data-video-id');
              const videoTitle = button.getAttribute('data-video-title');
              button.onclick = function () {
                playVideo(videoId, videoTitle);
              };
            });

            videoGrid.querySelectorAll('button[onclick^="showRenameDialog"]').forEach(button => {
              const onclick = button.getAttribute('onclick');
              button.onclick = function () {
                const match = onclick.match(/showRenameDialog\('([^']+)',\s*'([^']+)'\)/);
                if (match) {
                  showRenameDialog(match[1], match[2]);
                }
              };
            });

            videoGrid.querySelectorAll('button[onclick^="showDeleteDialog"]').forEach(button => {
              const onclick = button.getAttribute('onclick');
              button.onclick = function () {
                const match = onclick.match(/showDeleteDialog\('([^']+)',\s*'([^']+)'\)/);
                if (match) {
                  showDeleteDialog(match[1], match[2]);
                }
              };
            });
          }

          setupPaginationControls(filteredVideos);
          updatePaginationInfo(filteredVideos);
        }

        function updatePaginationInfo(filteredVideos) {
          if (!paginationContainer) return;
          const paginationInfo = paginationContainer.querySelector('p.text-sm.text-gray-400');
          if (paginationInfo) {
            const totalVideos = filteredVideos.length;

            if (totalVideos === 0) {
              paginationInfo.textContent = translations.noVideos;
            } else {
              const startIndex = (currentPage - 1) * itemsPerPage + 1;
              const endIndex = Math.min(currentPage * itemsPerPage, totalVideos);
              paginationInfo.textContent = translations.showingVideos
                .replace('{{start}}', startIndex)
                .replace('{{end}}', endIndex)
                .replace('{{total}}', totalVideos);
            }
          }
        }
      });

      // Reprocess video function
      async function reprocessVideo(videoId) {
        try {
          showToast('info', 'Adding video to processing queue...');

          const response = await fetch(`/api/video-processing/reprocess/${videoId}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            }
          });

          const data = await response.json();

          if (data.success) {
            showToast('success', 'Video added to processing queue successfully');
            // Refresh the page after a short delay to show updated status
            setTimeout(() => {
              window.location.reload();
            }, 2000);
          } else {
            showToast('error', data.error || 'Failed to reprocess video');
          }
        } catch (error) {
          console.error('Error reprocessing video:', error);
          showToast('error', 'Error reprocessing video: ' + error.message);
        }
      }

      // Make function globally available
      window.reprocessVideo = reprocessVideo;

      // Video processing progress tracking
      let progressIntervals = new Map();

      function startProgressTracking(videoId) {
        // Clear existing interval if any
        if (progressIntervals.has(videoId)) {
          clearInterval(progressIntervals.get(videoId));
        }

        const interval = setInterval(async () => {
          try {
            const response = await fetch(`/api/video-processing/progress/${videoId}`);
            const data = await response.json();

            if (data.success && data.progress) {
              updateProgressUI(videoId, data.progress);

              // Stop tracking if completed or failed
              if (data.progress.status === 'completed' || data.progress.status === 'failed') {
                clearInterval(interval);
                progressIntervals.delete(videoId);

                // Reload page after completion to show updated status
                if (data.progress.status === 'completed') {
                  setTimeout(() => {
                    window.location.reload();
                  }, 2000);
                }
              }
            } else {
              // No progress data found, stop tracking
              clearInterval(interval);
              progressIntervals.delete(videoId);
            }
          } catch (error) {
            console.error('Error fetching progress:', error);
          }
        }, 2000); // Poll every 2 seconds

        progressIntervals.set(videoId, interval);
      }

      function updateProgressUI(videoId, progress) {
        const progressBar = document.querySelector(`.processing-progress[data-video-id="${videoId}"]`);
        const progressMessage = document.querySelector(`.processing-message[data-video-id="${videoId}"]`);
        const statusSpan = document.querySelector(`.processing-status[data-video-id="${videoId}"]`);

        if (progressBar) {
          progressBar.style.width = `${progress.percent}%`;
        }

        if (progressMessage) {
          progressMessage.textContent = progress.message || `Processing: ${progress.percent}%`;
        }

        if (statusSpan) {
          statusSpan.textContent = `Processing ${progress.percent}%`;
        }
      }

      // Start tracking for all currently processing videos
      document.addEventListener('DOMContentLoaded', () => {
        const processingVideos = document.querySelectorAll('.processing-status');
        processingVideos.forEach(element => {
          const videoId = element.getAttribute('data-video-id');
          if (videoId) {
            startProgressTracking(videoId);
          }
        });
      });

      // Clean up intervals when page unloads
      window.addEventListener('beforeunload', () => {
        progressIntervals.forEach(interval => clearInterval(interval));
        progressIntervals.clear();
      });
    </script>
    <style>
      .video-js {
        margin: 0 auto;
        height: 100% !important;
        width: auto !important;
        max-width: 100%;
      }

      .vjs-poster {
        background-size: contain !important;
      }

      .video-js.vjs-9-16,
      .video-js.vjs-4-5 {
        max-width: 56.25% !important;
      }

      .video-js.vjs-16-9 {
        max-width: 100% !important;
      }

      .video-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 53vh;
        max-height: 600px;
        background-color: black;
      }

      /* Mobile Gallery Responsive Styles - Only for mobile devices */
      @media (max-width: 768px) {
        /* Mobile-only spacing adjustments */
        body .p-6 {
          padding: 1rem !important;
        }

        /* Mobile search and filter section - more specific selector */
        .bg-gray-800.rounded-lg.p-4 {
          padding: 0.75rem !important;
        }

        /* Mobile pagination improvements - specific to gallery */
        .mt-6.flex.flex-col.sm\\:flex-row.items-center.justify-between {
          gap: 0.75rem;
        }

        /* Mobile modal adjustments - only for modals */
        .fixed.inset-0 .max-w-lg,
        .fixed.inset-0 .max-w-md,
        .fixed.inset-0 .max-w-4xl {
          max-width: calc(100vw - 2rem) !important;
          margin: 1rem;
        }

        /* Mobile upload button adjustments - specific to gallery header */
        .flex.flex-col.sm\\:flex-row.flex-wrap.gap-3 {
          gap: 0.5rem;
        }

        .flex.flex-col.sm\\:flex-row.flex-wrap.gap-3 button {
          flex: 1;
          min-width: 0;
          justify-content: center;
        }
      }

      @media (max-width: 480px) {
        /* Extra small mobile adjustments - very specific selectors */
        body h2.text-2xl {
          font-size: 1.5rem !important;
        }

        body .p-3 {
          padding: 0.5rem !important;
        }

        /* Compact pagination on small screens - specific to gallery */
        .mt-6.flex .w-9.h-9 {
          width: 2rem !important;
          height: 2rem !important;
          font-size: 0.875rem;
        }

        /* Mobile upload modal adjustments - specific to modals */
        .fixed.inset-0 .p-8 {
          padding: 1rem !important;
        }

        .fixed.inset-0 .p-6 {
          padding: 0.75rem !important;
        }

        /* Compact stats cards - specific to gallery */
        .card-enhanced {
          padding: 1rem !important;
        }

        .card-enhanced .text-3xl {
          font-size: 1.875rem !important;
        }
      }

      /* Desktop-specific adjustments to ensure proper sizing */
      @media (min-width: 769px) {
        /* Ensure desktop maintains proper spacing */
        .p-6 {
          padding: 1.5rem;
        }

        .bg-gray-800.rounded-lg.p-4 {
          padding: 1rem;
        }

        /* Desktop grid maintains proper columns */
        .grid.grid-cols-1.sm\\:grid-cols-2.lg\\:grid-cols-3.xl\\:grid-cols-4 {
          grid-template-columns: repeat(4, minmax(0, 1fr));
        }

        /* Desktop stats cards */
        .grid.grid-cols-1.md\\:grid-cols-2 {
          grid-template-columns: repeat(2, minmax(0, 1fr));
        }
      }
    </style>