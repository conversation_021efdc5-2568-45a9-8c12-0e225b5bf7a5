<% layout('layout') -%>
  <div class="flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4">
    <div>
      <h2 class="text-2xl font-bold"><%= t('history.title') %></h2>
      <p class="text-gray-400 text-sm mt-1"><%= t('history.subtitle') %></p>
    </div>
    <div class="flex items-center gap-3">
      <div class="relative flex-1 md:flex-none md:w-64">
        <input type="text" id="history-search" placeholder="<%= t('history.search_placeholder') %>"
          class="bg-dark-700 border border-gray-600 text-white pl-9 pr-4 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary w-full">
        <i class="ti ti-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
      </div>
      <div class="w-32">
        <select id="platform-filter"
          class="bg-dark-700 border border-gray-600 text-white px-4 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary w-full">
          <option value="all"><%= t('history.all_platforms') %></option>
          <option value="YouTube">YouTube</option>
          <option value="Facebook">Facebook</option>
          <option value="Twitch">Twitch</option>
          <option value="TikTok">TikTok</option>
          <option value="Instagram">Instagram</option>
          <option value="Custom"><%= t('streams.custom') %></option>
        </select>
      </div>
    </div>
  </div>

  <!-- Desktop View: Enhanced Cards Grid -->
  <div class="hidden md:block">
    <div id="history-cards-container" class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
      <% if (history && history.length > 0) { %>
        <% history.forEach(function(entry) { %>
          <div class="card-enhanced group hover:scale-[1.02] transition-all duration-300"
               data-id="<%= entry.id %>" data-platform="<%= entry.platform %>" data-title="<%= entry.title %>">
            <!-- Card Header with Thumbnail -->
            <div class="relative">
              <div class="aspect-video bg-gradient-to-br from-dark-700 via-dark-600 to-dark-700 rounded-t-lg overflow-hidden relative">
                <% if (entry.thumbnail_path) { %>
                  <img src="<%= entry.thumbnail_path %>"
                       class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                       onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                  <!-- Enhanced Fallback Thumbnail -->
                  <div class="w-full h-full bg-gradient-to-br from-primary/20 via-dark-700 to-secondary/20 flex flex-col items-center justify-center relative overflow-hidden" style="display: none;">
                    <!-- Background Pattern -->
                    <div class="absolute inset-0 opacity-10">
                      <div class="absolute top-4 left-4 w-8 h-8 border-2 border-primary/30 rounded-full"></div>
                      <div class="absolute top-8 right-8 w-6 h-6 border-2 border-secondary/30 rounded-full"></div>
                      <div class="absolute bottom-6 left-8 w-4 h-4 border-2 border-primary/30 rounded-full"></div>
                      <div class="absolute bottom-4 right-6 w-10 h-10 border-2 border-secondary/30 rounded-full"></div>
                    </div>

                    <!-- Main Content -->
                    <div class="relative z-10 text-center">
                      <div class="w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center shadow-lg">
                        <i class="ti ti-video text-white text-2xl"></i>
                      </div>
                      <p class="text-gray-300 text-sm font-medium mb-1">Stream Recording</p>
                      <p class="text-gray-500 text-xs">Thumbnail not available</p>
                    </div>

                    <!-- Decorative Elements -->
                    <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary via-secondary to-primary opacity-50"></div>
                    <div class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-secondary via-primary to-secondary opacity-50"></div>
                  </div>
                <% } else { %>
                  <!-- Enhanced Default Thumbnail -->
                  <div class="w-full h-full bg-gradient-to-br from-primary/20 via-dark-700 to-secondary/20 flex flex-col items-center justify-center relative overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="absolute inset-0 opacity-10">
                      <div class="absolute top-4 left-4 w-8 h-8 border-2 border-primary/30 rounded-full"></div>
                      <div class="absolute top-8 right-8 w-6 h-6 border-2 border-secondary/30 rounded-full"></div>
                      <div class="absolute bottom-6 left-8 w-4 h-4 border-2 border-primary/30 rounded-full"></div>
                      <div class="absolute bottom-4 right-6 w-10 h-10 border-2 border-secondary/30 rounded-full"></div>
                    </div>

                    <!-- Main Content -->
                    <div class="relative z-10 text-center">
                      <div class="w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center shadow-lg">
                        <i class="ti ti-video text-white text-2xl"></i>
                      </div>
                      <p class="text-gray-300 text-sm font-medium mb-1">Stream Recording</p>
                      <p class="text-gray-500 text-xs">No thumbnail</p>
                    </div>

                    <!-- Decorative Elements -->
                    <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary via-secondary to-primary opacity-50"></div>
                    <div class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-secondary via-primary to-secondary opacity-50"></div>
                  </div>
                <% } %>
              </div>

              <!-- Platform Badge -->
              <div class="absolute top-3 left-3">
                <div class="flex items-center bg-black/70 backdrop-blur-sm rounded-full px-2 py-1">
                  <i class="ti ti-<%= helpers.getPlatformIcon(entry.platform) %> text-<%= helpers.getPlatformColor(entry.platform) %> text-sm mr-1"></i>
                  <span class="text-white text-xs font-medium"><%= entry.platform || 'Custom' %></span>
                </div>
              </div>

              <!-- Duration Badge -->
              <div class="absolute top-3 right-3">
                <div class="bg-black/70 backdrop-blur-sm rounded px-2 py-1">
                  <span class="text-white text-xs font-medium">
                    <%= helpers.formatDuration(entry.duration) %>
                  </span>
                </div>
              </div>

              <!-- Delete Button -->
              <div class="absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <button onclick="deleteHistoryEntry('<%= entry.id %>', '<%= entry.title %>')"
                        class="w-8 h-8 bg-red-500/80 hover:bg-red-500 rounded-full flex items-center justify-center transition-colors">
                  <i class="ti ti-trash text-white text-sm"></i>
                </button>
              </div>
            </div>

            <!-- Card Content -->
            <div class="p-4">
              <h3 class="font-semibold text-white mb-2 line-clamp-2 group-hover:text-primary transition-colors">
                <%= entry.title %>
              </h3>

              <div class="space-y-2 text-sm text-gray-400">
                <div class="flex items-center">
                  <i class="ti ti-clock text-green-400 mr-2"></i>
                  <span>Started: <%= helpers.formatDateTime(entry.start_time) %></span>
                </div>
                <div class="flex items-center">
                  <i class="ti ti-clock-stop text-red-400 mr-2"></i>
                  <span>Ended: <%= helpers.formatDateTime(entry.end_time) %></span>
                </div>
              </div>
            </div>
          </div>
        <% }); %>
      <% } else { %>
        <div class="col-span-full">
          <div class="card-enhanced p-12 text-center">
            <div class="flex flex-col items-center">
              <div class="w-20 h-20 rounded-full bg-dark-700 flex items-center justify-center mb-6">
                <i class="ti ti-history text-gray-500 text-3xl"></i>
              </div>
              <h3 class="text-xl font-semibold text-gray-300 mb-2"><%= t('history.no_history') %></h3>
              <p class="text-gray-500"><%= t('history.completed_streams_appear') %></p>
            </div>
          </div>
        </div>
      <% } %>
    </div>
  </div>

  <!-- Mobile View: Compact Cards -->
  <div class="block md:hidden">
    <div id="mobile-history-container" class="space-y-4">
      <% if (history && history.length > 0) { %>
        <% history.forEach(function(entry) { %>
          <div class="bg-gray-800 rounded-lg p-4 hover:bg-dark-700/50 transition-colors"
               data-id="<%= entry.id %>" data-platform="<%= entry.platform %>" data-title="<%= entry.title %>">
            <div class="flex items-start space-x-3">
              <!-- Enhanced Thumbnail -->
              <div class="w-16 h-10 bg-gradient-to-br from-primary/20 via-dark-700 to-secondary/20 rounded overflow-hidden flex-shrink-0 relative">
                <% if (entry.thumbnail_path) { %>
                  <img src="<%= entry.thumbnail_path %>" class="w-full h-full object-cover"
                       onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                  <!-- Mobile Fallback Thumbnail -->
                  <div class="w-full h-full bg-gradient-to-br from-primary/30 via-dark-700 to-secondary/30 flex items-center justify-center relative" style="display: none;">
                    <div class="w-6 h-6 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center">
                      <i class="ti ti-video text-white text-xs"></i>
                    </div>
                    <!-- Mini decorative elements -->
                    <div class="absolute top-1 left-1 w-1 h-1 bg-primary/50 rounded-full"></div>
                    <div class="absolute top-1 right-1 w-1 h-1 bg-secondary/50 rounded-full"></div>
                    <div class="absolute bottom-1 left-1 w-1 h-1 bg-secondary/50 rounded-full"></div>
                    <div class="absolute bottom-1 right-1 w-1 h-1 bg-primary/50 rounded-full"></div>
                  </div>
                <% } else { %>
                  <!-- Mobile Default Thumbnail -->
                  <div class="w-full h-full bg-gradient-to-br from-primary/30 via-dark-700 to-secondary/30 flex items-center justify-center relative">
                    <div class="w-6 h-6 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center">
                      <i class="ti ti-video text-white text-xs"></i>
                    </div>
                    <!-- Mini decorative elements -->
                    <div class="absolute top-1 left-1 w-1 h-1 bg-primary/50 rounded-full"></div>
                    <div class="absolute top-1 right-1 w-1 h-1 bg-secondary/50 rounded-full"></div>
                    <div class="absolute bottom-1 left-1 w-1 h-1 bg-secondary/50 rounded-full"></div>
                    <div class="absolute bottom-1 right-1 w-1 h-1 bg-primary/50 rounded-full"></div>
                  </div>
                <% } %>
              </div>

              <!-- Content -->
              <div class="flex-1 min-w-0">
                <div class="flex items-start justify-between">
                  <h3 class="font-medium text-white text-sm line-clamp-2 mb-1">
                    <%= entry.title %>
                  </h3>
                  <button onclick="deleteHistoryEntry('<%= entry.id %>', '<%= entry.title %>')"
                          class="p-1 text-gray-400 hover:text-red-400 transition-colors ml-2">
                    <i class="ti ti-trash text-sm"></i>
                  </button>
                </div>

                <div class="flex items-center mb-2">
                  <i class="ti ti-<%= helpers.getPlatformIcon(entry.platform) %> text-<%= helpers.getPlatformColor(entry.platform) %> text-sm mr-1"></i>
                  <span class="text-xs text-gray-400"><%= entry.platform || 'Custom' %></span>
                  <span class="mx-2 text-gray-600">•</span>
                  <span class="text-xs text-gray-400"><%= helpers.formatDuration(entry.duration) %></span>
                </div>

                <div class="text-xs text-gray-500">
                  <%= helpers.formatDateTime(entry.start_time) %>
                </div>
              </div>
            </div>
          </div>
        <% }); %>
      <% } else { %>
        <div class="bg-gray-800 rounded-lg p-8 text-center">
          <div class="flex flex-col items-center">
            <div class="w-16 h-16 rounded-full bg-gradient-to-br from-primary/20 via-dark-700 to-secondary/20 flex items-center justify-center mb-4 relative">
              <div class="w-10 h-10 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center">
                <i class="ti ti-history text-white text-lg"></i>
              </div>
              <!-- Decorative dots -->
              <div class="absolute top-2 left-2 w-1 h-1 bg-primary/50 rounded-full"></div>
              <div class="absolute top-2 right-2 w-1 h-1 bg-secondary/50 rounded-full"></div>
              <div class="absolute bottom-2 left-2 w-1 h-1 bg-secondary/50 rounded-full"></div>
              <div class="absolute bottom-2 right-2 w-1 h-1 bg-primary/50 rounded-full"></div>
            </div>
            <h3 class="font-semibold text-gray-300 mb-1"><%= t('history.no_history') %></h3>
            <p class="text-gray-500 text-sm"><%= t('history.completed_streams_appear') %></p>
          </div>
        </div>
      <% } %>
    </div>
  </div>
  <script>
    // Translation variables for JavaScript
    const translations = {
      deleteConfirm: '<%= t("history.delete_confirm") %>',
      deleteSuccess: '<%= t("history.delete_success") %>',
      deleteError: '<%= t("history.delete_error") %>',
      deleteErrorGeneral: '<%= t("history.delete_error_general") %>',
      noHistoryFound: '<%= t("history.no_history") %>',
      completedStreamsAppear: '<%= t("history.completed_streams_appear") %>',
      noMatchingResults: '<%= t("history.no_matching_results") %>',
      adjustSearchFilter: '<%= t("history.adjust_search_filter") %>'
    };

    function getPlatformIcon(platform) {
      switch (platform) {
        case 'YouTube': return 'brand-youtube';
        case 'Facebook': return 'brand-facebook';
        case 'Twitch': return 'brand-twitch';
        case 'TikTok': return 'brand-tiktok';
        case 'Instagram': return 'brand-instagram';
        case 'Shopee Live': return 'shopping-bag';
        case 'Restream.io': return 'live-photo';
        default: return 'broadcast';
      }
    }
    function getPlatformColor(platform) {
      switch (platform) {
        case 'YouTube': return 'red-500';
        case 'Facebook': return 'blue-500';
        case 'Twitch': return 'purple-500';
        case 'TikTok': return 'gray-100';
        case 'Instagram': return 'pink-500';
        case 'Shopee Live': return 'orange-500';
        case 'Restream.io': return 'teal-500';
        default: return 'gray-400';
      }
    }
    function formatDateTime(isoString) {
      if (!isoString) return '--';
      const date = new Date(isoString);
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
    function formatDuration(seconds) {
      if (!seconds) return '--';
      const hours = Math.floor(seconds / 3600).toString().padStart(2, '0');
      const minutes = Math.floor((seconds % 3600) / 60).toString().padStart(2, '0');
      const secs = Math.floor(seconds % 60).toString().padStart(2, '0');
      return `${hours}:${minutes}:${secs}`;
    }
    function deleteHistoryEntry(id, title) {
      if (confirm(translations.deleteConfirm.replace('{title}', title))) {
        fetch(`/api/history/${id}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json'
          }
        })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              showToast(translations.deleteSuccess);

              // Remove from both desktop and mobile views
              const desktopCard = document.querySelector(`#history-cards-container [data-id="${id}"]`);
              const mobileCard = document.querySelector(`#mobile-history-container [data-id="${id}"]`);

              if (desktopCard) {
                desktopCard.classList.add('animate-fade-out');
                setTimeout(() => {
                  desktopCard.remove();
                  checkEmptyState();
                }, 300);
              }

              if (mobileCard) {
                mobileCard.classList.add('animate-fade-out');
                setTimeout(() => {
                  mobileCard.remove();
                  checkEmptyState();
                }, 300);
              }
            } else {
              showToast(translations.deleteError + ': ' + (data.error || translations.deleteErrorGeneral));
            }
          })
          .catch(error => {
            console.error('Error:', error);
            showToast(translations.deleteErrorGeneral);
          });
      }
    }

    function checkEmptyState() {
      const desktopContainer = document.getElementById('history-cards-container');
      const mobileContainer = document.getElementById('mobile-history-container');

      // Check desktop view
      const desktopCards = desktopContainer.querySelectorAll('[data-id]');
      if (desktopCards.length === 0) {
        desktopContainer.innerHTML = `
          <div class="col-span-full">
            <div class="card-enhanced p-12 text-center">
              <div class="flex flex-col items-center">
                <div class="w-20 h-20 rounded-full bg-dark-700 flex items-center justify-center mb-6">
                  <i class="ti ti-history text-gray-500 text-3xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-300 mb-2">${translations.noHistoryFound}</h3>
                <p class="text-gray-500">${translations.completedStreamsAppear}</p>
              </div>
            </div>
          </div>
        `;
      }

      // Check mobile view
      const mobileCards = mobileContainer.querySelectorAll('[data-id]');
      if (mobileCards.length === 0) {
        mobileContainer.innerHTML = `
          <div class="bg-gray-800 rounded-lg p-8 text-center">
            <div class="flex flex-col items-center">
              <div class="w-16 h-16 rounded-full bg-dark-700 flex items-center justify-center mb-4">
                <i class="ti ti-history text-gray-500 text-2xl"></i>
              </div>
              <h3 class="font-semibold text-gray-300 mb-1">${translations.noHistoryFound}</h3>
              <p class="text-gray-500 text-sm">${translations.completedStreamsAppear}</p>
            </div>
          </div>
        `;
      }
    }
    document.addEventListener('DOMContentLoaded', function () {
      const searchInput = document.getElementById('history-search');
      const platformFilter = document.getElementById('platform-filter');

      function filterCards() {
        const searchTerm = searchInput.value.toLowerCase();
        const platform = platformFilter.value;

        // Filter desktop cards
        const desktopCards = document.querySelectorAll('#history-cards-container [data-id]');
        let visibleDesktopCount = 0;

        desktopCards.forEach(card => {
          const title = card.getAttribute('data-title').toLowerCase();
          const cardPlatform = card.getAttribute('data-platform');
          const matchesSearch = !searchTerm || title.includes(searchTerm);
          const matchesPlatform = platform === 'all' || cardPlatform === platform;

          if (matchesSearch && matchesPlatform) {
            card.style.display = '';
            visibleDesktopCount++;
          } else {
            card.style.display = 'none';
          }
        });

        // Filter mobile cards
        const mobileCards = document.querySelectorAll('#mobile-history-container [data-id]');
        let visibleMobileCount = 0;

        mobileCards.forEach(card => {
          const title = card.getAttribute('data-title').toLowerCase();
          const cardPlatform = card.getAttribute('data-platform');
          const matchesSearch = !searchTerm || title.includes(searchTerm);
          const matchesPlatform = platform === 'all' || cardPlatform === platform;

          if (matchesSearch && matchesPlatform) {
            card.style.display = '';
            visibleMobileCount++;
          } else {
            card.style.display = 'none';
          }
        });

        // Handle empty search results for desktop
        const desktopContainer = document.getElementById('history-cards-container');
        const existingDesktopEmpty = document.getElementById('desktop-empty-search-results');

        if (visibleDesktopCount === 0 && desktopCards.length > 0) {
          if (!existingDesktopEmpty) {
            const emptyDiv = document.createElement('div');
            emptyDiv.id = 'desktop-empty-search-results';
            emptyDiv.className = 'col-span-full';
            emptyDiv.innerHTML = `
              <div class="card-enhanced p-12 text-center">
                <div class="flex flex-col items-center">
                  <div class="w-20 h-20 rounded-full bg-dark-700 flex items-center justify-center mb-6">
                    <i class="ti ti-search-off text-gray-500 text-3xl"></i>
                  </div>
                  <h3 class="text-xl font-semibold text-gray-300 mb-2">${translations.noMatchingResults}</h3>
                  <p class="text-gray-500">${translations.adjustSearchFilter}</p>
                </div>
              </div>
            `;
            desktopContainer.appendChild(emptyDiv);
          }
        } else if (existingDesktopEmpty) {
          existingDesktopEmpty.remove();
        }

        // Handle empty search results for mobile
        const mobileContainer = document.getElementById('mobile-history-container');
        const existingMobileEmpty = document.getElementById('mobile-empty-search-results');

        if (visibleMobileCount === 0 && mobileCards.length > 0) {
          if (!existingMobileEmpty) {
            const emptyDiv = document.createElement('div');
            emptyDiv.id = 'mobile-empty-search-results';
            emptyDiv.innerHTML = `
              <div class="bg-gray-800 rounded-lg p-8 text-center">
                <div class="flex flex-col items-center">
                  <div class="w-16 h-16 rounded-full bg-dark-700 flex items-center justify-center mb-4">
                    <i class="ti ti-search-off text-gray-500 text-2xl"></i>
                  </div>
                  <h3 class="font-semibold text-gray-300 mb-1">${translations.noMatchingResults}</h3>
                  <p class="text-gray-500 text-sm">${translations.adjustSearchFilter}</p>
                </div>
              </div>
            `;
            mobileContainer.appendChild(emptyDiv);
          }
        } else if (existingMobileEmpty) {
          existingMobileEmpty.remove();
        }
      }

      searchInput.addEventListener('input', filterCards);
      platformFilter.addEventListener('change', filterCards);
    });
    function showToast(message) {
      const toast = document.createElement('div');
      toast.className = 'fixed bottom-4 right-4 bg-dark-800 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-fade-in';
      toast.innerHTML = `
      <div class="flex items-center">
        <i class="ti ti-check text-green-400 mr-2"></i>
        <span>${message}</span>
      </div>
    `;
      document.body.appendChild(toast);
      const style = document.createElement('style');
      style.textContent = `
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
      }
      @keyframes fadeOut {
        from { opacity: 1; transform: translateY(0); }
        to { opacity: 0; transform: translateY(20px); }
      }
      .animate-fade-in {
        animation: fadeIn 0.3s ease-out forwards;
      }
      .animate-fade-out {
        animation: fadeOut 0.3s ease-out forwards;
      }
    `;
      document.head.appendChild(style);
      setTimeout(() => {
        toast.classList.remove('animate-fade-in');
        toast.classList.add('animate-fade-out');
        setTimeout(() => {
          document.body.removeChild(toast);
        }, 300);
      }, 3000);
    }
  </script>