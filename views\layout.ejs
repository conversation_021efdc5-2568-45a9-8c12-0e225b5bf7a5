<!DOCTYPE html>
<html lang="<%= locale || 'id' %>">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>
    <%= title %> - StreamOnPod
  </title>

  <!-- SEO Meta Tags -->
  <% if (typeof seo !== 'undefined' && seo) { %>
    <%- seo.renderMetaTags() %>
  <% } %>

  <!-- Favicon -->
  <link rel="icon" href="/images/streamonpod-logo.png" type="image/png">
  <link rel="apple-touch-icon" href="/images/streamonpod-logo.png">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@2.30.0/tabler-icons.min.css">
  <!-- Critical CSS inline for faster loading -->
  <link rel="stylesheet" href="/css/critical.css" inline>
  <!-- Preload main stylesheet -->
  <link rel="preload" href="/css/styles.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="/css/styles.min.css"></noscript>


  <link href="https://vjs.zencdn.net/7.20.3/video-js.css" rel="stylesheet" />
  <script src="https://vjs.zencdn.net/7.20.3/video.min.js"></script>

  <!-- Modern Notification System -->
  <link rel="stylesheet" href="/css/notifications.css">
  <!-- html2canvas for modal generator export feature -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
  <script>
    // Suppress Tailwind production warning
    if (typeof console !== 'undefined' && console.warn) {
      const originalWarn = console.warn;
      console.warn = function(...args) {
        if (args[0] && args[0].includes && args[0].includes('cdn.tailwindcss.com should not be used in production')) {
          return; // Suppress this specific warning
        }
        originalWarn.apply(console, args);
      };
    }

    tailwind.config = {
      darkMode: 'class',
      theme: {
        fontFamily: {
          'inter': ['Inter', 'sans-serif'],
          'sans': ['Inter', 'system-ui', 'sans-serif']
        },
        extend: {
          colors: {
            'primary': '#ad6610',
            'secondary': '#8b5209',
            'dark': {
              '900': '#121212',
              '800': '#252525',
              '700': '#2D2D2D',
              '600': '#3D3D3D',
              '500': '#6E6E6E',
              '400': '#8F8F8F',
              '300': '#AFAFAF',
              '200': '#CFCFCF',
              '100': '#E5E5E5',
            },
            'gray': {
              '900': '#121212',
              '800': '#252525',
              '700': '#2D2D2D',
              '600': '#3D3D3D',
              '500': '#6E6E6E',
              '400': '#8F8F8F',
              '300': '#AFAFAF',
              '200': '#CFCFCF',
              '100': '#E5E5E5',
              '50': '#F5F5F5',
            }
          }
        }
      }
    }
  </script>
  <!-- Load optimized JavaScript -->
  <script src="/js/stream-modal.min.js" defer></script>
  <script src="/js/lazy-loading.js" defer></script>
  <% if (helpers.getUserRole && helpers.getUserRole(req) === 'admin') { %>
  <script src="/socket.io/socket.io.js"></script>
  <% } %>

  <!-- Service Worker Registration -->
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then(registration => {
            // Service worker registered successfully (logging removed for production)
          })
          .catch(registrationError => {
            // Service worker registration failed (logging removed for production)
          });
      });
    }
  </script>

  <!-- JSON-LD Structured Data -->
  <% if (typeof seo !== 'undefined' && seo && seo.renderJsonLd) { %>
    <%- seo.renderJsonLd() %>
  <% } %>
</head>
<body class="bg-dark-900 text-white font-inter">
  <div
    class="lg:hidden fixed top-0 left-0 right-0 h-16 bg-dark-800 shadow-lg flex items-center justify-between px-4 z-30">
    <div class="flex items-center">
      <div class="streamonpod-logo-mobile">
        <span class="text-white">Stream</span><span class="text-primary-accent">OnPod</span>
      </div>
    </div>
    <div class="flex items-center gap-1">
      <a href="https://t.me/streamonpod_support" target="_blank"
         class="p-2 text-gray-400 hover:text-white transition-colors">
         <i class="ti ti-brand-telegram text-lg"></i>
      </a>

      <!-- Mobile Language Switcher -->
      <div class="relative">
        <button id="mobile-language-btn" class="p-2 text-gray-400 hover:text-white transition-colors relative">
          <i class="ti ti-language text-lg"></i>
        </button>
      </div>

      <!-- Mobile Profile Section -->
      <div class="relative">
        <button id="mobile-header-profile-btn" class="p-2 text-gray-400 hover:text-white transition-colors flex items-center gap-2">
          <div class="w-6 h-6 rounded-full overflow-hidden">
            <%- helpers.getAvatar(req) %>
          </div>
        </button>
        <div id="mobile-header-profile-dropdown" class="hidden absolute right-0 top-full mt-2 w-48 bg-dark-800 border border-gray-700 rounded-lg shadow-xl z-[9999]">
          <div class="px-4 py-2 border-b border-gray-700">
            <div class="font-medium text-sm">
              <%= helpers.getUsername(req) %>
            </div>
            <div class="text-xs text-gray-400">
              <%= req.session.email || '' %>
            </div>
          </div>
          <a href="/settings"
            class="flex items-center px-4 py-2.5 text-sm text-gray-300 hover:bg-dark-700 hover:text-white transition-colors">
            <i class="ti ti-settings mr-3"></i>
            <span><%= t('common.settings') %></span>
          </a>
          <a href="https://t.me/streamonpod_support"
            class="flex items-center px-4 py-2.5 text-sm text-gray-300 hover:bg-dark-700 hover:text-white transition-colors" target="_blank">
            <i class="ti ti-brand-telegram mr-3"></i>
            <span><%= t('common.help') %></span>
          </a>
          <div class="h-px bg-gray-700 mx-4"></div>
          <a href="/logout" class="flex items-center px-4 py-2.5 text-sm text-logout hover:bg-dark-700 transition-colors">
            <i class="ti ti-logout mr-3"></i>
            <span><%= t('common.logout') %></span>
          </a>
        </div>
      </div>
    </div>
  </div>
  <!-- Desktop Header Navigation -->
  <div class="hidden lg:block fixed top-0 left-0 right-0 h-20 bg-dark-800 shadow-lg z-30 border-b border-gray-700">
    <div class="flex items-center justify-between px-8 h-full max-w-screen-2xl mx-auto">
      <!-- Logo -->
      <div class="flex items-center flex-shrink-0">
        <div class="streamonpod-logo-desktop">
          <span class="text-white">Stream</span><span class="text-primary-accent">OnPod</span>
        </div>
      </div>

      <!-- Navigation Menu -->
      <div class="flex items-center space-x-4 flex-1 justify-center">
        <a href="/dashboard" class="header-nav-item <%= active === 'dashboard' ? 'header-nav-active' : '' %>">
          <i class="ti ti-device-tv text-lg"></i>
          <span><%= t('common.streams') %></span>
        </a>
        <a href="/gallery" class="header-nav-item <%= active === 'gallery' ? 'header-nav-active' : '' %>">
          <i class="ti ti-photo text-lg"></i>
          <span><%= t('common.gallery') %></span>
        </a>
        <a href="/history" class="header-nav-item <%= active === 'history' ? 'header-nav-active' : '' %>">
          <i class="ti ti-history text-lg"></i>
          <span><%= t('common.history') %></span>
        </a>
        <a href="/subscription/plans" class="header-nav-item <%= active === 'subscription' ? 'header-nav-active' : '' %>">
          <i class="ti ti-crown text-lg"></i>
          <span><%= t('common.plans') %></span>
        </a>
        <a href="/referral/dashboard" class="header-nav-item <%= active === 'referral' ? 'header-nav-active' : '' %>">
          <i class="ti ti-gift text-lg"></i>
          <span><%= t('referral.title') %></span>
        </a>
        <% if (helpers.getUserRole && helpers.getUserRole(req) === 'admin') { %>
        <a href="/admin/dashboard" class="header-nav-item <%= active === 'admin' ? 'header-nav-active' : '' %>">
          <i class="ti ti-shield text-lg"></i>
          <span><%= t('common.admin') %></span>
        </a>
        <% } %>
      </div>

      <!-- Right Side Actions -->
      <div class="flex items-center space-x-4 flex-shrink-0">
        <a href="https://t.me/streamonpod_support" target="_blank"
           class="header-action-btn">
           <i class="ti ti-brand-telegram text-lg"></i>
        </a>

        <!-- Language Switcher -->
        <div class="relative">
          <button id="language-btn" class="header-action-btn flex items-center gap-2 group">
            <i class="ti ti-language text-lg group-hover:rotate-12 transition-transform duration-300"></i>
            <span class="text-xs font-semibold"><%= getCurrentLanguage().code.toUpperCase() %></span>
            <i class="ti ti-chevron-down text-xs transition-transform duration-300 group-hover:rotate-180"></i>
          </button>
          <div id="language-dropdown" class="hidden absolute right-0 top-full mt-2 w-52 bg-dark-800 border border-gray-700 rounded-xl shadow-2xl z-[9999] overflow-hidden">
            <div class="p-2">
              <div class="text-xs font-medium text-gray-500 px-3 py-2 border-b border-gray-700/50 mb-1">
                <%= t('common.language') %>
              </div>
              <% getLanguages().forEach(function(lang) { %>
              <a href="<%= getLanguageUrl(lang.code) %>"
                 class="group flex items-center px-3 py-2.5 text-sm text-gray-300 hover:bg-primary/20 hover:text-white transition-all duration-200 rounded-lg <%= locale === lang.code ? 'bg-primary/30 text-primary border border-primary/30' : '' %>">
                <i class="ti ti-language text-lg mr-3 group-hover:scale-110 transition-transform"></i>
                <div class="flex-1">
                  <div class="font-medium"><%= lang.native %></div>
                  <div class="text-xs text-gray-500 group-hover:text-gray-400"><%= lang.name %></div>
                </div>
                <% if (locale === lang.code) { %>
                <i class="ti ti-check text-primary ml-auto animate-pulse"></i>
                <% } %>
              </a>
              <% }); %>
            </div>
          </div>
        </div>



        <!-- Profile -->
        <div class="relative">
          <button id="desktop-profile-btn" class="header-action-btn flex items-center space-x-2">
            <div class="w-6 h-6 rounded-full overflow-hidden">
              <%- helpers.getAvatar(req) %>
            </div>
            <span class="text-sm font-medium"><%= helpers.getUsername(req) %></span>
            <i class="ti ti-chevron-down text-sm transition-transform duration-300" id="profile-chevron"></i>
          </button>
          <div id="desktop-profile-dropdown" class="hidden absolute right-0 top-full mt-2 w-48 bg-dark-800 border border-gray-700 rounded-lg shadow-xl z-[9999]">
            <div class="px-4 py-2 border-b border-gray-700">
              <div class="font-medium">
                <%= helpers.getUsername(req) %>
              </div>
            </div>
            <a href="/settings"
              class="flex items-center px-4 py-2.5 text-sm text-gray-300 hover:bg-dark-700 hover:text-white transition-colors">
              <i class="ti ti-settings mr-3"></i>
              <span><%= t('common.settings') %></span>
            </a>
            <a href="https://t.me/streamonpod_support"
              class="flex items-center px-4 py-2.5 text-sm text-gray-300 hover:bg-dark-700 hover:text-white transition-colors" target="_blank">
              <i class="ti ti-brand-telegram mr-3"></i>
              <span><%= t('common.help') %></span>
            </a>
            <div class="h-px bg-gray-700 mx-4"></div>
            <a href="/logout" class="flex items-center px-4 py-2.5 text-sm text-logout hover:bg-dark-700 transition-colors">
              <i class="ti ti-logout mr-3"></i>
              <span><%= t('common.logout') %></span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="flex">
    <div class="w-full flex flex-col min-h-screen">
      <!-- Page Header with Breadcrumb -->
      <div class="hidden lg:block bg-dark-900 border-b border-gray-700 pt-20 pb-4 px-6">
        <div class="max-w-screen-2xl mx-auto">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="page-indicator">
                <%
                let pageTitle = '';
                let pageIcon = '';
                let pageDescription = '';

                switch(active) {
                  case 'dashboard':
                    pageTitle = t('dashboard.title');
                    pageIcon = 'ti-device-tv';
                    pageDescription = t('dashboard.subtitle') || 'Manage your live streams';
                    break;
                  case 'gallery':
                    pageTitle = t('gallery.title');
                    pageIcon = 'ti-photo';
                    pageDescription = 'Browse your video gallery';
                    break;
                  case 'history':
                    pageTitle = t('history.title');
                    pageIcon = 'ti-history';
                    pageDescription = 'View your streaming history';
                    break;
                  case 'subscription':
                    pageTitle = t('subscription.title');
                    pageIcon = 'ti-crown';
                    pageDescription = 'Manage your subscription plans';
                    break;
                  case 'referral':
                    pageTitle = 'Program Referral';
                    pageIcon = 'ti-gift';
                    pageDescription = 'Ajak teman dan dapatkan komisi';
                    break;
                  case 'admin':
                    pageTitle = t('admin.title');
                    pageIcon = 'ti-shield';
                    pageDescription = 'System administration panel';
                    break;
                  case 'settings':
                    pageTitle = t('common.settings');
                    pageIcon = 'ti-settings';
                    pageDescription = 'Configure your preferences';
                    break;
                  default:
                    pageTitle = 'StreamOnPod';
                    pageIcon = 'ti-home';
                    pageDescription = 'Welcome to StreamOnPod';
                }
                %>
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-primary/20 rounded-xl flex items-center justify-center">
                    <i class="ti <%= pageIcon %> text-primary text-xl"></i>
                  </div>
                  <div>
                    <h1 class="text-xl font-bold text-white"><%= pageTitle %></h1>
                    <p class="text-sm text-gray-400"><%= pageDescription %></p>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex items-center space-x-2 text-sm text-gray-400">
              <i class="ti ti-clock text-xs"></i>
              <span id="current-time"></span>
            </div>
          </div>
        </div>
      </div>

      <div class="p-6 pt-6 lg:pt-6 flex-1">
        <%- body %>
      </div>

      <!-- Footer -->
      <footer class="enhanced-dashboard-footer bg-dark-800 border-t border-gray-700 py-4 sm:py-4 lg:py-6 px-4 sm:px-4 lg:px-6 mt-auto relative">
        <!-- Subtle brand accent line -->
        <div class="absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary/30 to-transparent"></div>

        <!-- Mobile-first responsive layout -->
        <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between sm:gap-4">
          <!-- Brand and links section - Mobile optimized -->
          <div class="flex flex-col items-center gap-3 sm:flex-row sm:items-center sm:gap-3">
            <!-- Brand -->
            <div class="text-sm sm:text-sm text-gray-400 text-center sm:text-left">
              <span class="text-primary font-semibold">StreamOnPod</span>
              <span class="text-xs font-medium bg-gray-700/80 px-2 py-1 rounded-md ml-1">© 2025</span>
            </div>

            <!-- Links - Mobile: horizontal with better spacing, Desktop: with separators -->
            <div class="flex flex-wrap items-center justify-center gap-4 sm:gap-3 text-sm sm:text-sm">
              <!-- Separator - hidden on mobile -->
              <div class="hidden sm:block h-4 w-px bg-gradient-to-b from-primary/40 to-gray-700"></div>

              <a href="https://t.me/streamonpod_support" target="_blank" class="dashboard-footer-link">
                <%= t('common.support') %>
              </a>

              <div class="hidden sm:block h-4 w-px bg-gradient-to-b from-primary/40 to-gray-700"></div>

              <a href="/tos" class="dashboard-footer-link">
                <%= t('landing.footer.terms_of_service') %>
              </a>

              <div class="hidden sm:block h-4 w-px bg-gradient-to-b from-primary/40 to-gray-700"></div>

              <a href="/privacy-policy" class="dashboard-footer-link">
                <%= t('landing.footer.privacy_policy') %>
              </a>
            </div>
          </div>

          <!-- Support button - Mobile optimized -->
          <div class="flex justify-center sm:justify-end">
            <a href="https://t.me/streamonpod_support" target="_blank" class="enhanced-support-button relative group">
              <span class="relative z-10 flex items-center gap-2 text-white font-medium px-4 py-2 sm:px-3 sm:py-1.5 text-sm">
                <i class="ti ti-brand-telegram text-primary group-hover:text-white transition-colors text-base"></i>
                <span class="sm:hidden">Help</span>
                <span class="hidden sm:inline"><%= t('common.support') %></span>
              </span>
            </a>
          </div>
        </div>
      </footer>
    </div>
  </div>
  <div class="lg:hidden floating-bottom-nav">
    <nav class="flex justify-around items-center h-16">
      <a href="/dashboard" class="bottom-nav-item <%= active === 'dashboard' ? 'bottom-nav-active' : '' %>">
        <i class="ti ti-device-tv"></i>
        <span><%= t('common.streams') %></span>
      </a>
      <a href="/gallery" class="bottom-nav-item <%= active === 'gallery' ? 'bottom-nav-active' : '' %>">
        <i class="ti ti-photo"></i>
        <span><%= t('common.gallery') %></span>
      </a>
      <a href="/history" class="bottom-nav-item <%= active === 'history' ? 'bottom-nav-active' : '' %>">
        <i class="ti ti-history"></i>
        <span><%= t('common.history') %></span>
      </a>
      <a href="/subscription/plans" class="bottom-nav-item <%= active === 'subscription' ? 'bottom-nav-active' : '' %>">
        <i class="ti ti-crown"></i>
        <span><%= t('common.plans') %></span>
      </a>
      <a href="/referral/dashboard" class="bottom-nav-item <%= active === 'referral' ? 'bottom-nav-active' : '' %>">
        <i class="ti ti-gift"></i>
        <span><%= t('referral.title') %></span>
      </a>
      <% if (helpers.getUserRole && helpers.getUserRole(req) === 'admin') { %>
      <a href="/admin/dashboard" class="bottom-nav-item <%= active === 'admin' ? 'bottom-nav-active' : '' %>">
        <i class="ti ti-shield"></i>
        <span><%= t('common.admin') %></span>
      </a>
      <% } %>
      <a href="/settings" class="bottom-nav-item <%= active === 'settings' ? 'bottom-nav-active' : '' %>">
        <i class="ti ti-settings"></i>
        <span><%= t('common.settings') %></span>
      </a>
    </nav>
  </div>


  <!-- Mobile Language Popup -->
  <div id="mobile-language-popup"
    class="fixed bottom-16 right-2 bg-dark-800 rounded-lg shadow-xl border border-gray-700 overflow-hidden hidden transform transition-all duration-200 ease-out opacity-0 translate-y-4 w-48">
    <div class="p-3 border-b border-gray-700">
      <h3 class="font-medium text-sm"><%= t('common.language') %></h3>
    </div>
    <div class="p-2">
      <% getLanguages().forEach(function(lang) { %>
      <a href="<%= getLanguageUrl(lang.code) %>"
         class="flex items-center px-3 py-2 text-sm text-gray-300 hover:bg-dark-700 hover:text-white transition-colors rounded <%= locale === lang.code ? 'bg-primary/20 text-primary' : '' %>">
        <span class="w-6 text-center mr-3"><%= lang.code.toUpperCase() %></span>
        <span><%= lang.native %></span>
        <% if (locale === lang.code) { %>
        <i class="ti ti-check ml-auto text-primary"></i>
        <% } %>
      </a>
      <% }); %>
    </div>
  </div>


<!-- Modern Notification System -->
<script src="/js/notifications.js"></script>

<!-- StreamOnPod Logo Styling -->
<style>
  /* StreamOnPod Logo Styling */
  .streamonpod-logo-mobile {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 1.125rem; /* 18px - lebih kecil */
    font-weight: 600;
    letter-spacing: -0.025em;
    line-height: 1;
    user-select: none;
  }

  .streamonpod-logo-desktop {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 1.5rem; /* 24px - lebih kecil */
    font-weight: 600;
    letter-spacing: -0.025em;
    line-height: 1;
    user-select: none;
  }

  .streamonpod-logo-large {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 2rem; /* 32px - lebih kecil */
    font-weight: 600;
    letter-spacing: -0.025em;
    line-height: 1;
    user-select: none;
  }

  .streamonpod-logo-xl {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 2.5rem; /* 40px - lebih kecil */
    font-weight: 600;
    letter-spacing: -0.025em;
    line-height: 1;
    user-select: none;
  }

  .text-primary-accent {
    color: #ad6610;
  }

  @media (max-width: 640px) {
    .streamonpod-logo-large {
      font-size: 1.75rem; /* 28px on mobile */
    }
    .streamonpod-logo-xl {
      font-size: 2rem; /* 32px on mobile */
    }
  }
</style>

</body>
</html>
<style>
  #profile-dropdown {
    position: fixed;
    z-index: 100;
    min-width: 220px;
    border-radius: 8px;
    transition: all 0.2s ease;
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
    width: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    bottom: 80px;
    left: 15px;
  }
  #profile-dropdown.hidden {
    opacity: 0;
    transform: translateY(10px);
    pointer-events: none;
    display: block !important;
    visibility: hidden;
  }
  @media (max-width: 768px) {
    #profile-dropdown {
      min-width: 200px;
      right: 20px;
      left: auto;
      bottom: auto;
      top: 100px;
    }
  }
  .text-logout {
    color: #FF5555;
  }
  .text-logout:hover {
    color: #FF7777;
  }
  /* Legacy kirim-tip-button styles removed - now using enhanced-support-button */
  @media (max-width: 1023px) {
    body {
      padding-top: 64px; /* Account for mobile header height (h-16 = 64px) */
    }
  }

  /* Floating Bottom Navigation Styles */
  .floating-bottom-nav {
    position: fixed;
    bottom: 16px; /* 16px margin from bottom */
    left: 16px;
    right: 16px;
    background: rgba(37, 37, 37, 0.95); /* bg-dark-800 (#252525) - sama dengan header */
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(45, 45, 45, 0.6); /* dark-700 border (#2D2D2D) */
    border-radius: 20px; /* rounded corners */
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.5),
      0 2px 8px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.03); /* very subtle inner highlight */
    z-index: 30;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .floating-bottom-nav:hover {
    transform: translateY(-2px);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.6),
      0 4px 12px rgba(0, 0, 0, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);
  }

  /* Ensure floating nav only applies to mobile */
  @media (min-width: 1024px) {
    .floating-bottom-nav {
      display: none !important;
    }
  }

  /* Add subtle animation on load */
  .floating-bottom-nav {
    animation: slideUpFloat 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  @keyframes slideUpFloat {
    0% {
      opacity: 0;
      transform: translateY(100px) scale(0.9);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
  .bottom-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    text-align: center;
    padding: 10px 4px;
    color: #8F8F8F; /* dark-400 - sesuai dengan tema aplikasi */
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 0; /* Allow flex items to shrink */
    border-radius: 12px;
    margin: 0 2px;
  }
  .bottom-nav-item i {
    font-size: 22px;
    margin-bottom: 4px;
    transition: all 0.3s ease;
  }
  .bottom-nav-item span {
    font-size: 11px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    font-weight: 500;
    transition: all 0.3s ease;
  }
  .bottom-nav-item:hover {
    color: #E5E5E5; /* dark-100 - sesuai dengan tema aplikasi */
    background-color: rgba(173, 102, 16, 0.15);
    transform: translateY(-1px);
  }
  .bottom-nav-item:active {
    transform: translateY(0) scale(0.95);
    background-color: rgba(173, 102, 16, 0.15);
  }

  /* Touch device optimizations */
  @media (hover: none) and (pointer: coarse) {
    .floating-bottom-nav:hover {
      transform: translateY(0); /* Disable hover lift on touch devices */
    }
    .bottom-nav-item:hover {
      transform: translateY(0); /* Disable hover lift on touch devices */
    }
  }
  .bottom-nav-active {
    color: #ad6610;
    background-color: rgba(173, 102, 16, 0.15);
  }
  .bottom-nav-active::before {
    content: '';
    position: absolute;
    top: 6px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background-color: #ad6610;
    border-radius: 50%;
    box-shadow: 0 0 8px rgba(173, 102, 16, 0.6);
  }

  /* Responsive floating bottom navigation */
  @media (max-width: 480px) {
    .floating-bottom-nav {
      bottom: 12px;
      left: 12px;
      right: 12px;
      border-radius: 18px;
    }
    .bottom-nav-item span {
      font-size: 9px;
    }
    .bottom-nav-item i {
      font-size: 18px;
      margin-bottom: 2px;
    }
    .bottom-nav-item {
      padding: 8px 2px;
      margin: 0 1px;
      border-radius: 10px;
    }
  }

  /* Extra small screens - further optimize for 7 items */
  @media (max-width: 360px) {
    .floating-bottom-nav {
      bottom: 8px;
      left: 8px;
      right: 8px;
      border-radius: 16px;
    }
    .bottom-nav-item span {
      font-size: 8px;
    }
    .bottom-nav-item i {
      font-size: 16px;
      margin-bottom: 1px;
    }
    .bottom-nav-item {
      padding: 6px 1px;
      margin: 0 0.5px;
      border-radius: 8px;
    }
  }
  @media (max-width: 1023px) {
    .min-h-screen {
      padding-bottom: 100px !important; /* Increased for floating nav */
    }
    #mobile-profile-popup.show {
      opacity: 1;
      transform: translateY(0);
      visibility: visible;
    }
  }

  /* Floating navigation popup adjustments */
  @media (max-width: 1023px) {
    #mobile-profile-popup {
      bottom: 88px !important; /* Adjust for floating nav */
    }
    #mobile-language-popup {
      bottom: 88px !important; /* Adjust for floating nav */
    }
  }

  @media (max-width: 480px) {
    #mobile-profile-popup {
      bottom: 84px !important;
    }
    #mobile-language-popup {
      bottom: 84px !important;
    }
  }

  @media (max-width: 360px) {
    #mobile-profile-popup {
      bottom: 80px !important;
    }
    #mobile-language-popup {
      bottom: 80px !important;
    }
  }


  /* Language popup styles */
  #mobile-language-popup.show {
    opacity: 1 !important;
    transform: translateY(0) !important;
    visibility: visible !important;
  }
  #mobile-language-popup {
    z-index: 100;
    transition: opacity 0.3s, transform 0.3s, visibility 0.3s;
    display: block !important;
    visibility: hidden;
  }
  #mobile-language-popup.hidden {
    display: none !important;
  }

  /* Mobile Header Profile Dropdown Styles */
  #mobile-header-profile-dropdown {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: top right;
  }

  #mobile-header-profile-dropdown.show {
    opacity: 1 !important;
    transform: translateY(0) scale(1) !important;
    visibility: visible !important;
  }

  #mobile-header-profile-dropdown.hidden {
    display: none !important;
  }
</style>
<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Mobile Header Profile Dropdown
    const mobileHeaderProfileBtn = document.getElementById('mobile-header-profile-btn');
    const mobileHeaderProfileDropdown = document.getElementById('mobile-header-profile-dropdown');

    if (mobileHeaderProfileBtn && mobileHeaderProfileDropdown) {
      mobileHeaderProfileBtn.addEventListener('click', function (e) {
        e.preventDefault();
        e.stopPropagation();

        const isHidden = mobileHeaderProfileDropdown.classList.contains('hidden');

        // Close mobile language popup if open
        const mobileLanguagePopup = document.getElementById('mobile-language-popup');
        if (mobileLanguagePopup && !mobileLanguagePopup.classList.contains('hidden')) {
          mobileLanguagePopup.classList.remove('show');
          setTimeout(() => {
            mobileLanguagePopup.classList.add('hidden');
          }, 200);
        }

        // Toggle profile dropdown
        if (isHidden) {
          mobileHeaderProfileDropdown.classList.remove('hidden');
          mobileHeaderProfileDropdown.style.display = 'block';
          mobileHeaderProfileDropdown.style.opacity = '0';
          mobileHeaderProfileDropdown.style.transform = 'translateY(-10px) scale(0.95)';
          setTimeout(() => {
            mobileHeaderProfileDropdown.style.opacity = '1';
            mobileHeaderProfileDropdown.style.transform = 'translateY(0) scale(1)';
          }, 10);
        } else {
          mobileHeaderProfileDropdown.style.opacity = '0';
          mobileHeaderProfileDropdown.style.transform = 'translateY(-10px) scale(0.95)';
          setTimeout(() => {
            mobileHeaderProfileDropdown.classList.add('hidden');
            mobileHeaderProfileDropdown.style.display = 'none';
          }, 200);
        }
      });

      // Close dropdown when clicking outside
      document.addEventListener('click', function (e) {
        const isClickInsideDropdown = mobileHeaderProfileDropdown.contains(e.target);
        const isClickOnButton = mobileHeaderProfileBtn.contains(e.target);
        if (!isClickInsideDropdown && !isClickOnButton && !mobileHeaderProfileDropdown.classList.contains('hidden')) {
          mobileHeaderProfileDropdown.style.opacity = '0';
          mobileHeaderProfileDropdown.style.transform = 'translateY(-10px) scale(0.95)';
          setTimeout(() => {
            mobileHeaderProfileDropdown.classList.add('hidden');
            mobileHeaderProfileDropdown.style.display = 'none';
          }, 200);
        }
      });

      // Prevent dropdown from closing when clicking inside
      mobileHeaderProfileDropdown.addEventListener('click', (e) => {
        e.stopPropagation();
      });
    }

    // Enhanced Language Switcher with Smooth Animations
    const languageBtn = document.getElementById('language-btn');
    const languageDropdown = document.getElementById('language-dropdown');

    if (languageBtn && languageDropdown) {
      languageBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        const isHidden = languageDropdown.classList.contains('hidden');

        // Close other dropdowns first
        const desktopProfileDropdown = document.getElementById('desktop-profile-dropdown');
        const profileChevron = document.getElementById('profile-chevron');
        if (desktopProfileDropdown && !desktopProfileDropdown.classList.contains('hidden')) {
          desktopProfileDropdown.classList.remove('show');
          setTimeout(() => {
            desktopProfileDropdown.classList.add('hidden');
          }, 200);
          if (profileChevron) {
            profileChevron.style.transform = 'rotate(0deg)';
          }
        }

        // Toggle language dropdown with animation
        if (isHidden) {
          languageDropdown.classList.remove('hidden');
          languageDropdown.style.display = 'block';
          languageDropdown.style.opacity = '0';
          languageDropdown.style.transform = 'translateY(-10px) scale(0.95)';
          setTimeout(() => {
            languageDropdown.style.opacity = '1';
            languageDropdown.style.transform = 'translateY(0) scale(1)';
          }, 10);
        } else {
          languageDropdown.style.opacity = '0';
          languageDropdown.style.transform = 'translateY(-10px) scale(0.95)';
          setTimeout(() => {
            languageDropdown.classList.add('hidden');
            languageDropdown.style.display = 'none';
          }, 200);
        }

        // Update aria-expanded for accessibility
        languageBtn.setAttribute('aria-expanded', isHidden);
      });

      // Close dropdown when clicking outside
      document.addEventListener('click', (e) => {
        const isClickInsideDropdown = languageDropdown.contains(e.target);
        const isClickOnButton = languageBtn.contains(e.target);
        if (!isClickInsideDropdown && !isClickOnButton && !languageDropdown.classList.contains('hidden')) {
          languageDropdown.style.opacity = '0';
          languageDropdown.style.transform = 'translateY(-10px) scale(0.95)';
          setTimeout(() => {
            languageDropdown.classList.add('hidden');
            languageDropdown.style.display = 'none';
          }, 200);
          languageBtn.setAttribute('aria-expanded', 'false');
        }
      });

      languageDropdown.addEventListener('click', (e) => {
        e.stopPropagation();
      });
    }

    // Mobile language switcher
    const mobileLanguageBtn = document.getElementById('mobile-language-btn');
    const mobileLanguagePopup = document.getElementById('mobile-language-popup');
    if (mobileLanguageBtn && mobileLanguagePopup) {
      mobileLanguageBtn.addEventListener('click', function (e) {
        e.preventDefault();
        e.stopPropagation();

        const isHidden = mobileLanguagePopup.classList.contains('hidden');

        // Close mobile header profile dropdown if open
        const mobileHeaderProfileDropdown = document.getElementById('mobile-header-profile-dropdown');
        if (mobileHeaderProfileDropdown && !mobileHeaderProfileDropdown.classList.contains('hidden')) {
          mobileHeaderProfileDropdown.style.opacity = '0';
          mobileHeaderProfileDropdown.style.transform = 'translateY(-10px) scale(0.95)';
          setTimeout(() => {
            mobileHeaderProfileDropdown.classList.add('hidden');
            mobileHeaderProfileDropdown.style.display = 'none';
          }, 200);
        }

        if (isHidden) {
          mobileLanguagePopup.classList.remove('hidden');
          setTimeout(() => {
            mobileLanguagePopup.classList.add('show');
          }, 10);
        } else {
          mobileLanguagePopup.classList.remove('show');
          setTimeout(() => {
            mobileLanguagePopup.classList.add('hidden');
          }, 200);
        }
      });

      document.addEventListener('click', function (e) {
        if (!mobileLanguagePopup.classList.contains('hidden') &&
          !mobileLanguageBtn.contains(e.target) &&
          !mobileLanguagePopup.contains(e.target)) {
          mobileLanguagePopup.classList.remove('show');
          setTimeout(() => {
            mobileLanguagePopup.classList.add('hidden');
          }, 200);
        }
      });
    }

    // Enhanced Language Switcher with Animations
    // Note: Language switcher functionality is already handled above with the correct IDs



    // Enhanced Desktop Profile Dropdown with Smooth Animations
    const desktopProfileBtn = document.getElementById('desktop-profile-btn');
    const desktopProfileDropdown = document.getElementById('desktop-profile-dropdown');
    const profileChevron = document.getElementById('profile-chevron');

    if (desktopProfileBtn && desktopProfileDropdown) {
      desktopProfileBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        const isHidden = desktopProfileDropdown.classList.contains('hidden');

        // Close other dropdowns first
        const languageDropdown = document.getElementById('language-dropdown');
        if (languageDropdown && !languageDropdown.classList.contains('hidden')) {
          languageDropdown.classList.remove('opacity-100', 'translate-y-0', 'scale-100');
          languageDropdown.classList.add('opacity-0', 'translate-y-2', 'scale-95');
          setTimeout(() => {
            languageDropdown.classList.add('hidden');
          }, 200);
        }

        // Toggle profile dropdown with smooth animation
        if (isHidden) {
          desktopProfileDropdown.classList.remove('hidden');
          desktopProfileDropdown.style.display = 'block';
          setTimeout(() => {
            desktopProfileDropdown.classList.add('show');
          }, 10);
        } else {
          desktopProfileDropdown.classList.remove('show');
          setTimeout(() => {
            desktopProfileDropdown.classList.add('hidden');
          }, 200);
        }

        // Update aria-expanded for accessibility
        desktopProfileBtn.setAttribute('aria-expanded', isHidden);

        // Animate chevron with smooth rotation
        if (profileChevron) {
          profileChevron.style.transform = isHidden ? 'rotate(180deg)' : 'rotate(0deg)';
        }
      });

      // Close dropdown when clicking outside
      document.addEventListener('click', (e) => {
        const isClickInsideDropdown = desktopProfileDropdown.contains(e.target);
        const isClickOnButton = desktopProfileBtn.contains(e.target);
        if (!isClickInsideDropdown && !isClickOnButton && !desktopProfileDropdown.classList.contains('hidden')) {
          desktopProfileDropdown.classList.remove('show');
          setTimeout(() => {
            desktopProfileDropdown.classList.add('hidden');
          }, 200);
          desktopProfileBtn.setAttribute('aria-expanded', 'false');
          if (profileChevron) {
            profileChevron.style.transform = 'rotate(0deg)';
          }
        }
      });

      // Prevent dropdown from closing when clicking inside
      desktopProfileDropdown.addEventListener('click', (e) => {
        e.stopPropagation();
      });
    }

    // Update current time
    function updateTime() {
      const now = new Date();
      const timeString = now.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      });
      const timeElement = document.getElementById('current-time');
      if (timeElement) {
        timeElement.textContent = timeString;
      }
    }

    // Update time immediately and then every minute
    updateTime();
    setInterval(updateTime, 60000);





  });
















</script>


</body>
</html>