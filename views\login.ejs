<!DOCTYPE html>
<html lang="<%= locale %>">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= t('auth.login_title') %> - StreamOnPod</title>
  <link rel="icon" href="/images/streamonpod-logo.png" type="image/png">
  <link rel="preload" href="/images/streamonpod-logotype.png" as="image" type="image/png">
  <link rel="alternate icon" href="/images/favicon.ico" type="image/x-icon">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@2.30.0/tabler-icons.min.css">
  <link rel="stylesheet" href="/css/styles.css">
  <script>
    tailwind.config = {
      darkMode: 'class',
      theme: {
        fontFamily: {
          'inter': ['Inter', 'sans-serif'],
          'sans': ['Inter', 'system-ui', 'sans-serif']
        },
        extend: {
          colors: {
            'primary': '#ad6610',
            'secondary': '#8b5209',
            'dark': {
              '900': '#121212',
              '800': '#252525',
              '700': '#2D2D2D',
              '600': '#3D3D3D',
              '500': '#6E6E6E',
              '400': '#8F8F8F',
              '100': '#E5E5E5',
            }
          }
        }
      }
    }
  </script>
</head>
<body class="bg-dark-900 text-white font-inter">
  <%- include('partials/language-switcher') %>

  <div class="min-h-screen flex items-center justify-center px-6">
    <div class="w-full max-w-md card-enhanced p-8">

      <div class="flex justify-center mb-8">
        <div class="streamonpod-logo-xl">
          <span class="text-white">Stream</span><span class="text-primary-accent">OnPod</span>
        </div>
      </div>

      <div class="text-center mb-6">
        <h1 class="text-2xl font-bold text-white mb-2"><%= t('auth.login_title') %></h1>
        <p class="text-gray-400"><%= t('auth.login_subtitle') %></p>
      </div>

      <% if (error) { %>
        <div id="error-message" class="bg-red-500/10 border border-red-500/20 text-red-400 px-4 py-3 rounded-lg mb-6">
          <div class="flex items-center">
            <i class="ti ti-alert-circle mr-2"></i>
            <span>
              <%= error %>
            </span>
          </div>
          <% if (error.includes('Too many login attempts')) { %>
            <% } %>
        </div>
        <% } %>

          <form id="loginForm" action="/login" method="post" class="space-y-5">
            <input type="hidden" name="_csrf" value="<%= csrfToken %>">
            <div>
              <label for="username" class="text-sm font-medium block mb-2"><%= t('auth.username') %></label>
              <div class="relative">
                <input type="text" id="username" name="username"
                  class="w-full pl-10 pr-4 py-2.5 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary"
                  placeholder="<%= t('auth.username') %>" required>
                <i class="ti ti-user absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
              </div>
            </div>
            <div class="pb-3">
              <label for="password" class="text-sm font-medium block mb-2"><%= t('auth.password') %></label>
              <div class="relative">
                <input type="password" id="password" name="password"
                  class="w-full pl-10 pr-12 py-2.5 bg-dark-700 border border-gray-600 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary"
                  placeholder="<%= t('auth.password') %>" required>
                <i class="ti ti-lock absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                <button type="button" onclick="togglePasswordVisibility()"
                  class="absolute right-3 top-1/2 -translate-y-1/2 flex items-center justify-center text-gray-400 hover:text-white transition-colors">
                  <i class="ti ti-eye text-base" id="passwordToggle"></i>
                </button>
              </div>
            </div>

            <button type="submit" id="loginButton"
              class="btn-primary-enhanced w-full py-2.5 font-medium">
              <span id="loginText"><%= t('auth.login_button') %></span>
              <span id="loginSpinner" class="hidden">
                <i class="ti ti-loader animate-spin mr-2"></i>
                Logging in...
              </span>
            </button>
          </form>

          <!-- Register Link -->
          <div class="text-center mt-6">
            <p class="text-gray-400">
              <%= t('auth.dont_have_account') %>
              <a href="/register" class="text-primary hover:underline"><%= t('auth.sign_up') %></a>
            </p>
          </div>
    </div>
  </div>
  <script>
    function togglePasswordVisibility() {
      const password = document.getElementById('password');
      const passwordToggle = document.getElementById('passwordToggle');
      if (password.type === 'password') {
        password.type = 'text';
        passwordToggle.classList.remove('ti-eye');
        passwordToggle.classList.add('ti-eye-off');
      } else {
        password.type = 'password';
        passwordToggle.classList.remove('ti-eye-off');
        passwordToggle.classList.add('ti-eye');
      }
    }

    // Simple form submission with loading state
    document.getElementById('loginForm').addEventListener('submit', function(e) {
      const loginButton = document.getElementById('loginButton');
      const loginText = document.getElementById('loginText');
      const loginSpinner = document.getElementById('loginSpinner');

      // Show loading state
      loginButton.disabled = true;
      loginText.classList.add('hidden');
      loginSpinner.classList.remove('hidden');

      // Let the form submit normally
      // The server will handle redirect
    });
  </script>

  <!-- StreamOnPod Logo Styling -->
  <style>
    .streamonpod-logo-xl {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      font-size: 2.5rem; /* 40px - lebih kecil */
      font-weight: 600;
      letter-spacing: -0.025em;
      line-height: 1;
      user-select: none;
    }

    .text-primary-accent {
      color: #ad6610;
    }

    @media (max-width: 640px) {
      .streamonpod-logo-xl {
        font-size: 2rem; /* 32px on mobile */
      }
    }
  </style>
</body>
</html>