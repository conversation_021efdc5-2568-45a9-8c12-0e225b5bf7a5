<!-- Language Switcher Component -->
<div class="fixed top-4 right-4 z-50">
  <div class="relative">
    <button id="standalone-language-btn" class="group p-3 bg-dark-800/90 backdrop-blur-sm border border-gray-700/50 rounded-xl text-gray-400 hover:text-white hover:border-primary/50 transition-all duration-200 flex items-center gap-2 shadow-lg hover:shadow-xl">
      <i class="ti ti-language text-lg group-hover:text-primary transition-colors"></i>
      <span class="text-sm font-medium"><%= getCurrentLanguage().code.toUpperCase() %></span>
      <i class="ti ti-chevron-down text-xs group-hover:text-primary transition-all duration-200 group-hover:rotate-180"></i>
    </button>

    <div id="standalone-language-dropdown" class="hidden absolute right-0 top-full mt-2 w-52 bg-dark-800/95 backdrop-blur-sm border border-gray-700/50 rounded-xl shadow-2xl z-[9999] overflow-hidden">
      <div class="p-2">
        <div class="text-xs font-medium text-gray-500 px-3 py-2 border-b border-gray-700/50 mb-1">
          <%= t('common.language') %>
        </div>
        <% getLanguages().forEach(function(lang) { %>
        <a href="<%= getLanguageUrl(lang.code) %>" 
           class="group flex items-center px-3 py-2.5 text-sm text-gray-300 hover:bg-dark-700/70 hover:text-white transition-all duration-200 rounded-lg <%= locale === lang.code ? 'bg-primary/20 text-primary border border-primary/30' : '' %>">
          <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-700/50 group-hover:bg-primary/20 transition-colors mr-3 <%= locale === lang.code ? 'bg-primary/30' : '' %>">
            <span class="text-xs font-bold <%= locale === lang.code ? 'text-primary' : 'text-gray-400 group-hover:text-primary' %>">
              <%= lang.code.toUpperCase() %>
            </span>
          </div>
          <div class="flex-1">
            <div class="font-medium"><%= lang.native %></div>
            <div class="text-xs text-gray-500"><%= lang.name %></div>
          </div>
          <% if (locale === lang.code) { %>
          <i class="ti ti-check text-primary ml-2"></i>
          <% } %>
        </a>
        <% }); %>
      </div>
    </div>
  </div>
</div>

<script>
// Standalone Language switcher functionality
document.addEventListener('DOMContentLoaded', () => {
  const languageBtn = document.getElementById('standalone-language-btn');
  const languageDropdown = document.getElementById('standalone-language-dropdown');
  
  if (languageBtn && languageDropdown) {
    languageBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      languageDropdown.classList.toggle('hidden');
      
      // Add animation
      if (!languageDropdown.classList.contains('hidden')) {
        languageDropdown.style.opacity = '0';
        languageDropdown.style.transform = 'translateY(-10px)';
        setTimeout(() => {
          languageDropdown.style.opacity = '1';
          languageDropdown.style.transform = 'translateY(0)';
        }, 10);
      }
    });
    
    document.addEventListener('click', (e) => {
      const isClickInsideDropdown = languageDropdown.contains(e.target);
      const isClickOnButton = languageBtn.contains(e.target);
      if (!isClickInsideDropdown && !isClickOnButton && !languageDropdown.classList.contains('hidden')) {
        languageDropdown.style.opacity = '0';
        languageDropdown.style.transform = 'translateY(-10px)';
        setTimeout(() => {
          languageDropdown.classList.add('hidden');
        }, 150);
      }
    });
    
    languageDropdown.addEventListener('click', (e) => {
      e.stopPropagation();
    });
  }
});
</script>

<style>
#language-dropdown {
  transition: opacity 0.15s ease-out, transform 0.15s ease-out;
}
</style>
