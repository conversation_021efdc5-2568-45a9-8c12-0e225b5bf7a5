<% layout('layout') -%>

<!-- Header -->
<div class="bg-dark-800 border-b border-gray-700 p-6 -mx-6 -mt-6 mb-6">
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-white"><%= t('referral.title') %></h1>
      <p class="text-gray-400 mt-1"><%= t('referral.subtitle') %></p>
    </div>
    <div class="flex items-center space-x-4">
      <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
        <i class="ti ti-gift text-white text-sm"></i>
      </div>
    </div>
  </div>
</div>

<!-- Referral Info Alert -->
<div class="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 mb-6">
  <div class="flex items-start space-x-3">
    <i class="ti ti-info-circle text-blue-400 text-xl mt-0.5"></i>
    <div>
      <h3 class="text-blue-400 font-medium mb-1"><%= t('referral.how_it_works.title') %></h3>
      <p class="text-gray-300 text-sm">
        <%- t('referral.how_it_works.description') %>
      </p>
      <p class="text-yellow-400 text-xs mt-2">
        <i class="ti ti-info-circle mr-1"></i>
        <%= t('referral.how_it_works.note') %>
      </p>
    </div>
  </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
  <!-- Balance -->
  <div class="card-enhanced p-6">
    <div class="flex items-center justify-between mb-4">
      <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-wallet text-green-400 text-xl"></i>
      </div>
      <span class="text-sm text-gray-400"><%= t('referral.balance') %></span>
    </div>
    <div class="text-2xl font-bold text-white mb-1">
      <%= formatIDR(stats.balance) %>
    </div>
    <p class="text-gray-400 text-sm"><%= t('referral.available_balance') %></p>
  </div>

  <!-- Clicks -->
  <div class="card-enhanced p-6">
    <div class="flex items-center justify-between mb-4">
      <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-click text-blue-400 text-xl"></i>
      </div>
      <span class="text-sm text-gray-400"><%= t('referral.total_clicks') %></span>
    </div>
    <div class="text-2xl font-bold text-white mb-1">
      <%= stats.total_clicks %>
    </div>
    <p class="text-gray-400 text-sm"><%= t('referral.total_link_clicks') %></p>
  </div>

  <!-- Pending -->
  <div class="card-enhanced p-6">
    <div class="flex items-center justify-between mb-4">
      <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-clock text-yellow-400 text-xl"></i>
      </div>
      <span class="text-sm text-gray-400"><%= t('referral.status.pending') %></span>
    </div>
    <div class="text-2xl font-bold text-white mb-1">
      <%= stats.pending_referrals %>
    </div>
    <p class="text-gray-400 text-sm"><%= t('referral.pending_referrals') %></p>
  </div>

  <!-- Success -->
  <div class="card-enhanced p-6">
    <div class="flex items-center justify-between mb-4">
      <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-check text-green-400 text-xl"></i>
      </div>
      <span class="text-sm text-gray-400"><%= t('referral.successful_referrals') %></span>
    </div>
    <div class="text-2xl font-bold text-white mb-1 text-green-400">
      <%= stats.successful_referrals %>
    </div>
    <p class="text-gray-400 text-sm"><%= t('referral.successful_referrals') %></p>
  </div>
</div>

<!-- Referral Link Section -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
  <!-- Referral Link -->
  <div class="card-enhanced p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-white"><%= t('referral.referral_link') %></h3>
      <button onclick="copyReferralLink()" class="btn-secondary text-sm">
        <i class="ti ti-copy mr-2"></i>
        <%= t('referral.copy_link') %>
      </button>
    </div>

    <div class="bg-dark-700 rounded-lg p-4 mb-4">
      <p class="text-gray-400 text-sm mb-2"><%= t('referral.your_referral_code') %>:</p>
      <div class="flex items-center space-x-2">
        <code class="text-primary font-mono text-lg"><%= stats.referral_code %></code>
        <button onclick="copyReferralCode()" class="text-gray-400 hover:text-white">
          <i class="ti ti-copy"></i>
        </button>
      </div>
    </div>

    <div class="bg-dark-700 rounded-lg p-4">
      <p class="text-gray-400 text-sm mb-2"><%= t('referral.referral_link') %>:</p>
      <div class="flex items-center space-x-2">
        <input
          type="text"
          id="referralLink"
          value="<%= referralLink %>"
          readonly
          class="flex-1 bg-transparent text-white text-sm font-mono border-none outline-none"
        >
        <button onclick="copyReferralLink()" class="text-gray-400 hover:text-white">
          <i class="ti ti-copy"></i>
        </button>
      </div>
    </div>

    <p class="text-gray-400 text-sm mt-4">
      <%= t('referral.share_link') %>
    </p>
  </div>

  <!-- Withdrawal Section -->
  <div class="card-enhanced p-6">
    <h3 class="text-lg font-semibold text-white mb-4"><%= t('referral.withdraw_balance') %></h3>

    <div class="mb-4">
      <p class="text-gray-400 text-sm mb-2"><%= t('referral.available_balance') %>:</p>
      <p class="text-2xl font-bold text-green-400"><%= formatIDR(stats.balance) %></p>
    </div>

    <% if (stats.balance >= 50000) { %>
      <button onclick="openWithdrawModal()" class="btn-primary w-full">
        <i class="ti ti-cash mr-2"></i>
        <%= t('referral.withdraw_balance') %>
      </button>
    <% } else { %>
      <div class="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4">
        <p class="text-yellow-400 text-sm">
          <i class="ti ti-info-circle mr-2"></i>
          <%= t('referral.minimum_withdrawal_info') %>
        </p>
      </div>
    <% } %>

    <p class="text-gray-400 text-xs mt-4">
      <%= t('referral.processing_info') %>
    </p>
  </div>
</div>

<!-- Withdrawal History -->
<div class="card-enhanced p-6 mb-8">
  <div class="flex items-center justify-between mb-6">
    <h3 class="text-lg font-semibold text-white"><%= t('referral.withdrawal_history') %></h3>
    <button onclick="loadWithdrawalHistory()" class="btn-secondary text-sm">
      <i class="ti ti-refresh mr-2"></i>
      <%= t('referral.refresh') %>
    </button>
  </div>

  <div id="withdrawalHistoryContent">
    <div class="text-center py-8">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      <p class="text-gray-400 mt-2"><%= t('referral.loading_withdrawal_history') %></p>
    </div>
  </div>
</div>

<!-- Earnings History -->
<div class="card-enhanced p-6 mb-8">
  <div class="flex items-center justify-between mb-6">
    <h3 class="text-lg font-semibold text-white"><%= t('referral.earnings_history') %></h3>
    <button onclick="loadEarningsHistory()" class="btn-secondary text-sm">
      <i class="ti ti-refresh mr-2"></i>
      <%= t('referral.refresh') %>
    </button>
  </div>

  <div id="earningsHistory">
    <% if (earnings && earnings.length > 0) { %>
      <div class="space-y-4">
        <% earnings.forEach(earning => { %>
          <div class="bg-dark-700 rounded-lg p-4 flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                <i class="ti ti-coins text-green-400"></i>
              </div>
              <div>
                <p class="text-white font-medium">
                  <%= t('referral.commission_from') %> <%= earning.masked_username || 'User' %>
                  <span class="text-gray-500 font-normal"><%= earning.unique_id %></span>
                </p>
                <p class="text-gray-400 text-sm">
                  <%= t('referral.referral_code') %>: <%= earning.referral_code %> •
                  <%= new Date(earning.created_at).toLocaleDateString('id-ID') %>
                </p>
              </div>
            </div>
            <div class="text-right">
              <p class="text-green-400 font-semibold">+<%= formatIDR(earning.amount) %></p>
              <p class="text-gray-400 text-sm capitalize"><%= earning.status %></p>
            </div>
          </div>
        <% }); %>
      </div>
    <% } else { %>
      <div class="text-center py-8">
        <i class="ti ti-coins text-gray-600 text-4xl mb-4"></i>
        <p class="text-gray-400"><%= t('referral.no_earnings') %></p>
        <p class="text-gray-500 text-sm mt-2"><%= t('referral.share_referral_link') %></p>
      </div>
    <% } %>
  </div>
</div>

<!-- Referral Details -->
<div class="mt-8 mb-8">
  <div class="flex flex-col lg:flex-row lg:space-x-6 space-y-6 lg:space-y-0">
    <!-- Pending Referrals -->
    <div class="flex-1 card-enhanced referral-card p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-white"><%= t('referral.pending_referrals') %></h3>
        <button onclick="loadReferralDetails()" class="btn-secondary text-sm px-3 py-1.5">
          <i class="ti ti-refresh mr-1"></i>
          <%= t('referral.refresh') %>
        </button>
      </div>

      <div id="pendingReferrals" class="min-h-[200px]">
        <div class="text-center py-12 animate-pulse-slow">
          <i class="ti ti-loader-2 text-gray-600 text-3xl mb-3 animate-spin"></i>
          <p class="text-gray-400 text-sm"><%= t('referral.loading_data') %></p>
        </div>
      </div>
    </div>

    <!-- Successful Referrals -->
    <div class="flex-1 card-enhanced referral-card p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-white"><%= t('referral.successful_referrals') %></h3>
        <button onclick="loadReferralDetails()" class="btn-secondary text-sm px-3 py-1.5">
          <i class="ti ti-refresh mr-1"></i>
          <%= t('referral.refresh') %>
        </button>
      </div>

      <div id="successfulReferrals" class="min-h-[200px]">
        <div class="text-center py-12 animate-pulse-slow">
          <i class="ti ti-loader-2 text-gray-600 text-3xl mb-3 animate-spin"></i>
          <p class="text-gray-400 text-sm"><%= t('referral.loading_data') %></p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Withdrawal Modal -->
<div id="withdrawModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-dark-800 rounded-lg p-6 w-full max-w-md">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-white"><%= t('referral.withdraw_balance') %></h3>
        <button onclick="closeWithdrawModal()" class="text-gray-400 hover:text-white">
          <i class="ti ti-x text-xl"></i>
        </button>
      </div>

      <form id="withdrawForm" onsubmit="submitWithdrawal(event)">
        <div class="space-y-4">
          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2"><%= t('referral.withdrawal_amount') %></label>
            <input
              type="number"
              name="amount"
              min="50000"
              max="<%= stats.balance %>"
              step="1000"
              required
              class="w-full bg-dark-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-primary focus:outline-none"
              placeholder="<%= t('referral.placeholders.minimum_amount') %>"
            >
          </div>

          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2"><%= t('referral.bank_name') %></label>
            <input
              type="text"
              name="bankName"
              required
              class="w-full bg-dark-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-primary focus:outline-none"
              placeholder="<%= t('referral.placeholders.bank_example') %>"
            >
          </div>

          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2"><%= t('referral.account_number') %></label>
            <input
              type="text"
              name="accountNumber"
              required
              class="w-full bg-dark-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-primary focus:outline-none"
              placeholder="<%= t('referral.placeholders.account_number_placeholder') %>"
            >
          </div>

          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2"><%= t('referral.account_name') %></label>
            <input
              type="text"
              name="accountName"
              required
              class="w-full bg-dark-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-primary focus:outline-none"
              placeholder="<%= t('referral.placeholders.account_name_placeholder') %>"
            >
          </div>
        </div>

        <div class="flex space-x-3 mt-6">
          <button type="button" onclick="closeWithdrawModal()" class="flex-1 btn-secondary">
            <%= t('referral.cancel') %>
          </button>
          <button type="submit" class="flex-1 btn-primary" id="withdrawSubmitBtn">
            <i class="ti ti-cash mr-2" id="withdrawSubmitIcon"></i>
            <span id="withdrawSubmitText"><%= t('referral.submit_withdrawal') %></span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
function copyReferralCode() {
  const code = '<%= stats.referral_code %>';
  navigator.clipboard.writeText(code).then(() => {
    showNotification('<%= t("referral.referral_code_copied") %>', 'success');
  });
}

function copyReferralLink() {
  const link = document.getElementById('referralLink').value;
  navigator.clipboard.writeText(link).then(() => {
    showNotification('<%= t("referral.referral_link_copied") %>', 'success');
  });
}

function openWithdrawModal() {
  document.getElementById('withdrawModal').classList.remove('hidden');
}

function closeWithdrawModal() {
  document.getElementById('withdrawModal').classList.add('hidden');
  document.getElementById('withdrawForm').reset();
}

async function submitWithdrawal(event) {
  event.preventDefault();

  // Show loading state
  const submitBtn = document.getElementById('withdrawSubmitBtn');
  const submitIcon = document.getElementById('withdrawSubmitIcon');
  const submitText = document.getElementById('withdrawSubmitText');

  submitBtn.disabled = true;
  submitIcon.className = 'ti ti-loader-2 mr-2 animate-spin';
  submitText.textContent = '<%= t("referral.processing") %>';

  const formData = new FormData(event.target);
  const data = {
    amount: formData.get('amount'),
    bankName: formData.get('bankName'),
    accountNumber: formData.get('accountNumber'),
    accountName: formData.get('accountName')
  };

  try {
    const response = await fetch('/referral/api/withdraw', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': '<%= csrfToken %>'
      },
      body: JSON.stringify(data)
    });

    const result = await response.json();

    if (result.success) {
      // Show success state
      submitIcon.className = 'ti ti-check mr-2';
      submitText.textContent = '<%= t("referral.success") %>';

      showNotification('<%= t("referral.withdrawal_submitted") %>', 'success');
      closeWithdrawModal();

      // Reload withdrawal history and balance
      await loadWithdrawalHistory();
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } else {
      showNotification(result.error || '<%= t("referral.withdrawal_failed") %>', 'error');
    }
  } catch (error) {
    console.error('Withdrawal error:', error);
    showNotification('<%= t("referral.withdrawal_error") %>', 'error');
  } finally {
    // Reset button state
    setTimeout(() => {
      submitBtn.disabled = false;
      submitIcon.className = 'ti ti-cash mr-2';
      submitText.textContent = '<%= t("referral.submit_withdrawal") %>';
    }, 2000);
  }
}

async function loadEarningsHistory() {
  try {
    const response = await fetch('/referral/api/earnings');
    const result = await response.json();

    if (result.success) {
      // Update earnings history display
      // Implementation depends on your notification system
      showNotification('<%= t("referral.earnings_updated") %>', 'success');
    }
  } catch (error) {
    console.error('Load earnings error:', error);
    showNotification('<%= t("referral.earnings_load_failed") %>', 'error');
  }
}

async function loadReferralDetails() {
  try {
    // Show loading state
    const pendingContainer = document.getElementById('pendingReferrals');
    const successfulContainer = document.getElementById('successfulReferrals');

    pendingContainer.innerHTML = `
      <div class="text-center py-12 animate-pulse-slow">
        <i class="ti ti-loader-2 text-gray-600 text-3xl mb-3 animate-spin"></i>
        <p class="text-gray-400 text-sm"><%= t("referral.loading_data") %></p>
      </div>
    `;

    successfulContainer.innerHTML = `
      <div class="text-center py-12 animate-pulse-slow">
        <i class="ti ti-loader-2 text-gray-600 text-3xl mb-3 animate-spin"></i>
        <p class="text-gray-400 text-sm"><%= t("referral.loading_data") %></p>
      </div>
    `;

    const response = await fetch('/referral/api/referral-details');
    const result = await response.json();

    if (result.success) {
      const { pending, successful } = result.data;

      // Update pending referrals
      const pendingContainer = document.getElementById('pendingReferrals');
      if (pending.length > 0) {
        pendingContainer.innerHTML = `
          <div class="space-y-2 max-h-60 overflow-y-auto pr-2">
            ${pending.map(ref => `
              <div class="bg-dark-700/50 rounded-lg p-3 border border-gray-700/50 hover:border-yellow-500/30 transition-colors">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                      <i class="ti ti-clock text-yellow-400 text-sm"></i>
                    </div>
                    <div class="min-w-0">
                      <p class="text-white font-medium text-sm truncate">
                        ${ref.masked_username}
                        <span class="text-gray-500 font-normal">${ref.unique_id}</span>
                      </p>
                      <p class="text-gray-400 text-xs">
                        ${new Date(ref.signup_date).toLocaleDateString('id-ID')}
                      </p>
                    </div>
                  </div>
                  <div class="text-right flex-shrink-0 ml-2">
                    <p class="text-yellow-400 text-xs font-medium"><%= t("referral.not_purchased_plan") %></p>
                  </div>
                </div>
              </div>
            `).join('')}
          </div>
        `;
      } else {
        pendingContainer.innerHTML = `
          <div class="text-center py-12">
            <i class="ti ti-clock text-gray-600 text-3xl mb-3"></i>
            <p class="text-gray-400 text-sm"><%= t("referral.no_pending_referrals") %></p>
            <p class="text-gray-500 text-xs mt-1"><%= t("referral.share_for_more_signups") %></p>
          </div>
        `;
      }

      // Update successful referrals
      const successfulContainer = document.getElementById('successfulReferrals');
      if (successful.length > 0) {
        successfulContainer.innerHTML = `
          <div class="space-y-2 max-h-60 overflow-y-auto pr-2">
            ${successful.map(ref => `
              <div class="bg-dark-700/50 rounded-lg p-3 border border-gray-700/50 hover:border-green-500/30 transition-colors">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                      <i class="ti ti-check text-green-400 text-sm"></i>
                    </div>
                    <div class="min-w-0">
                      <p class="text-white font-medium text-sm truncate">
                        ${ref.masked_username}
                        <span class="text-gray-500 font-normal">${ref.unique_id}</span>
                      </p>
                      <p class="text-gray-400 text-xs">
                        ${new Date(ref.completed_date).toLocaleDateString('id-ID')}
                      </p>
                    </div>
                  </div>
                  <div class="text-right flex-shrink-0 ml-2">
                    <p class="text-green-400 text-xs font-semibold">+${formatIDRClient(ref.commission_amount)}</p>
                  </div>
                </div>
              </div>
            `).join('')}
          </div>
        `;
      } else {
        successfulContainer.innerHTML = `
          <div class="text-center py-12">
            <i class="ti ti-check text-gray-600 text-3xl mb-3"></i>
            <p class="text-gray-400 text-sm"><%= t("referral.no_successful_referrals") %></p>
            <p class="text-gray-500 text-xs mt-1"><%= t("referral.wait_for_purchases") %></p>
          </div>
        `;
      }
    }
  } catch (error) {
    console.error('Load referral details error:', error);
    showNotification('<%= t("referral.referral_details_failed") %>', 'error');
  }
}

function formatIDRClient(amount) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0
  }).format(amount);
}

function showNotification(message, type) {
  // Use the proper notification system
  if (window.showNotification && window.showNotification[type]) {
    window.showNotification[type]('Referral', message);
  } else {
    // Fallback to alert if notification system is not available
    alert(message);
  }
}

// Load withdrawal history
async function loadWithdrawalHistory() {
  try {
    const response = await fetch('/referral/api/withdrawals');
    const result = await response.json();

    const container = document.getElementById('withdrawalHistoryContent');

    if (result.success && result.data.length > 0) {
      let historyHtml = `
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead>
              <tr class="border-b border-gray-700">
                <th class="text-left py-3 px-4 text-gray-300 font-medium"><%= t("admin_referral.date") %></th>
                <th class="text-left py-3 px-4 text-gray-300 font-medium"><%= t("admin_referral.amount") %></th>
                <th class="text-left py-3 px-4 text-gray-300 font-medium"><%= t("admin_referral.bank") %></th>
                <th class="text-left py-3 px-4 text-gray-300 font-medium"><%= t("admin_referral.status") %></th>
                <th class="text-left py-3 px-4 text-gray-300 font-medium"><%= t("admin_referral.note") %></th>
              </tr>
            </thead>
            <tbody>
      `;

      result.data.forEach(withdrawal => {
        const statusClass = withdrawal.status === 'pending' ? 'bg-yellow-900/20 text-yellow-400 border-yellow-500/30' :
                           withdrawal.status === 'approved' ? 'bg-green-900/20 text-green-400 border-green-500/30' :
                           'bg-red-900/20 text-red-400 border-red-500/30';

        const statusIcon = withdrawal.status === 'pending' ? 'ti-clock' :
                          withdrawal.status === 'approved' ? 'ti-check' : 'ti-x';

        historyHtml += `
          <tr class="border-b border-gray-800">
            <td class="py-4 px-4">
              <div>
                <p class="text-white text-sm">${new Date(withdrawal.requested_at).toLocaleDateString('id-ID')}</p>
                <p class="text-gray-400 text-xs">${new Date(withdrawal.requested_at).toLocaleTimeString('id-ID')}</p>
              </div>
            </td>
            <td class="py-4 px-4">
              <p class="text-white font-semibold">Rp ${withdrawal.amount.toLocaleString('id-ID')}</p>
            </td>
            <td class="py-4 px-4">
              <div>
                <p class="text-white">${withdrawal.bank_name}</p>
                <p class="text-gray-400 text-sm font-mono">${withdrawal.account_number}</p>
                <p class="text-gray-400 text-xs">${withdrawal.account_name}</p>
              </div>
            </td>
            <td class="py-4 px-4">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClass} border">
                <i class="ti ${statusIcon} mr-1"></i>
                ${withdrawal.status === 'pending' ? '<%= t("referral.status.pending") %>' :
                  withdrawal.status === 'approved' ? '<%= t("referral.status.approved") %>' : '<%= t("referral.status.rejected") %>'}
              </span>
            </td>
            <td class="py-4 px-4">
              <div class="text-gray-400 text-sm">
                ${withdrawal.admin_notes || '-'}
                ${withdrawal.processed_at ? `<br><span class="text-xs"><%= t("admin_referral.processed") %>: ${new Date(withdrawal.processed_at).toLocaleDateString('id-ID')}</span>` : ''}
              </div>
            </td>
          </tr>
        `;
      });

      historyHtml += `
            </tbody>
          </table>
        </div>
      `;

      container.innerHTML = historyHtml;
    } else {
      container.innerHTML = `
        <div class="text-center py-12">
          <i class="ti ti-cash text-gray-600 text-4xl mb-4"></i>
          <p class="text-gray-400 text-lg"><%= t("referral.no_withdrawal_history") %></p>
          <p class="text-gray-500 text-sm mt-2"><%= t("referral.withdrawal_history_appear") %></p>
        </div>
      `;
    }
  } catch (error) {
    console.error('Load withdrawal history error:', error);
    document.getElementById('withdrawalHistoryContent').innerHTML = `
      <div class="text-center py-8">
        <i class="ti ti-alert-circle text-red-400 text-4xl mb-4"></i>
        <p class="text-red-400"><%= t("referral.withdrawal_history_failed") %></p>
        <button onclick="loadWithdrawalHistory()" class="btn-secondary mt-4">
          <i class="ti ti-refresh mr-2"></i>
          <%= t("referral.try_again") %>
        </button>
      </div>
    `;
  }
}

// Load referral details when page loads
document.addEventListener('DOMContentLoaded', function() {
  loadReferralDetails();
  loadWithdrawalHistory();
});
</script>

<style>
/* Custom scrollbar for referral lists */
.max-h-60::-webkit-scrollbar {
  width: 4px;
}

.max-h-60::-webkit-scrollbar-track {
  background: rgba(55, 65, 81, 0.3);
  border-radius: 2px;
}

.max-h-60::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.max-h-60::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* Ensure equal height cards */
.referral-card {
  min-height: 280px;
}

/* Better spacing for mobile */
@media (max-width: 1024px) {
  .card-enhanced {
    padding: 1rem;
  }

  .flex.lg\\:space-x-6 {
    gap: 1.5rem;
  }
}

/* Ensure proper spacing on desktop */
@media (min-width: 1024px) {
  .flex.lg\\:space-x-6 {
    gap: 1.5rem;
  }
}

/* Loading animation */
@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
