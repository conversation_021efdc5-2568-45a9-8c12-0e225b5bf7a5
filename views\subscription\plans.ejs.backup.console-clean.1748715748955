<% layout('layout') -%>

<!-- Midtrans Snap Script -->
<% if (typeof midtransClientKey !== 'undefined' && midtransClientKey) { %>
<script type="text/javascript"
  src="https://app.sandbox.midtrans.com/snap/snap.js"
  data-client-key="<%= midtransClientKey %>"
  onload="console.log('✅ Midtrans Snap script loaded successfully')"
  onerror="console.error('❌ Failed to load Midtrans Snap script')"></script>
<% } else { %>
<script>console.warn('⚠️ Midtrans client key not available');</script>
<% } %>

<!-- Header -->
<div class="bg-dark-800 border-b border-gray-700 p-6 -mx-6 -mt-6 mb-6">
  <div class="max-w-7xl mx-auto">
    <h1 class="text-3xl font-bold text-white mb-2"><%= t('subscription.choose_plan_title') %></h1>
    <p class="text-gray-400"><%= t('subscription.choose_plan_subtitle') %></p>
  </div>
</div>

<!-- Current Plan Info -->
<% if (typeof currentSubscription !== 'undefined' && currentSubscription) { %>
  <div class="bg-primary/10 border border-primary/20 rounded-lg p-6 mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-semibold text-primary"><%= t('subscription.current_plan') %>: <%= currentSubscription.plan_name %></h3>
          <% if (typeof quotaInfo !== 'undefined' && quotaInfo) { %>
            <p class="text-gray-300">
              <%= t('subscription.streaming_usage') %>: <%= quotaInfo.streaming.current %>/<%= quotaInfo.streaming.max %> <%= t('subscription.slots_used') %> |
              <%= t('subscription.storage_usage') %>: <%= quotaInfo.storage.current %><%= quotaInfo.storage.unit %>/<%= quotaInfo.storage.max %><%= quotaInfo.storage.unit %> <%= t('subscription.used') %> (<%= quotaInfo.storage.percentage %>%)
            </p>
          <% } %>
        </div>
        <div class="text-right">
          <p class="text-gray-400 text-sm"><%= t('subscription.expires') %>: <%= new Date(currentSubscription.end_date).toLocaleDateString() %></p>
        </div>
    </div>
  </div>
<% } %>

<!-- Pricing Cards -->
<div class="max-w-7xl mx-auto px-6">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <% plans.forEach(function(plan) { %>
        <div class="bg-dark-800 rounded-lg border <%= plan.name === 'Pro' ? 'border-primary' : 'border-gray-700' %> relative overflow-hidden">
          <% if (plan.name === 'Pro') { %>
            <div class="absolute top-0 left-0 right-0 bg-primary text-white text-center py-2 text-sm font-medium">
              <%= t('subscription.most_popular') %>
            </div>
          <% } %>

          <div class="p-6 <%= plan.name === 'Pro' ? 'pt-12' : '' %>">
            <!-- Plan Header -->
            <div class="text-center mb-6">
              <h3 class="text-xl font-bold text-white mb-2"><%= plan.name %></h3>
              <div class="mb-4">
                <% if (plan.price === 0) { %>
                  <span class="text-3xl font-bold text-white">Gratis</span>
                <% } else { %>
                  <div class="text-center">
                    <span class="text-3xl font-bold text-white">Rp<%= plan.price.toLocaleString('id-ID') %></span>
                    <span class="text-gray-400">/<%= plan.billing_period %></span>
                  </div>
                <% } %>
              </div>
            </div>

            <!-- Features -->
            <div class="space-y-3 mb-6">
              <div class="flex items-center text-sm">
                <i class="ti ti-check text-green-400 mr-2"></i>
                <span class="text-gray-300">
                  <%= plan.max_streaming_slots === -1 ? t('subscription.unlimited') : plan.max_streaming_slots %> <%= plan.max_streaming_slots !== 1 ? t('subscription.streaming_slots') : t('subscription.streaming_slot') %>
                </span>
              </div>
              <div class="flex items-center text-sm">
                <i class="ti ti-check text-green-400 mr-2"></i>
                <span class="text-gray-300">
                  <% if (plan.max_storage_gb <= 1) { %>
                    <%= Math.round(plan.max_storage_gb * 1024) %>MB <%= t('subscription.storage') %>
                  <% } else { %>
                    <%= plan.max_storage_gb %>GB <%= t('subscription.storage') %>
                  <% } %>
                </span>
              </div>
              <% if (plan.features) { %>
                <% try { %>
                  <% var features = typeof plan.features === 'string' ? JSON.parse(plan.features) : plan.features; %>
                  <% features.forEach(function(feature) { %>
                    <div class="flex items-center text-sm">
                      <i class="ti ti-check text-green-400 mr-2"></i>
                      <span class="text-gray-300"><%= feature %></span>
                    </div>
                  <% }); %>
                <% } catch(e) { %>
                  <div class="flex items-center text-sm">
                    <i class="ti ti-check text-green-400 mr-2"></i>
                    <span class="text-gray-300"><%= t('subscription.basic_features') %></span>
                  </div>
                <% } %>
              <% } %>
            </div>

            <!-- Action Button -->
            <div class="text-center">
              <% if (typeof currentSubscription !== 'undefined' && currentSubscription && currentSubscription.plan_name === plan.name) { %>
                <button class="w-full bg-gray-600 text-gray-300 py-2 px-4 rounded-lg cursor-not-allowed" disabled>
                  <%= t('subscription.current_plan') %>
                </button>
              <% } else if (plan.price === 0) { %>
                <button onclick="subscribeToPlan('<%= plan.id %>', this)" class="w-full bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors">
                  <%= t('subscription.get_started') %>
                </button>
              <% } else { %>
                <button onclick="subscribeToPlan('<%= plan.id %>', this)" class="w-full <%= plan.name === 'Pro' ? 'bg-primary hover:bg-secondary' : 'bg-primary hover:bg-secondary' %> text-white py-2 px-4 rounded-lg transition-colors">
                  <%= typeof currentSubscription !== 'undefined' && currentSubscription ? t('subscription.upgrade') : t('subscription.subscribe') %>
                </button>
              <% } %>
            </div>
          </div>
        </div>
      <% }); %>
  </div>

  <!-- FAQ Section -->
  <div class="mt-16">
      <h2 class="text-2xl font-bold text-white text-center mb-8"><%= t('subscription.faq_title') %></h2>
      <div class="max-w-3xl mx-auto space-y-4">
        <div class="bg-dark-800 rounded-lg border border-gray-700">
          <button class="w-full text-left p-6 focus:outline-none" onclick="toggleFAQ(this)">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-white"><%= t('subscription.faq_change_plan_q') %></h3>
              <i class="ti ti-chevron-down text-gray-400 transform transition-transform"></i>
            </div>
          </button>
          <div class="hidden px-6 pb-6">
            <p class="text-gray-300"><%= t('subscription.faq_change_plan_a') %></p>
          </div>
        </div>

        <div class="bg-dark-800 rounded-lg border border-gray-700">
          <button class="w-full text-left p-6 focus:outline-none" onclick="toggleFAQ(this)">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-white"><%= t('subscription.faq_exceed_limits_q') %></h3>
              <i class="ti ti-chevron-down text-gray-400 transform transition-transform"></i>
            </div>
          </button>
          <div class="hidden px-6 pb-6">
            <p class="text-gray-300"><%= t('subscription.faq_exceed_limits_a') %></p>
          </div>
        </div>

        <div class="bg-dark-800 rounded-lg border border-gray-700">
          <button class="w-full text-left p-6 focus:outline-none" onclick="toggleFAQ(this)">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-white"><%= t('subscription.faq_subscription_management_q') %></h3>
              <i class="ti ti-chevron-down text-gray-400 transform transition-transform"></i>
            </div>
          </button>
          <div class="hidden px-6 pb-6">
            <p class="text-gray-300"><%= t('subscription.faq_subscription_management_a') %></p>
          </div>
        </div>
    </div>
  </div>
</div>

<script>
  // Translation variables for JavaScript
  const translations = {
    subscribeSuccess: '<%= t("subscription.subscribe_success") %>',
    subscribeError: '<%= t("subscription.subscribe_error") %>',
    subscribeFailed: '<%= t("subscription.subscribe_failed") %>'
  };

  // Check Midtrans availability on page load
  document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 Checking Midtrans Snap availability on page load...');
    console.log('typeof snap:', typeof snap);
    console.log('window.snap:', window.snap);

    if (typeof snap !== 'undefined') {
      console.log('✅ Midtrans Snap is available');
    } else {
      console.warn('⚠️ Midtrans Snap is not available');
    }
  });

  function toggleFAQ(button) {
    const content = button.nextElementSibling;
    const icon = button.querySelector('i');

    content.classList.toggle('hidden');
    icon.classList.toggle('rotate-180');
  }

  async function subscribeToPlan(planId, buttonElement) {
    try {
      console.log('🚀 Starting subscription process for plan:', planId);
      console.log('🔘 Button element:', buttonElement);

      // First check if plan requires payment
      console.log('📡 Checking subscription requirements...');
      const response = await fetch('/subscription/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planId })
      });

      console.log('📡 Subscription response status:', response.status);
      const result = await response.json();
      console.log('📡 Subscription response data:', result);

      if (result.success) {
        if (result.requires_payment) {
          console.log('💰 Payment required, proceeding to payment creation...');
          // Plan requires payment, create payment transaction
          await createPayment(planId, buttonElement);
        } else {
          console.log('🆓 Free plan, subscription created directly');
          // Free plan, subscription created directly
          alert(translations.subscribeSuccess);
          window.location.reload();
        }
      } else {
        console.log('❌ Subscription failed:', result.error);
        alert(translations.subscribeError + ': ' + result.error);
      }
    } catch (error) {
      console.error('❌ Subscription error:', error);
      alert(translations.subscribeFailed);
    }
  }

  async function createPayment(planId, button) {
    try {
      console.log('🔄 Creating payment for plan:', planId);

      // Show loading state
      const originalText = button.textContent;
      button.textContent = 'Processing...';
      button.disabled = true;

      console.log('📡 Sending payment request...');
      const response = await fetch('/payment/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planId })
      });

      console.log('📡 Response status:', response.status);
      const result = await response.json();
      console.log('📡 Response data:', result);

      if (result.success) {
        console.log('✅ Payment creation successful');
        console.log('🎫 Snap token:', result.snap_token);
        console.log('🔗 Redirect URL:', result.redirect_url);

        // Check if Snap is available
        if (typeof snap !== 'undefined' && result.snap_token) {
          console.log('🔄 Using Snap popup for payment...');

          // Store order_id for status checking
          const orderId = result.order_id;

          // Restore button state before opening Snap
          button.textContent = originalText;
          button.disabled = false;

          // Open Snap popup
          snap.pay(result.snap_token, {
            onSuccess: function(snapResult) {
              console.log('✅ Payment successful:', snapResult);
              alert('Pembayaran berhasil! Menunggu konfirmasi sistem...');
              // Use the order_id from payment creation response
              checkPaymentStatus(orderId, 3000);
            },
            onPending: function(snapResult) {
              console.log('⏳ Payment pending:', snapResult);
              alert('Pembayaran sedang diproses. Silakan tunggu konfirmasi sistem...');
              // Use the order_id from payment creation response
              checkPaymentStatus(orderId, 5000);
            },
            onError: function(snapResult) {
              console.log('❌ Payment error:', snapResult);
              alert('Pembayaran gagal. Silakan coba lagi.');
            },
            onClose: function() {
              console.log('🔒 Payment popup closed');
              // User closed the popup, no action needed
            }
          });
        } else {
          // Fallback to redirect method if Snap is not available
          console.log('⚠️ Snap not available, using redirect method...');
          alert('Membuka halaman pembayaran Midtrans...');

          // Open payment page in new tab
          const paymentWindow = window.open(result.redirect_url, '_blank');

          // Restore button state
          button.textContent = originalText;
          button.disabled = false;

          // Check if window was blocked
          if (!paymentWindow) {
            alert('Pop-up diblokir! Silakan klik link berikut untuk melanjutkan pembayaran:\n\n' + result.redirect_url);
          } else {
            // Show message to user
            alert('Halaman pembayaran telah dibuka di tab baru. Setelah pembayaran selesai, refresh halaman ini.');
          }
        }
      } else {
        console.log('❌ Payment creation failed:', result.error);
        alert('Failed to create payment: ' + result.error);
        // Restore button state on error
        button.textContent = originalText;
        button.disabled = false;
      }

    } catch (error) {
      console.error('Payment creation error:', error);
      alert('Failed to create payment. Please try again.');

      // Restore button state
      if (button) {
        button.textContent = button.getAttribute('data-original-text') || 'Subscribe';
        button.disabled = false;
      }
    }
  }

  // Function to check payment status after Snap callback
  async function checkPaymentStatus(orderId, delay = 3000) {
    console.log('🔍 Checking payment status for order:', orderId);

    // Wait for the specified delay to allow webhook processing
    setTimeout(async () => {
      try {
        const response = await fetch(`/payment/status/${orderId}`);
        const result = await response.json();

        console.log('📊 Payment status result:', result);

        if (result.success) {
          if (result.status === 'success') {
            alert('Pembayaran berhasil dikonfirmasi! Subscription Anda telah diaktivasi.');
            window.location.reload();
          } else if (result.status === 'pending') {
            alert('Pembayaran sedang diproses. Halaman akan dimuat ulang untuk melihat status terbaru.');
            window.location.reload();
          } else {
            alert('Status pembayaran: ' + result.status + '. Halaman akan dimuat ulang.');
            window.location.reload();
          }
        } else {
          console.warn('⚠️ Could not verify payment status, reloading page...');
          window.location.reload();
        }
      } catch (error) {
        console.error('❌ Error checking payment status:', error);
        alert('Tidak dapat memverifikasi status pembayaran. Halaman akan dimuat ulang.');
        window.location.reload();
      }
    }, delay);
  }


</script>
