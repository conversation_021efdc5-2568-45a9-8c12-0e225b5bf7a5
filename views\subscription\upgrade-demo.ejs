<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Prorated Upgrade - StreamOnPod</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/prorated-upgrade.css">
    
    <!-- Midtrans Snap -->
    <script type="text/javascript" src="https://app.sandbox.midtrans.com/snap/snap.js" data-client-key="YOUR_CLIENT_KEY"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">🔄 Demo Sistem Prorated Upgrade</h1>
                <p class="text-center text-muted mb-5">
                    Sistem upgrade yang adil dengan perhitungan prorated dan biaya admin 1%
                </p>
            </div>
        </div>

        <!-- Current Plan Status -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">📋 Status Plan Saat Ini</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Plan Aktif:</strong> PodLite<br>
                                <strong>Harga:</strong> Rp 24.900/bulan<br>
                                <strong>Masa Aktif:</strong> 20 hari lagi
                            </div>
                            <div class="col-md-6">
                                <strong>Fitur:</strong><br>
                                • 1 Streaming Slot<br>
                                • 1GB Storage<br>
                                • Basic Support
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Available Plans -->
        <div class="row">
            <div class="col-12">
                <h3 class="mb-4">📦 Pilihan Upgrade Plan</h3>
            </div>
        </div>

        <div class="row">
            <!-- PodFlow Plan -->
            <div class="col-md-6 mb-4">
                <div class="card plan-card h-100">
                    <div class="card-header bg-warning text-dark text-center">
                        <h5 class="mb-0">PodFlow</h5>
                        <div class="plan-price">
                            <span class="currency">Rp</span> 49.900
                            <span class="period">/bulan</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li>✅ 3 Streaming Slots</li>
                            <li>✅ 5GB Storage</li>
                            <li>✅ Advanced Settings</li>
                            <li>✅ Priority Support</li>
                            <li>✅ Analytics Dashboard</li>
                        </ul>
                        
                        <div id="podflow-calculation" class="mt-3"></div>
                        
                        <button class="btn btn-upgrade w-100 mt-3" 
                                onclick="calculateAndShowUpgrade('podflow-plan-id', 'PodFlow')">
                            🔄 Hitung Upgrade
                        </button>
                        
                        <button class="btn btn-success w-100 mt-2" 
                                onclick="processUpgrade('podflow-plan-id', 'PodFlow')" 
                                style="display: none;" 
                                id="podflow-upgrade-btn">
                            💳 Upgrade Sekarang
                        </button>
                    </div>
                </div>
            </div>

            <!-- PodPrime Plan -->
            <div class="col-md-6 mb-4">
                <div class="card plan-card h-100">
                    <div class="upgrade-badge">POPULER</div>
                    <div class="card-header bg-success text-white text-center">
                        <h5 class="mb-0">PodPrime</h5>
                        <div class="plan-price">
                            <span class="currency">Rp</span> 99.900
                            <span class="period">/bulan</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li>✅ 10 Streaming Slots</li>
                            <li>✅ 20GB Storage</li>
                            <li>✅ Advanced Settings</li>
                            <li>✅ Premium Support</li>
                            <li>✅ Analytics Dashboard</li>
                            <li>✅ Custom Branding</li>
                            <li>✅ API Access</li>
                        </ul>
                        
                        <div id="podprime-calculation" class="mt-3"></div>
                        
                        <button class="btn btn-upgrade w-100 mt-3" 
                                onclick="calculateAndShowUpgrade('podprime-plan-id', 'PodPrime')">
                            🔄 Hitung Upgrade
                        </button>
                        
                        <button class="btn btn-success w-100 mt-2" 
                                onclick="processUpgrade('podprime-plan-id', 'PodPrime')" 
                                style="display: none;" 
                                id="podprime-upgrade-btn">
                            💳 Upgrade Sekarang
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- How It Works -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">🤔 Bagaimana Sistem Prorated Upgrade Bekerja?</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>📊 Perhitungan Prorated:</h6>
                                <ol>
                                    <li>Hitung nilai sisa hari dari plan lama</li>
                                    <li>Kurangi dari harga plan baru</li>
                                    <li>Tambahkan biaya admin 1%</li>
                                    <li>User bayar selisihnya</li>
                                    <li>Masa aktif tetap sampai tanggal yang sama</li>
                                </ol>
                            </div>
                            <div class="col-md-6">
                                <h6>💡 Contoh Perhitungan:</h6>
                                <div class="calculation-example">
                                    <table class="calculation-table">
                                        <tr>
                                            <td>Plan lama (20 hari sisa)</td>
                                            <td class="amount">Rp 16.600</td>
                                        </tr>
                                        <tr>
                                            <td>Plan baru PodPrime</td>
                                            <td class="amount">Rp 99.900</td>
                                        </tr>
                                        <tr>
                                            <td>Kredit plan lama</td>
                                            <td class="amount savings">-Rp 16.600</td>
                                        </tr>
                                        <tr>
                                            <td>Harga upgrade</td>
                                            <td class="amount">Rp 83.300</td>
                                        </tr>
                                        <tr>
                                            <td>Biaya admin (1%)</td>
                                            <td class="amount">Rp 833</td>
                                        </tr>
                                        <tr class="total-row">
                                            <td><strong>Total Pembayaran</strong></td>
                                            <td class="amount"><strong>Rp 84.133</strong></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Benefits -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-upgrade alert-success">
                    <h6>✅ Keuntungan Sistem Prorated Upgrade:</h6>
                    <ul class="mb-0">
                        <li><strong>Adil untuk User:</strong> Tidak kehilangan nilai yang sudah dibayar</li>
                        <li><strong>Mencegah Eksploitasi:</strong> User tidak bisa "gaming" sistem untuk mendapat plan mahal dengan harga murah</li>
                        <li><strong>Transparan:</strong> Perhitungan jelas dan mudah dipahami</li>
                        <li><strong>Konsisten:</strong> Masa aktif tetap sampai tanggal yang sama</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Prorated Upgrade Handler -->
    <script src="/js/prorated-upgrade.js"></script>
    
    <script>
        // Demo functions
        async function calculateAndShowUpgrade(planId, planName) {
            try {
                // Mock calculation for demo
                const mockCalculation = {
                    isUpgrade: true,
                    currentPlan: 'PodLite',
                    newPlan: planName,
                    remainingDays: 20,
                    remainingValue: 16600,
                    newPlanPrice: planName === 'PodFlow' ? 49900 : 99900,
                    upgradePrice: planName === 'PodFlow' ? 33300 : 83300,
                    adminFee: planName === 'PodFlow' ? 333 : 833,
                    totalPayment: planName === 'PodFlow' ? 33633 : 84133,
                    savings: 16600,
                    // Formatted versions
                    savingsFormatted: 'Rp 16.600',
                    upgradePriceFormatted: planName === 'PodFlow' ? 'Rp 33.300' : 'Rp 83.300',
                    totalPaymentFormatted: planName === 'PodFlow' ? 'Rp 33.633' : 'Rp 84.133',
                    newPlanPriceFormatted: planName === 'PodFlow' ? 'Rp 49.900' : 'Rp 99.900',
                    adminFeeFormatted: planName === 'PodFlow' ? 'Rp 333' : 'Rp 833'
                };

                const containerId = planName.toLowerCase() + '-calculation';
                window.proratedUpgradeHandler.displayCalculation(mockCalculation, containerId);
                
                // Show upgrade button
                const upgradeBtn = document.getElementById(planName.toLowerCase() + '-upgrade-btn');
                if (upgradeBtn) {
                    upgradeBtn.style.display = 'block';
                }
                
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }

        async function processUpgrade(planId, planName) {
            try {
                alert(`Demo: Memproses upgrade ke ${planName}...\n\nDalam implementasi nyata, ini akan membuka Midtrans Snap untuk pembayaran.`);
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }
    </script>
</body>
</html>
