<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Midtrans Integration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { border-color: #4CAF50; background-color: #f9fff9; }
        .error { border-color: #f44336; background-color: #fff9f9; }
        .warning { border-color: #ff9800; background-color: #fffaf0; }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #45a049; }
        button:disabled { background-color: #cccccc; cursor: not-allowed; }
        .log {
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Midtrans Integration</h1>
        
        <div class="test-section">
            <h3>1. Environment Check</h3>
            <p><strong>Client Key:</strong> <span id="clientKey">Loading...</span></p>
            <p><strong>Environment:</strong> <span id="environment">Loading...</span></p>
        </div>

        <div class="test-section">
            <h3>2. Midtrans Snap Script</h3>
            <p><strong>Script Status:</strong> <span id="scriptStatus">Loading...</span></p>
            <p><strong>Snap Object:</strong> <span id="snapObject">Loading...</span></p>
        </div>

        <div class="test-section">
            <h3>3. Test Payment</h3>
            <button onclick="testPayment()" id="testBtn">Test Payment (Rp150,000)</button>
            <p><em>This will create a test payment for Basic plan</em></p>
        </div>

        <div class="test-section">
            <h3>4. Debug Log</h3>
            <div id="debugLog" class="log">Initializing...\n</div>
            <button onclick="clearLog()">Clear Log</button>
        </div>
    </div>

    <!-- Midtrans Snap Script -->
    <% if (typeof midtransClientKey !== 'undefined' && midtransClientKey) { %>
    <% const isProduction = process.env.MIDTRANS_IS_PRODUCTION === 'true'; %>
    <% const snapUrl = isProduction ? 'https://app.midtrans.com/snap/snap.js' : 'https://app.sandbox.midtrans.com/snap/snap.js'; %>
    <script type="text/javascript"
      src="<%= snapUrl %>"
      data-client-key="<%= midtransClientKey %>"
      onload="onSnapLoaded()"
      onerror="onSnapError()"></script>
    <% } %>

    <script>
        let debugLog = '';
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog += `[${timestamp}] ${message}\n`;
            document.getElementById('debugLog').textContent = debugLog;
            // console.log(message); // Cleaned for production
        }

        function clearLog() {
            debugLog = '';
            document.getElementById('debugLog').textContent = '';
        }

        function onSnapLoaded() {
            log('✅ Midtrans Snap script loaded successfully');
            checkSnapAvailability();
        }

        function onSnapError() {
            log('❌ Failed to load Midtrans Snap script');
            document.getElementById('scriptStatus').textContent = '❌ Failed to load';
            document.getElementById('scriptStatus').parentElement.className = 'test-section error';
        }

        function checkSnapAvailability() {
            const snapAvailable = typeof snap !== 'undefined';
            document.getElementById('snapObject').textContent = snapAvailable ? '✅ Available' : '❌ Not Available';
            document.getElementById('scriptStatus').textContent = snapAvailable ? '✅ Loaded' : '❌ Not Loaded';
            
            if (snapAvailable) {
                document.getElementById('scriptStatus').parentElement.className = 'test-section success';
                log('✅ Midtrans Snap object is available');
            } else {
                document.getElementById('scriptStatus').parentElement.className = 'test-section error';
                log('❌ Midtrans Snap object is not available');
            }
        }

        async function testPayment() {
            const btn = document.getElementById('testBtn');
            btn.disabled = true;
            btn.textContent = 'Creating payment...';
            
            try {
                log('🔄 Starting test payment...');
                
                const response = await fetch('/payment/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        planId: '04903f71-0f66-475d-94ed-dc45f7fc29b8' // Basic plan ID
                    })
                });

                log(`📡 Response status: ${response.status}`);
                const result = await response.json();
                log(`📡 Response data: ${JSON.stringify(result, null, 2)}`);

                if (result.success) {
                    log('✅ Payment creation successful');
                    
                    if (typeof snap !== 'undefined') {
                        log('🎫 Opening Midtrans Snap popup...');
                        snap.pay(result.snap_token, {
                            onSuccess: function(result) {
                                log('✅ Payment success: ' + JSON.stringify(result));
                                alert('Payment successful!');
                            },
                            onPending: function(result) {
                                log('⏳ Payment pending: ' + JSON.stringify(result));
                                alert('Payment pending');
                            },
                            onError: function(result) {
                                log('❌ Payment error: ' + JSON.stringify(result));
                                alert('Payment error');
                            },
                            onClose: function() {
                                log('🚪 Payment popup closed');
                            }
                        });
                    } else {
                        log('⚠️ Snap not available, opening redirect URL');
                        window.open(result.redirect_url, '_blank');
                    }
                } else {
                    log('❌ Payment creation failed: ' + result.error);
                    alert('Payment creation failed: ' + result.error);
                }
            } catch (error) {
                log('❌ Error: ' + error.message);
                alert('Error: ' + error.message);
            } finally {
                btn.disabled = false;
                btn.textContent = 'Test Payment (Rp150,000)';
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            log('🔍 Page loaded, checking environment...');
            
            // Check client key
            const clientKey = '<%= typeof midtransClientKey !== "undefined" ? midtransClientKey : "Not Available" %>';
            document.getElementById('clientKey').textContent = clientKey !== 'Not Available' ? '✅ Available' : '❌ Not Available';
            
            // Check environment
            const isProduction = '<%= process.env.MIDTRANS_IS_PRODUCTION %>' === 'true';
            document.getElementById('environment').textContent = isProduction ? '🔴 Production' : '🟡 Sandbox';
            
            log(`Client Key: ${clientKey}`);
            log(`Environment: ${isProduction ? 'Production' : 'Sandbox'}`);
            
            // Check snap availability after a short delay
            setTimeout(checkSnapAvailability, 1000);
        });
    </script>
</body>
</html>
