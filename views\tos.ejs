<% layout('layout') -%>

<!-- Terms of Service Page -->
<div class="min-h-screen bg-dark-900 text-white">
  <!-- Header -->
  <div class="bg-gradient-to-r from-primary/20 to-primary/10 border-b border-gray-700">
    <div class="max-w-4xl mx-auto px-4 py-8">
      <div class="text-center">
        <h1 class="text-3xl md:text-4xl font-bold mb-2">
          <%= t('tos.title') %>
        </h1>
        <p class="text-gray-300 text-lg">
          <%= t('tos.subtitle') %>
        </p>
      </div>
    </div>
  </div>

  <!-- Content -->
  <div class="max-w-4xl mx-auto px-4 py-8">
    <!-- Introduction -->
    <div class="bg-dark-800 rounded-lg p-6 mb-8 border border-gray-700">
      <p class="text-gray-300 leading-relaxed">
        <%= t('tos.intro') %>
      </p>
    </div>

    <!-- Terms Sections -->
    <div class="space-y-8">
      <!-- Section 1: Definitions -->
      <section class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <h2 class="text-xl font-semibold mb-4 text-primary flex items-center">
          <span class="bg-primary/20 text-primary rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">1</span>
          <%= t('tos.section_1.title') %>
        </h2>
        <div class="space-y-3 text-gray-300">
          <div class="pl-4 border-l-2 border-primary/30">
            <p><strong>StreamOnPod:</strong> <%= t('tos.section_1.content.streamonpod').replace('StreamOnPod: ', '') %></p>
          </div>
          <div class="pl-4 border-l-2 border-primary/30">
            <p><strong><%= t('tos.section_1.content.user').split(':')[0] %>:</strong> <%= t('tos.section_1.content.user').split(': ')[1] %></p>
          </div>
          <div class="pl-4 border-l-2 border-primary/30">
            <p><strong><%= t('tos.section_1.content.service').split(':')[0] %>:</strong> <%= t('tos.section_1.content.service').split(': ')[1] %></p>
          </div>
        </div>
      </section>

      <!-- Section 2: General Terms -->
      <section class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <h2 class="text-xl font-semibold mb-4 text-primary flex items-center">
          <span class="bg-primary/20 text-primary rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">2</span>
          <%= t('tos.section_2.title') %>
        </h2>
        <ul class="space-y-3 text-gray-300">
          <% t('tos.section_2.content').forEach(function(item) { %>
          <li class="flex items-start">
            <i class="ti ti-check text-primary mt-1 mr-3 flex-shrink-0"></i>
            <span><%= item %></span>
          </li>
          <% }); %>
        </ul>
      </section>

      <!-- Section 3: Service Scope -->
      <section class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <h2 class="text-xl font-semibold mb-4 text-primary flex items-center">
          <span class="bg-primary/20 text-primary rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">3</span>
          <%= t('tos.section_3.title') %>
        </h2>
        <div class="space-y-3 text-gray-300">
          <% t('tos.section_3.content').forEach(function(item, index) { %>
            <% if (index === 0) { %>
              <p><%= item %></p>
            <% } else if (index === 1) { %>
              <p class="font-medium mt-4"><%= item %></p>
            <% } else { %>
              <div class="flex items-start ml-4">
                <i class="ti ti-point text-primary mt-1 mr-2 flex-shrink-0"></i>
                <span><%= item.replace('• ', '') %></span>
              </div>
            <% } %>
          <% }); %>
        </div>
      </section>

      <!-- Section 4: Ownership and Copyright -->
      <section class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <h2 class="text-xl font-semibold mb-4 text-primary flex items-center">
          <span class="bg-primary/20 text-primary rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">4</span>
          <%= t('tos.section_4.title') %>
        </h2>
        <ul class="space-y-3 text-gray-300">
          <% t('tos.section_4.content').forEach(function(item) { %>
          <li class="flex items-start">
            <i class="ti ti-check text-primary mt-1 mr-3 flex-shrink-0"></i>
            <span><%= item %></span>
          </li>
          <% }); %>
        </ul>
      </section>

      <!-- Section 5: User Responsibilities -->
      <section class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <h2 class="text-xl font-semibold mb-4 text-primary flex items-center">
          <span class="bg-primary/20 text-primary rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">5</span>
          <%= t('tos.section_5.title') %>
        </h2>
        <ul class="space-y-3 text-gray-300">
          <% t('tos.section_5.content').forEach(function(item) { %>
          <li class="flex items-start">
            <i class="ti ti-alert-triangle text-yellow-500 mt-1 mr-3 flex-shrink-0"></i>
            <span><%= item %></span>
          </li>
          <% }); %>
        </ul>
      </section>

      <!-- Section 6: Payment and Refund Policy -->
      <section class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <h2 class="text-xl font-semibold mb-4 text-primary flex items-center">
          <span class="bg-primary/20 text-primary rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">6</span>
          <%= t('tos.section_6.title') %>
        </h2>
        <ul class="space-y-3 text-gray-300">
          <% t('tos.section_6.content').forEach(function(item) { %>
          <li class="flex items-start">
            <i class="ti ti-credit-card text-green-500 mt-1 mr-3 flex-shrink-0"></i>
            <span><%= item %></span>
          </li>
          <% }); %>
        </ul>
      </section>

      <!-- Section 7: Limitation of Liability -->
      <section class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <h2 class="text-xl font-semibold mb-4 text-primary flex items-center">
          <span class="bg-primary/20 text-primary rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">7</span>
          <%= t('tos.section_7.title') %>
        </h2>
        <ul class="space-y-3 text-gray-300">
          <% t('tos.section_7.content').forEach(function(item) { %>
          <li class="flex items-start">
            <i class="ti ti-shield-x text-red-500 mt-1 mr-3 flex-shrink-0"></i>
            <span><%= item %></span>
          </li>
          <% }); %>
        </ul>
      </section>

      <!-- Section 8: Privacy -->
      <section class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <h2 class="text-xl font-semibold mb-4 text-primary flex items-center">
          <span class="bg-primary/20 text-primary rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">8</span>
          <%= t('tos.section_8.title') %>
        </h2>
        <ul class="space-y-3 text-gray-300">
          <% t('tos.section_8.content').forEach(function(item) { %>
          <li class="flex items-start">
            <i class="ti ti-lock text-blue-500 mt-1 mr-3 flex-shrink-0"></i>
            <span><%= item %></span>
          </li>
          <% }); %>
        </ul>
      </section>

      <!-- Section 9: Force Majeure -->
      <section class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <h2 class="text-xl font-semibold mb-4 text-primary flex items-center">
          <span class="bg-primary/20 text-primary rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">9</span>
          <%= t('tos.section_9.title') %>
        </h2>
        <div class="text-gray-300">
          <% t('tos.section_9.content').forEach(function(item) { %>
          <div class="flex items-start">
            <i class="ti ti-alert-circle text-orange-500 mt-1 mr-3 flex-shrink-0"></i>
            <span><%= item %></span>
          </div>
          <% }); %>
        </div>
      </section>

      <!-- Section 10: Applicable Law -->
      <section class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <h2 class="text-xl font-semibold mb-4 text-primary flex items-center">
          <span class="bg-primary/20 text-primary rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">10</span>
          <%= t('tos.section_10.title') %>
        </h2>
        <ul class="space-y-3 text-gray-300">
          <% t('tos.section_10.content').forEach(function(item) { %>
          <li class="flex items-start">
            <i class="ti ti-scale text-purple-500 mt-1 mr-3 flex-shrink-0"></i>
            <span><%= item %></span>
          </li>
          <% }); %>
        </ul>
      </section>

      <!-- Section 11: Conclusion -->
      <section class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <h2 class="text-xl font-semibold mb-4 text-primary flex items-center">
          <span class="bg-primary/20 text-primary rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">11</span>
          <%= t('tos.section_11.title') %>
        </h2>
        <ul class="space-y-3 text-gray-300">
          <% t('tos.section_11.content').forEach(function(item) { %>
          <li class="flex items-start">
            <i class="ti ti-check-circle text-green-500 mt-1 mr-3 flex-shrink-0"></i>
            <span><%= item %></span>
          </li>
          <% }); %>
        </ul>
      </section>
    </div>

    <!-- Contact Section -->
    <div class="bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg p-6 mt-8 border border-primary/20">
      <h2 class="text-xl font-semibold mb-4 text-primary">
        <%= t('tos.contact.title') %>
      </h2>
      <p class="text-gray-300 mb-4">
        <%= t('tos.contact.description') %>
      </p>
      <div class="flex flex-col sm:flex-row gap-4">
        <a href="mailto:<EMAIL>" 
           class="flex items-center text-primary hover:text-primary/80 transition-colors">
          <i class="ti ti-mail mr-2"></i>
          <%= t('tos.contact.email') %>
        </a>
        <a href="https://t.me/streamonpod_support" 
           target="_blank"
           class="flex items-center text-primary hover:text-primary/80 transition-colors">
          <i class="ti ti-brand-telegram mr-2"></i>
          <%= t('tos.contact.telegram') %>
        </a>
      </div>
    </div>

    <!-- Back to Home -->
    <div class="text-center mt-8">
      <a href="/" 
         class="inline-flex items-center px-6 py-3 bg-primary hover:bg-primary/90 text-white rounded-lg transition-colors">
        <i class="ti ti-arrow-left mr-2"></i>
        <%= t('common.back') %> to Home
      </a>
    </div>
  </div>
</div>
